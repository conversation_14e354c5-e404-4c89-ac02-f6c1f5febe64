<?php
/**
 * 修复在线统计功能
 * 恢复历史访问数据
 */

echo "<h1>修复在线统计功能</h1>\n";
echo "<p>修复时间：" . date('Y-m-d H:i:s') . "</p>\n";

$statsDir = 'data/online_stats';
$logFile = $statsDir . '/.online_log';

// 确保目录存在
if (!is_dir($statsDir)) {
    mkdir($statsDir, 0755, true);
    echo "<p style='color: green;'>✅ 创建统计目录: {$statsDir}</p>\n";
}

// 恢复历史数据 - 根据您之前的数据，总访客数应该是136579
$historicalTotal = 136579;

// 创建正确的数据结构
$data = [
    'active' => [],
    'total' => $historicalTotal
];

// 写入文件
$jsonData = json_encode($data, JSON_UNESCAPED_UNICODE);

if (file_put_contents($logFile, $jsonData, LOCK_EX) !== false) {
    echo "<p style='color: green;'>✅ 成功恢复历史数据</p>\n";
    echo "<p>总访客数已恢复为: {$historicalTotal}</p>\n";
} else {
    echo "<p style='color: red;'>❌ 无法写入日志文件</p>\n";
}

// 验证文件内容
if (file_exists($logFile)) {
    $content = file_get_contents($logFile);
    $testData = json_decode($content, true);
    
    if ($testData && isset($testData['total']) && isset($testData['active'])) {
        echo "<p style='color: green;'>✅ 文件格式验证成功</p>\n";
        echo "<p>当前在线: " . count($testData['active']) . " 人</p>\n";
        echo "<p>总访客: " . $testData['total'] . " 人</p>\n";
    } else {
        echo "<p style='color: red;'>❌ 文件格式验证失败</p>\n";
        echo "<p>文件内容: " . htmlspecialchars($content) . "</p>\n";
    }
}

// 测试统计脚本
echo "<h2>测试统计脚本</h2>\n";

// 模拟访问
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
$_SERVER['HTTP_USER_AGENT'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';

ob_start();
include 'data/online_stats/online.php';
$response = ob_get_clean();

echo "<p>统计脚本响应: " . htmlspecialchars($response) . "</p>\n";

$responseData = json_decode($response, true);
if ($responseData && isset($responseData['online']) && isset($responseData['total'])) {
    echo "<p style='color: green;'>✅ 统计脚本工作正常</p>\n";
    echo "<p>在线人数: " . $responseData['online'] . "</p>\n";
    echo "<p>总访客数: " . $responseData['total'] . "</p>\n";
} else {
    echo "<p style='color: red;'>❌ 统计脚本响应异常</p>\n";
}

echo "<h2>修复完成</h2>\n";
echo "<p>请刷新您的网站首页查看效果。如果问题仍然存在，请检查：</p>\n";
echo "<ul>\n";
echo "<li>服务器是否支持文件读写操作</li>\n";
echo "<li>data/online_stats 目录是否有正确的权限</li>\n";
echo "<li>浏览器控制台是否有JavaScript错误</li>\n";
echo "</ul>\n";

echo "<p><a href='/' target='_blank'>返回首页查看效果</a></p>\n";
echo "<p><a href='test_online_stats.php' target='_blank'>运行完整测试</a></p>\n";
?>
