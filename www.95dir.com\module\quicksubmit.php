<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = '快速提交网站';
$tempfile = 'quicksubmit.html';
$pageurl = '?mod=quicksubmit';

// 处理表单提交
if ($_POST['act'] == 'submit') {
	// 检查提交功能是否开启
	if (isset($options['is_enabled_submit']) && $options['is_enabled_submit'] != 'yes') {
		$reason = isset($options['submit_close_reason']) ? $options['submit_close_reason'] : '网站提交功能暂时关闭，请稍后再试。';
		echo "<script>alert('" . addslashes($reason) . "'); history.back();</script>";
		exit;
	}
	
	// 获取并清理表单数据
	$cate_id = intval($_POST['cate_id']);
	$web_name = trim($_POST['web_name']);
	$web_url = trim($_POST['web_url']);
	$web_tags = strtolower(addslashes(trim($_POST['web_tags'])));
	$web_intro = addslashes(trim($_POST['web_intro']));
	$web_owner = trim($_POST['web_owner']);
	$web_email = trim($_POST['web_email']);
	$check_code = strtolower(trim($_POST['check_code']));
	
	// 验证码检查
	if (empty($check_code) || $check_code != $_SESSION['code']) {
		echo "<script>alert('验证码错误！请重新输入验证码。'); history.back();</script>";
		exit;
	}
	
	// 数据验证
	if ($cate_id <= 0) {
		echo "<script>alert('请选择网站所属分类！'); history.back();</script>";
		exit;
	}
	
	if (empty($web_name)) {
		echo "<script>alert('请输入网站名称！'); history.back();</script>";
		exit;
	}
	
	// 检查网站名称长度（中文算2个字符）
	$name_length = 0;
	for ($i = 0; $i < mb_strlen($web_name, 'UTF-8'); $i++) {
		$char = mb_substr($web_name, $i, 1, 'UTF-8');
		if (ord($char) > 127) {
			$name_length += 2; // 中文字符算2个字符
		} else {
			$name_length += 1; // 英文字符算1个字符
		}
	}
	
	if ($name_length > 12) {
		echo "<script>alert('网站名称过长！最多12个字符（6个汉字）'); history.back();</script>";
		exit;
	}
	
	if (empty($web_url)) {
		echo "<script>alert('请输入网站域名！'); history.back();</script>";
		exit;
	} else {
		// 清理URL，移除协议前缀
		$web_url = preg_replace('/^https?:\/\//', '', $web_url);
		$web_url = preg_replace('/^www\./', '', $web_url);
		$web_url = rtrim($web_url, '/');
		
		// 域名格式检查
		if (!preg_match('/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/', $web_url)) {
			echo "<script>alert('请输入正确的网站域名！例如：example.com'); history.back();</script>";
			exit;
		}
		
		// 检查域名长度
		if (strlen($web_url) > 100) {
			echo "<script>alert('网站域名过长！'); history.back();</script>";
			exit;
		}
	}
	
	if (empty($web_intro)) {
		echo "<script>alert('请输入网站简介！'); history.back();</script>";
		exit;
	}
	
	// 检查简介长度
	if (mb_strlen($web_intro, 'UTF-8') < 10) {
		echo "<script>alert('网站简介至少需要10个字符！'); history.back();</script>";
		exit;
	}
	
	if (mb_strlen($web_intro, 'UTF-8') > 500) {
		echo "<script>alert('网站简介不能超过500个字符！'); history.back();</script>";
		exit;
	}
	
	if (empty($web_email)) {
		echo "<script>alert('请输入电子邮箱！'); history.back();</script>";
		exit;
	} else {
		if (!filter_var($web_email, FILTER_VALIDATE_EMAIL)) {
			echo "<script>alert('请输入正确的电子邮箱！'); history.back();</script>";
			exit;
		}
	}
	
	// 检查网站是否已存在
	$table = $DB->table('websites');
	$query = $DB->query("SELECT web_id, web_name, web_status, web_ctime FROM $table WHERE web_url='$web_url'");
	if ($DB->num_rows($query)) {
		$existing_web = $DB->fetch_array($query);
		$status_msg = '';
		
		switch($existing_web['web_status']) {
			case 1:
				$status_msg = '该网站已被拉黑，无法重复提交！';
				break;
			case 2:
				$status_msg = '该网站正在审核中，请勿重复提交！';
				break;
			case 3:
				$status_msg = '该网站已收录（收录时间：' . date('Y-m-d', $existing_web['web_ctime']) . '），请勿重复提交！';
				break;
			case 4:
				$status_msg = '该网站审核不通过，请修改后重新提交！';
				break;
			default:
				$status_msg = '该网站已存在，请勿重复提交！';
		}
		
		echo "<script>alert('$status_msg'); history.back();</script>";
		exit;
	}
	
	// 准备数据 - 参照会员提交逻辑
	$web_time = time();
	$web_ip = get_client_ip();

	// 处理TAG标签格式
	if (!empty($web_tags)) {
		$web_tags = str_replace('，', ',', $web_tags);
		$web_tags = str_replace(',,', ',', $web_tags);
		if (substr($web_tags, -1) == ',') {
			$web_tags = substr($web_tags, 0, strlen($web_tags) - 1);
		}
	}

	$web_data = array(
		'cate_id' => $cate_id,
		'user_id' => 0, // 非会员用户ID为0
		'web_name' => $web_name,
		'web_url' => $web_url,
		'web_tags' => $web_tags,
		'web_intro' => $web_intro,
		'web_status' => 2, // 待审核状态
		'web_ctime' => $web_time
	);
	
	// 插入数据
	$DB->insert($table, $web_data);
	$web_id = $DB->insert_id();

	// 插入统计数据 - 参照会员提交逻辑
	$web_ip_numeric = sprintf("%u", ip2long($web_ip));
	$stat_data = array(
		'web_id' => $web_id,
		'web_ip' => $web_ip_numeric,
		'web_grank' => 0,
		'web_brank' => 0,
		'web_srank' => 0,
		'web_arank' => 0,
		'web_utime' => $web_time
	);
	$DB->insert($DB->table('webdata'), $stat_data);

	// 更新分类统计
	$DB->query("UPDATE ".$DB->table('categories')." SET cate_postcount=cate_postcount+1 WHERE cate_id=$cate_id");

	// 更新缓存 - 需要先包含缓存函数文件
	require_once(APP_PATH.'module/static.php');
	update_cache('archives');

	// 发送邮件通知管理员
	if (!empty($options['smtp_host']) && !empty($options['smtp_user']) && !empty($options['admin_email'])) {
		try {
			require_once(APP_PATH.'include/sendmail.php');

			$subject = '[' . $options['site_name'] . '] 有新的网站快速提交需要审核';
			$mailbody = '
			<h2>有新的网站快速提交需要审核</h2>
			<p><strong>网站名称：</strong>' . htmlspecialchars($web_data['web_name']) . '</p>
			<p><strong>网站地址：</strong>' . htmlspecialchars($web_data['web_url']) . '</p>
			<p><strong>网站简介：</strong>' . htmlspecialchars(substr($web_data['web_intro'], 0, 200)) . '</p>
			<p><strong>提交时间：</strong>' . date('Y-m-d H:i:s', $web_data['web_ctime']) . '</p>
			<p><a href="' . $options['site_url'] . 'system/" target="_blank">点击进入后台审核</a></p>
			';

			sendmail($options['admin_email'], $subject, $mailbody);
		} catch (Exception $e) {
			// 静默处理邮件发送异常
		}
	}

	// 发送微信机器人通知
	if (!empty($options['wechat_robot'])) {
		try {
			$messageData = [
				"msgtype" => "text",
				"text" => [
					"content" => "有快速提交网站需要审核\n站点名称：".$web_data['web_name']."\n站点地址：".$web_data['web_url']."\n提交时间：".date('Y-m-d H:i:s',$web_data['web_ctime']),
				]
			];

			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $options['wechat_robot']);
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($messageData));
			curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
			curl_setopt($ch, CURLOPT_TIMEOUT, 5);

			curl_exec($ch);
			curl_close($ch);
		} catch (Exception $e) {
			// 静默处理微信机器人通知异常
		}
	}

	// 发送钉钉机器人通知
	if (!empty($options['dingding_robot'])) {
		try {
			$messageData = [
				"msgtype" => "text",
				"text" => [
					"content" => "有快速提交网站需要审核\n站点名称：".$web_data['web_name']."\n站点地址：".$web_data['web_url']."\n提交时间：".date('Y-m-d H:i:s',$web_data['web_ctime']),
				]
			];

			$webhook_url = $options['dingding_robot'];
			if (!empty($options['dingding_secret'])) {
				$timestamp = round(microtime(true) * 1000);
				$string_to_sign = $timestamp . "\n" . $options['dingding_secret'];
				$sign = base64_encode(hash_hmac('sha256', $string_to_sign, $options['dingding_secret'], true));
				$webhook_url .= "&timestamp=" . $timestamp . "&sign=" . urlencode($sign);
			}

			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $webhook_url);
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($messageData));
			curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
			curl_setopt($ch, CURLOPT_TIMEOUT, 5);

			curl_exec($ch);
			curl_close($ch);
		} catch (Exception $e) {
			// 静默处理钉钉机器人通知异常
		}
	}

	// 成功提交的消息
	$success_msg = "网站提交成功！\n\n";
	$success_msg .= "网站名称：" . $web_name . "\n";
	$success_msg .= "网站域名：" . $web_url . "\n";
	$success_msg .= "提交时间：" . date('Y-m-d H:i:s') . "\n\n";
	$success_msg .= "我们会在1-3个工作日内审核您的网站，请耐心等待。\n";
	$success_msg .= "感谢您的提交！";

	// 清理消息内容，确保JavaScript安全
	$safe_msg = str_replace(array("\r", "\n"), array("", "\\n"), addslashes($success_msg));

	echo "<script>";
	echo "alert('" . $safe_msg . "');";
	echo "window.location.href = '?mod=index';";
	echo "</script>";
	exit;
}

// 获取分类选项
$category_option = get_category_option('webdir', 0);

// 设置模板变量
$smarty->assign('site_title', $pagename.' - '.$options['site_name']);
$smarty->assign('site_keywords', '快速提交网站,网站收录,免费收录');
$smarty->assign('site_description', '快速提交网站到分类目录，无需注册，直接提交。');
$smarty->assign('site_path', '当前位置：<a href="'.$options['site_url'].'">'.$options['site_name'].'</a> &raquo; '.$pagename);
$smarty->assign('pageurl', $pageurl);
$smarty->assign('category_option', $category_option);

// 从配置中获取提交设置
$cfg = array(
	'is_enabled_submit' => isset($options['is_enabled_submit']) ? $options['is_enabled_submit'] : 'yes',
	'submit_close_reason' => isset($options['submit_close_reason']) ? $options['submit_close_reason'] : '网站提交功能暂时关闭，请稍后再试。'
);
$smarty->assign('cfg', $cfg);

smarty_output($tempfile);
?>
