<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排行榜组件测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 320px;
            margin: 0 auto;
        }
        
        /* ==================== 热门排行榜样式 ==================== */
        #toprank-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            overflow: hidden;
            margin-bottom: 15px;
        }

        #toprank-box h3 {
            background: linear-gradient(90deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
            color: #fff;
            font-size: 16px;
            font-weight: bold;
            padding: 12px 15px;
            margin: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .toprank-title-span {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .toprank-title-span .icon-gold {
            color: #ffd700;
            font-size: 18px;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .more-link {
            color: rgba(255,255,255,0.9) !important;
            font-size: 12px;
            text-decoration: none;
            padding: 4px 8px;
            border-radius: 15px;
            background: rgba(255,255,255,0.1);
            transition: all 0.3s ease;
        }

        .more-link:hover {
            background: rgba(255,255,255,0.2);
            color: #fff !important;
        }

        .toprank-container {
            padding: 15px;
            background: rgba(255,255,255,0.05);
        }

        .rank-section {
            margin-bottom: 20px;
        }

        .rank-section:last-child {
            margin-bottom: 0;
        }

        .rank-section-title {
            color: #fff;
            font-size: 14px;
            font-weight: bold;
            margin: 0 0 10px 0;
            display: flex;
            align-items: center;
            gap: 6px;
            padding-bottom: 5px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .rank-icon {
            font-size: 14px;
            color: #ff6b6b;
        }

        .vip-icon {
            color: #ffd700 !important;
        }

        .rank-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .rank-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            transition: all 0.3s ease;
        }

        .rank-item:last-child {
            border-bottom: none;
        }

        .rank-item:hover {
            background: rgba(255,255,255,0.1);
            border-radius: 6px;
            padding-left: 5px;
        }

        .rank-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            font-weight: bold;
            font-size: 12px;
            margin-right: 10px;
            flex-shrink: 0;
        }

        /* 排名数字颜色 */
        .rank-1 .rank-number {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: #fff;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
        }

        .rank-2 .rank-number {
            background: linear-gradient(135deg, #ffa726, #ff9800);
            color: #fff;
            box-shadow: 0 2px 8px rgba(255, 167, 38, 0.4);
        }

        .rank-3 .rank-number {
            background: linear-gradient(135deg, #66bb6a, #4caf50);
            color: #fff;
            box-shadow: 0 2px 8px rgba(102, 187, 106, 0.4);
        }

        .rank-4 .rank-number,
        .rank-5 .rank-number {
            background: linear-gradient(135deg, #78909c, #607d8b);
            color: #fff;
            box-shadow: 0 2px 8px rgba(120, 144, 156, 0.4);
        }

        .vip-number {
            background: linear-gradient(135deg, #ffd700, #ffb300) !important;
            color: #333 !important;
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.5) !important;
        }

        .rank-content {
            flex: 1;
            min-width: 0;
        }

        .rank-name {
            color: #fff !important;
            font-size: 13px;
            font-weight: 500;
            text-decoration: none;
            display: block;
            margin-bottom: 3px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: color 0.3s ease;
        }

        .rank-name:hover {
            color: #ffd700 !important;
        }

        .vip-name {
            color: #ffd700 !important;
        }

        .vip-name:hover {
            color: #fff !important;
        }

        .rank-stats {
            display: flex;
            gap: 10px;
            font-size: 11px;
            color: rgba(255,255,255,0.7);
        }

        .rank-views,
        .rank-outstat {
            display: flex;
            align-items: center;
            gap: 3px;
        }

        .rank-views i,
        .rank-outstat i {
            font-size: 10px;
        }

        .vip-badge {
            background: linear-gradient(135deg, #ffd700, #ffb300);
            color: #333;
            font-size: 9px;
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 10px;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 热门排行榜 -->
        <div id="toprank-box" class="clearfix">
            <h3>
                <span class="toprank-title-span">
                    <i class="fas fa-trophy icon-gold" aria-hidden="true"></i>热门排行榜
                </span>
                <a href="?mod=top" class="more-link" title="查看完整排行榜">更多 &raquo;</a>
            </h3>
            <div class="toprank-container">
                <!-- 综合排行榜 TOP5 -->
                <div class="rank-section">
                    <h4 class="rank-section-title">
                        <i class="fas fa-fire rank-icon"></i>综合热门 TOP5
                    </h4>
                    <ul class="rank-list">
                        <li class="rank-item rank-1">
                            <span class="rank-number">1</span>
                            <div class="rank-content">
                                <a href="#" title="百度" class="rank-name">百度</a>
                                <div class="rank-stats">
                                    <span class="rank-views">
                                        <i class="fas fa-eye"></i>12580
                                    </span>
                                    <span class="rank-outstat">
                                        <i class="fas fa-external-link-alt"></i>8965
                                    </span>
                                </div>
                            </div>
                        </li>
                        <li class="rank-item rank-2">
                            <span class="rank-number">2</span>
                            <div class="rank-content">
                                <a href="#" title="腾讯" class="rank-name">腾讯</a>
                                <div class="rank-stats">
                                    <span class="rank-views">
                                        <i class="fas fa-eye"></i>9876
                                    </span>
                                    <span class="rank-outstat">
                                        <i class="fas fa-external-link-alt"></i>7654
                                    </span>
                                </div>
                            </div>
                        </li>
                        <li class="rank-item rank-3">
                            <span class="rank-number">3</span>
                            <div class="rank-content">
                                <a href="#" title="阿里巴巴" class="rank-name">阿里巴巴</a>
                                <div class="rank-stats">
                                    <span class="rank-views">
                                        <i class="fas fa-eye"></i>8765
                                    </span>
                                    <span class="rank-outstat">
                                        <i class="fas fa-external-link-alt"></i>6543
                                    </span>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>

                <!-- VIP特权排行榜 TOP3 -->
                <div class="rank-section">
                    <h4 class="rank-section-title">
                        <i class="fas fa-crown rank-icon vip-icon"></i>VIP特权 TOP3
                    </h4>
                    <ul class="rank-list vip-rank">
                        <li class="rank-item vip-item rank-1">
                            <span class="rank-number vip-number">1</span>
                            <div class="rank-content">
                                <a href="#" title="VIP网站1" class="rank-name vip-name">VIP网站1</a>
                                <div class="rank-stats">
                                    <span class="rank-views">
                                        <i class="fas fa-eye"></i>5432
                                    </span>
                                    <span class="vip-badge">VIP</span>
                                </div>
                            </div>
                        </li>
                        <li class="rank-item vip-item rank-2">
                            <span class="rank-number vip-number">2</span>
                            <div class="rank-content">
                                <a href="#" title="VIP网站2" class="rank-name vip-name">VIP网站2</a>
                                <div class="rank-stats">
                                    <span class="rank-views">
                                        <i class="fas fa-eye"></i>4321
                                    </span>
                                    <span class="vip-badge">VIP</span>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
