<?php
/*
 * <AUTHOR>  　 @祥💥　技术支持
 * @Mail         : <EMAIL>
 * @Date         : 2025-02-12 15:22:31
 * @LastEditTime : 2025-02-18 09:01:04
 * @LastEditors  :  　 @祥💥　技术支持
 * @Description  : 
 * @FilePath     : \35dir\module\getdata.php
 * It's up to you ^_^
 * Copyright (c) 2025 by <EMAIL>, All Rights Reserved. 
 */
if (!defined('IN_IWEBDIR')) exit('Access Denied');

require(APP_PATH.'module/webdata.php');

$type = trim($_GET['type']);
$web_id = intval($_GET['wid']);

if (in_array($type, array('ip', 'grank', 'brank', 'srank', 'arank', 'clink', 'outstat', 'addfav', 'rate', 'error'))) {
	$where = "w.web_id=$web_id";
	$web = get_one_website($where);
	if (!$web) {
		exit();
	}
	
	$update_cycle = time() + (3600 * 24 * $options['data_update_cycle']);
	$update_time = time();
	if ($web['web_utime'] < $update_cycle) {
		$DB->query("UPDATE ".$DB->table('webdata')." SET web_utime='$update_time' WHERE web_id=".$web['web_id']);
		#server ip
		if ($type == 'ip') {
			$ip = get_serverip($web['web_url']);
			$ip = sprintf("%u", ip2long($ip));
			$DB->query("UPDATE ".$DB->table('webdata')." SET web_ip='$ip' WHERE web_id=".$web['web_id']);
		}
	
		#google pagerank
		if ($type == 'grank') {
			 $rank = get_pagerank($web['web_url']);
			 $DB->query("UPDATE ".$DB->table('webdata')." SET web_grank='$rank' WHERE web_id=".$web['web_id']);
		}
		
		#baidu pagerank
		if ($type == 'brank') {
			$rank = get_baidurank($web['web_url']);
			$DB->query("UPDATE ".$DB->table('webdata')." SET web_brank='$rank' WHERE web_id=".$web['web_id']);
		}
		
		#sogou pagerank
		if ($type == 'srank') {
			$rank = get_sogourank($web['web_url']);
			$DB->query("UPDATE ".$DB->table('webdata')." SET web_srank='$rank' WHERE web_id=".$web['web_id']);
		}
		
		#alexa rank
		if ($type == 'arank') {
			$rank = get_alexarank($web['web_url']);
			$DB->query("UPDATE ".$DB->table('webdata')." SET web_arank='$rank' WHERE web_id=".$web['web_id']);
		}
		
		#check link
		if ($type == 'clink') {
			if ($web['web_ispay'] == 0) {
				$link_result = smart_check_website_link($web['web_url'], $options);

				if ($link_result['has_link']) {
					$DB->query("UPDATE ".$DB->table('websites')." SET web_islink=0 WHERE web_id=".$web['web_id']);
				} else {
					$DB->query("UPDATE ".$DB->table('websites')." SET web_islink=1 WHERE web_id=".$web['web_id']);
				}
			}
		}
	}
	
	#outstat
	if ($type == 'outstat') {
		$DB->query("UPDATE ".$DB->table('webdata')." SET web_outstat=web_outstat+1, web_otime=".time()." WHERE web_id=".$web['web_id']);
	}
	
	
	
	
	#favorite
	if ($type == 'addfav') {
		/** check login  */
		$auth_cookie = $_COOKIE['auth_cookie'];
		$myself = check_user_login($auth_cookie);
		if (empty($myself)) {
			exit('<script type="text/javascript">alert("您还未登录或未注册！"); window.location.href = "'.$options['site_root'].'member/?mod=login";</script>');
		}
		
		$query = $DB->query("SELECT web_id FROM ".$DB->table('favorites')." WHERE user_id=$myself[user_id] AND web_id=$web_id");
    	if ($DB->num_rows($query)) {
        	exit('<script type="text/javascript">alert("您已收藏过此网站！")</script>');
    	}
		$DB->free_result($query);
		
		/** insert */
		$DB->insert($DB->table('favorites'), array('user_id' => $myself['user_id'], 'web_id' => $web_id, 'fav_time' => time()));
		/** count */
		$count = $DB->get_count($DB->table('favorites'), array('web_id' => $web_id));
		$DB->update($DB->table('webdata'), array('web_fnum' => $count));
		exit('<script type="text/javascript">alert("网站收藏成功！")</script>');
	}
	
	#rate
	if ($type == 'rate') {
	/*	$do = trim($_GET['do']);
		if ($do == 'init') {
			$sql = "SELECT rate_score, rate_count FROM ".$DB->table('webdata')." WHERE web_id=".$web['web_id'];
			$row = $DB->fetch_one($sql);
			if ($row) {
				$score = @round($row['rate_score'] / $row['rate_count'], 2);
				echo "{\"rate_count\":\"".$row["rate_count"]."\", \"rate_score\":\"".$rate_score."\"}";
				//echo $score;
			}
		}
		
		if ($do == 'rate') {
			$num = intval($_GET['value']);
			if ($_COOKIE["rate_wid_".$web['web_id']] <> 1) {
				setcookie("rate_wid_".$web['web_id'], 1, time() + 3600);
				$DB->query("UPDATE ".$DB->table('webdata')." SET rate_score=rate_score+$num, rate_count=rate_count+1 WHERE web_id=".$web['web_id']);
				
				$sql = "SELECT rate_score, rate_count FROM ".$DB->table('webdata')." WHERE web_id=".$web['web_id'];
				$res = $DB->fetch_one($sql);
				$score = @round($res['rate_score'] / $res['rate_count'], 2);
				echo $score;
			}
		}
		*/
	}
	
	#error
	if ($type == 'error') {
		$DB->query("UPDATE ".$DB->table('webdata')." SET web_errors=web_errors+1, web_utime=".time()." WHERE web_id=".$web['web_id']);
	}
}

/**
 * 智能友情链接检测函数
 * @param string $url 要检测的网站URL
 * @param array $options 系统配置选项
 * @return array 检测结果
 */
function smart_check_website_link($url, $options) {
    // 完全复制后台 smart_check_website_link_direct 函数的逻辑，确保一致性
    $result = array(
        'has_link' => false,
        'details' => '',
        'patterns_found' => array()
    );

    try {
        // 获取网站内容
        $content = get_url_content($url);

        if (empty($content)) {
            $result['details'] = '无法获取网站内容';
            return $result;
        }

        $check_domain = $options['check_link_url']; // 95dir.com
        $check_name = $options['check_link_name'];   // 95分类目录

        // 修复后的检测模式 - 更严格的友情链接检测
        $patterns = array(
            'exact_match' => array(
                'pattern' => '/<a[^>]*href=["\']?https?:\/\/(www\.)?'.preg_quote($check_domain, '/').'[\/]?["\']?[^>]*>.*?'.preg_quote($check_name, '/').'.*?<\/a>/i',
                'weight' => 100,
                'desc' => '精确匹配（域名+名称）'
            ),
            'domain_with_text' => array(
                'pattern' => '/<a[^>]*href=["\']?https?:\/\/(www\.)?'.preg_quote($check_domain, '/').'[\/]?["\']?[^>]*>[^<]*'.preg_quote($check_name, '/').'[^<]*<\/a>/i',
                'weight' => 95,
                'desc' => '域名+文本匹配'
            ),
            'friendlink_section_domain' => array(
                'pattern' => '/(?:友情链接|友链|合作伙伴|推荐网站|相关链接|links?).{0,500}<a[^>]*href=["\']?https?:\/\/(www\.)?'.preg_quote($check_domain, '/').'[\/]?["\']?[^>]*>/i',
                'weight' => 85,
                'desc' => '友情链接区域的域名'
            ),
            'domain_with_site_text' => array(
                'pattern' => '/<a[^>]*href=["\']?https?:\/\/(www\.)?'.preg_quote($check_domain, '/').'[\/]?["\']?[^>]*>[^<]*(?:网站|站点|目录|导航|分类)[^<]*<\/a>/i',
                'weight' => 80,
                'desc' => '域名+网站相关文本'
            ),
            'domain_only_strict' => array(
                'pattern' => '/<a[^>]*href=["\']?https?:\/\/(www\.)?'.preg_quote($check_domain, '/').'[\/]?["\']?[^>]*>(?!.*(?:广告|AD|推广|赞助))[^<]{1,30}<\/a>/i',
                'weight' => 70,
                'desc' => '严格域名匹配（排除广告）'
            ),
            'text_with_domain_verification' => array(
                'pattern' => '/<a[^>]*href=["\'][^"\']*'.preg_quote($check_domain, '/').'[^"\']*["\'][^>]*>.*?'.preg_quote($check_name, '/').'.*?<\/a>/i',
                'weight' => 90,
                'desc' => '文本+域名验证'
            )
        );

        $max_weight = 0;
        $found_patterns = array();

        foreach ($patterns as $key => $pattern_info) {
            if (preg_match($pattern_info['pattern'], $content, $matches)) {
                $found_patterns[] = $pattern_info['desc'];
                if ($pattern_info['weight'] > $max_weight) {
                    $max_weight = $pattern_info['weight'];
                }
            }
        }

        // 提高判断阈值到85，减少误判
        if ($max_weight >= 85) {
            $result['has_link'] = true;
            $result['details'] = '找到友情链接：' . implode('、', $found_patterns) . "（权重：$max_weight）";
        } else if ($max_weight >= 70) {
            $result['has_link'] = false;
            $result['details'] = '可能的链接：' . implode('、', $found_patterns) . "（权重不足：$max_weight < 85）";
        } else {
            $result['has_link'] = false;
            $result['details'] = '未找到友情链接';
        }

        $result['patterns_found'] = $found_patterns;

    } catch (Exception $e) {
        $result['details'] = '检测出错：' . $e->getMessage();
    }

    return $result;
}
?>