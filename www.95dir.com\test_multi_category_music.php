<?php
// 测试多分类音乐链接获取功能
define('IN_IWEBDIR', true);
define('APP_PATH', './source/');

require('./source/init.php');
require('./source/module/article.php');

echo "<h2>多分类音乐链接获取测试</h2>\n";

// 定义音乐分类
$music_categories = array(
    317 => '音乐试听专区',
    318 => '流行歌曲', 
    319 => 'DJ串烧'
);

echo "<h3>1. 检查各音乐分类状态</h3>\n";
foreach ($music_categories as $cate_id => $cate_name) {
    echo "<h4>分类 $cate_id: $cate_name</h4>\n";
    
    // 检查分类下的文章总数
    $total_sql = "SELECT COUNT(*) as total FROM ".$DB->table('articles')." WHERE cate_id=$cate_id";
    $total_result = $DB->query($total_sql);
    $total_row = $DB->fetch_array($total_result);
    echo "总文章数: {$total_row['total']}<br>\n";
    
    // 检查包含音乐内容的文章数
    $music_sql = "SELECT COUNT(*) as total FROM ".$DB->table('articles')." WHERE cate_id=$cate_id AND (art_content LIKE '%.mp3%' OR art_content LIKE '%music.163.com%' OR art_content LIKE '%y.qq.com%' OR art_content LIKE '%kugou.com%' OR art_content LIKE '%audio%' OR art_content LIKE '%音乐%' OR art_content LIKE '%歌曲%' OR art_content LIKE '%DJ%' OR art_content LIKE '%串烧%')";
    $music_result = $DB->query($music_sql);
    $music_row = $DB->fetch_array($music_result);
    echo "包含音乐内容的文章数: {$music_row['total']}<br>\n";
    
    // 检查可用的文章数（已审核或管理员待审核）
    $available_sql = "SELECT COUNT(*) as total FROM ".$DB->table('articles')." WHERE cate_id=$cate_id AND (art_content LIKE '%.mp3%' OR art_content LIKE '%music.163.com%' OR art_content LIKE '%y.qq.com%' OR art_content LIKE '%kugou.com%' OR art_content LIKE '%audio%' OR art_content LIKE '%音乐%' OR art_content LIKE '%歌曲%' OR art_content LIKE '%DJ%' OR art_content LIKE '%串烧%') AND (art_status=3 OR (user_id=1 AND art_status=2))";
    $available_result = $DB->query($available_sql);
    $available_row = $DB->fetch_array($available_result);
    echo "可用的音乐文章数: {$available_row['total']}<br>\n";
    
    echo "<br>\n";
}

echo "<h3>2. 测试单个分类音乐链接获取</h3>\n";
foreach ($music_categories as $cate_id => $cate_name) {
    echo "<h4>从分类 $cate_id ($cate_name) 获取音乐链接</h4>\n";
    $links = get_music_links($cate_id, 5);
    
    if (!empty($links)) {
        echo "<ul>\n";
        foreach ($links as $link) {
            echo "<li><strong>" . htmlspecialchars($link['title']) . "</strong><br>";
            echo "URL: " . htmlspecialchars($link['url']) . "<br>";
            echo "分类: " . htmlspecialchars($link['category']) . "</li>\n";
        }
        echo "</ul>\n";
    } else {
        echo "<p style='color: red;'>该分类下没有找到音乐链接</p>\n";
    }
    echo "<br>\n";
}

echo "<h3>3. 测试多分类音乐链接获取</h3>\n";
$all_links = get_music_links(null, 20);
echo "<p>从所有音乐分类获取到的链接数量: " . count($all_links) . "</p>\n";

if (!empty($all_links)) {
    echo "<ul>\n";
    foreach ($all_links as $link) {
        echo "<li><strong>" . htmlspecialchars($link['title']) . "</strong><br>";
        echo "URL: " . htmlspecialchars($link['url']) . "<br>";
        echo "分类: " . htmlspecialchars($link['category']) . "<br>";
        echo "状态: " . ($link['status'] == 3 ? '已审核' : '待审核') . "</li>\n";
    }
    echo "</ul>\n";
} else {
    echo "<p style='color: red;'>没有找到任何音乐链接</p>\n";
}

echo "<h3>4. 添加测试数据到DJ串烧分类</h3>\n";

// 检查DJ串烧分类是否有数据，如果没有就添加一些
$dj_count_sql = "SELECT COUNT(*) as total FROM ".$DB->table('articles')." WHERE cate_id=319";
$dj_count_result = $DB->query($dj_count_sql);
$dj_count_row = $DB->fetch_array($dj_count_result);

if ($dj_count_row['total'] == 0) {
    echo "<p>DJ串烧分类下没有文章，正在添加测试数据...</p>\n";
    
    $dj_songs = array(
        array(
            'title' => 'DJ串烧1 - 嗨翻天电音',
            'content' => '这是一首超嗨的DJ串烧。<br><audio controls><source src="https://music.163.com/song/media/outer/url?id=400001" type="audio/mpeg"></audio><br>DJ串烧，让你嗨翻天！'
        ),
        array(
            'title' => 'DJ串烧2 - 夜店必备',
            'content' => '夜店专用DJ串烧。<br>MP3下载: https://dj-music.example.com/nightclub-mix.mp3<br>这首DJ串烧绝对让你燃起来！'
        ),
        array(
            'title' => 'DJ串烧3 - 车载音乐',
            'content' => '适合开车听的DJ串烧。<br>酷狗音乐: https://www.kugou.com/dj/car-music.html<br><audio src="https://example.com/car-dj.mp3" controls></audio>'
        )
    );
    
    $success_count = 0;
    foreach ($dj_songs as $song) {
        $title = $DB->escape_string($song['title']);
        $content = $DB->escape_string($song['content']);
        $intro = $DB->escape_string(substr(strip_tags($song['content']), 0, 100));
        
        $sql = "INSERT INTO ".$DB->table('articles')." 
                (user_id, cate_id, art_title, art_tags, art_intro, art_content, art_status, art_ctime, art_utime) 
                VALUES 
                (1, 319, '$title', 'DJ,串烧,电音,测试', '$intro', '$content', 3, ".time().", ".time().")";
        
        if ($DB->query($sql)) {
            echo "<p style='color: green;'>✓ 添加成功: {$song['title']}</p>\n";
            $success_count++;
        } else {
            echo "<p style='color: red;'>✗ 添加失败: {$song['title']}</p>\n";
        }
    }
    
    echo "<p>成功添加 $success_count 首DJ串烧测试数据</p>\n";
    
    // 重新测试
    echo "<h4>重新测试DJ串烧分类</h4>\n";
    $dj_links = get_music_links(319, 10);
    if (!empty($dj_links)) {
        echo "<ul>\n";
        foreach ($dj_links as $link) {
            echo "<li><strong>" . htmlspecialchars($link['title']) . "</strong><br>";
            echo "URL: " . htmlspecialchars($link['url']) . "</li>\n";
        }
        echo "</ul>\n";
    }
} else {
    echo "<p>DJ串烧分类下已有 {$dj_count_row['total']} 篇文章</p>\n";
}

echo "<h3>5. 测试AJAX接口</h3>\n";
echo "<p><a href='?mod=ajaxget&type=music_list' target='_blank'>点击测试多分类音乐列表API</a></p>\n";

echo "<h3>完成</h3>\n";
echo "<p style='color: green; font-weight: bold;'>多分类音乐链接获取功能测试完成！</p>\n";
?>
