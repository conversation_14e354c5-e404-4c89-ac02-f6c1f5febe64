<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线统计测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .stats { background: #f0f0f0; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .stats h3 { margin-top: 0; color: #333; }
        .number { font-size: 24px; font-weight: bold; color: #007bff; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>95目录网 - 在线统计测试</h1>
    
    <div class="stats">
        <h3>当前统计数据</h3>
        <p>当前在线：<span id="onlineCount" class="number">加载中...</span> 人</p>
        <p>总访客：<span id="totalVisitors" class="number">加载中...</span> 人</p>
    </div>
    
    <div id="status" class="status info">正在加载统计数据...</div>
    
    <div style="margin-top: 30px;">
        <button onclick="updateStats()">手动刷新统计</button>
        <button onclick="testAPI()">测试API响应</button>
        <button onclick="location.href='/'">返回首页</button>
    </div>
    
    <div id="debug" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; font-family: monospace; font-size: 12px;"></div>

    <script>
        let debugInfo = [];
        
        function log(message) {
            debugInfo.push(new Date().toLocaleTimeString() + ': ' + message);
            document.getElementById('debug').innerHTML = debugInfo.join('<br>');
            console.log(message);
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
        }
        
        function updateStats() {
            log('开始更新统计数据...');
            
            const timestamp = new Date().getTime();
            const url = '/data/online_stats/online.php?t=' + timestamp;
            
            fetch(url, {
                method: 'GET',
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
            })
            .then(response => {
                log('收到响应，状态码: ' + response.status);
                if (!response.ok) {
                    throw new Error('HTTP ' + response.status);
                }
                return response.text();
            })
            .then(text => {
                log('响应内容: ' + text);
                
                try {
                    const data = JSON.parse(text);
                    const online = parseInt(data.online) || 0;
                    const total = parseInt(data.total) || 0;
                    
                    document.getElementById('onlineCount').textContent = online;
                    document.getElementById('totalVisitors').textContent = total.toLocaleString();
                    
                    updateStatus('统计数据更新成功！在线: ' + online + ', 总计: ' + total, 'success');
                    log('统计数据更新成功');
                } catch (e) {
                    log('JSON解析错误: ' + e.message);
                    updateStatus('数据格式错误: ' + e.message, 'error');
                }
            })
            .catch(error => {
                log('请求失败: ' + error.message);
                updateStatus('统计服务错误: ' + error.message, 'error');
                document.getElementById('onlineCount').textContent = '错误';
                document.getElementById('totalVisitors').textContent = '错误';
            });
        }
        
        function testAPI() {
            log('测试API直接访问...');
            window.open('/data/online_stats/online.php', '_blank');
        }
        
        // 页面加载时立即执行
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始初始化统计');
            updateStats();
        });
        
        // 每30秒自动更新一次
        setInterval(updateStats, 30000);
    </script>
</body>
</html>
