<?php
// 测试JSON格式音乐数据提取
define('IN_IWEBDIR', true);
define('APP_PATH', './source/');

require('./source/init.php');
require('./source/module/article.php');

echo "<h2>测试JSON格式音乐数据提取</h2>\n";

// 测试你提供的JSON格式
$test_json_content = '{
    "id": 36685,
    "desc": "20分钟试听经典国语dj串烧，劲爆dj合集，DJ打碟现场 20分钟试听经典国语dj串烧，劲爆dj合集，DJ打碟现场，高速必听越听越上头#经典老歌#劲爆dj#中文dj#越听越上头#dj",
    "music": "https://sf5-hl-cdn-tos.douyinstatic.com/obj/ies-music/7499955641297996554.mp3"
}';

echo "<h3>1. 测试单个JSON对象</h3>\n";
echo "<h4>原始JSON内容：</h4>\n";
echo "<pre>" . htmlspecialchars($test_json_content) . "</pre>\n";

$music_data = extract_music_urls_with_titles($test_json_content);
echo "<h4>提取结果：</h4>\n";
if (!empty($music_data)) {
    foreach ($music_data as $i => $data) {
        echo "<p><strong>音乐 " . ($i + 1) . ":</strong><br>";
        echo "标题: " . htmlspecialchars($data['title']) . "<br>";
        echo "URL: " . htmlspecialchars($data['url']) . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>没有提取到数据</p>\n";
}

// 测试多个JSON对象的数组
$test_json_array = '[
    {
        "id": 36685,
        "desc": "经典老歌DJ串烧合集",
        "music": "https://example.com/music1.mp3"
    },
    {
        "id": 36686,
        "desc": "流行歌曲精选，好听的歌曲推荐",
        "music": "https://example.com/music2.mp3"
    }
]';

echo "<h3>2. 测试JSON数组</h3>\n";
echo "<h4>原始JSON数组：</h4>\n";
echo "<pre>" . htmlspecialchars($test_json_array) . "</pre>\n";

$music_data_array = extract_music_urls_with_titles($test_json_array);
echo "<h4>提取结果：</h4>\n";
if (!empty($music_data_array)) {
    foreach ($music_data_array as $i => $data) {
        echo "<p><strong>音乐 " . ($i + 1) . ":</strong><br>";
        echo "标题: " . htmlspecialchars($data['title']) . "<br>";
        echo "URL: " . htmlspecialchars($data['url']) . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>没有提取到数据</p>\n";
}

// 测试混合内容（JSON + 普通文本）
$test_mixed_content = '
这是一篇包含JSON音乐数据的文章：

{
    "desc": "周杰伦经典歌曲合集，包含青花瓷、稻香等热门歌曲",
    "music": "https://music.163.com/song/media/outer/url?id=185668"
}

还有一些普通格式的音乐：
邓紫棋 - 泡沫：https://example.com/music/paomo.mp3
';

echo "<h3>3. 测试混合内容</h3>\n";
echo "<h4>原始混合内容：</h4>\n";
echo "<pre>" . htmlspecialchars($test_mixed_content) . "</pre>\n";

$music_data_mixed = extract_music_urls_with_titles($test_mixed_content);
echo "<h4>提取结果：</h4>\n";
if (!empty($music_data_mixed)) {
    foreach ($music_data_mixed as $i => $data) {
        echo "<p><strong>音乐 " . ($i + 1) . ":</strong><br>";
        echo "标题: " . htmlspecialchars($data['title']) . "<br>";
        echo "URL: " . htmlspecialchars($data['url']) . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>没有提取到数据</p>\n";
}

// 创建测试文章
echo "<h3>4. 创建测试文章并验证</h3>\n";

// 删除现有测试文章
$DB->query("DELETE FROM ".$DB->table('articles')." WHERE art_title LIKE 'JSON音乐测试%'");

$test_article_content = $test_json_content;
$title = $DB->escape_string('JSON音乐测试 - DJ串烧');
$content = $DB->escape_string($test_article_content);
$intro = $DB->escape_string('测试JSON格式音乐数据提取');

$sql = "INSERT INTO ".$DB->table('articles')." 
        (user_id, cate_id, art_title, art_tags, art_intro, art_content, art_status, art_ctime, art_utime) 
        VALUES 
        (1, 319, '$title', 'JSON,测试,DJ,串烧', '$intro', '$content', 3, ".time().", ".time().")";

if ($DB->query($sql)) {
    $art_id = $DB->insert_id();
    echo "<p style='color: green;'>✓ 测试文章创建成功 (ID: $art_id)</p>\n";
    
    // 测试get_music_links函数
    echo "<h4>get_music_links函数结果：</h4>\n";
    $music_links = get_music_links(319, 20);
    
    foreach ($music_links as $link) {
        if (strpos($link['title'], 'JSON音乐测试') !== false || strpos($link['title'], '20分钟试听') !== false) {
            echo "<p><strong>显示标题:</strong> " . htmlspecialchars($link['title']) . "<br>";
            echo "<strong>提取标题:</strong> " . htmlspecialchars($link['extracted_title'] ?? '无') . "<br>";
            echo "<strong>原文章标题:</strong> " . htmlspecialchars($link['original_title']) . "<br>";
            echo "<strong>URL:</strong> " . htmlspecialchars($link['url']) . "</p>\n";
        }
    }
    
} else {
    echo "<p style='color: red;'>✗ 创建测试文章失败: " . $DB->error() . "</p>\n";
}

echo "<h3>5. 预期效果</h3>\n";
echo "<p>对于你提供的JSON数据，应该提取出：</p>\n";
echo "<ul>\n";
echo "<li><strong>标题:</strong> 20分钟试听经典国语dj串烧，劲爆dj合集，DJ打碟现场</li>\n";
echo "<li><strong>URL:</strong> https://sf5-hl-cdn-tos.douyinstatic.com/obj/ies-music/7499955641297996554.mp3</li>\n";
echo "</ul>\n";
echo "<p>重复内容和hashtag标签会被自动清理。</p>\n";

echo "<h3>6. 测试AJAX接口</h3>\n";
echo "<p><a href='?mod=ajaxget&type=music_list' target='_blank'>点击查看AJAX接口返回的数据</a></p>\n";
?>
