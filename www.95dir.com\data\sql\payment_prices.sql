-- 付费价格配置表
CREATE TABLE IF NOT EXISTS `dir_payment_prices` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `service_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '服务类型：1=VIP，2=推荐，3=快审',
  `service_name` varchar(50) NOT NULL DEFAULT '' COMMENT '服务名称',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '价格',
  `unit` varchar(20) NOT NULL DEFAULT '' COMMENT '单位：年/月/次',
  `duration_days` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '服务时长（天数）',
  `effective_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '生效时间',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1=有效，0=已停用',
  `operator` varchar(50) NOT NULL DEFAULT 'admin' COMMENT '操作员',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `created_at` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `service_type` (`service_type`),
  KEY `effective_time` (`effective_time`),
  KEY `status` (`status`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- 插入默认价格配置
INSERT INTO `dir_payment_prices` (`service_type`, `service_name`, `price`, `unit`, `duration_days`, `effective_time`, `status`, `operator`, `remark`, `created_at`) VALUES
(1, 'VIP服务', 30.00, '年', 365, UNIX_TIMESTAMP(), 1, 'system', '默认VIP价格配置', UNIX_TIMESTAMP()),
(2, '推荐服务', 10.00, '月', 30, UNIX_TIMESTAMP(), 1, 'system', '默认推荐价格配置', UNIX_TIMESTAMP()),
(3, '快审服务', 5.00, '次', 0, UNIX_TIMESTAMP(), 1, 'system', '默认快审价格配置', UNIX_TIMESTAMP());
