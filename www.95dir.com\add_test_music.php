<?php
// 添加测试音乐文章
define('IN_IWEBDIR', true);
define('APP_PATH', './source/');

require('./source/init.php');

echo "<h2>添加测试音乐文章</h2>\n";

// 测试文章数据
$test_articles = array(
    array(
        'title' => '测试音乐1 - 流行歌曲',
        'content' => '这是一首好听的流行歌曲。<br>
        <audio controls>
            <source src="https://music.163.com/song/media/outer/url?id=123456" type="audio/mpeg">
        </audio><br>
        直接链接: https://example.com/music/test1.mp3<br>
        网易云音乐: https://music.163.com/song?id=123456'
    ),
    array(
        'title' => '测试音乐2 - QQ音乐',
        'content' => '这是一首来自QQ音乐的歌曲。<br>
        QQ音乐链接: https://y.qq.com/n/yqq/song/001234567.html<br>
        <audio src="https://example.com/music/test2.mp3" controls></audio>'
    ),
    array(
        'title' => '测试音乐3 - 酷狗音乐',
        'content' => '酷狗音乐测试。<br>
        酷狗链接: https://www.kugou.com/song/123456.html<br>
        MP3文件: https://music-cdn.example.com/songs/test3.mp3'
    )
);

$success_count = 0;
$error_count = 0;

foreach ($test_articles as $index => $article) {
    $art_title = $article['title'];
    $art_content = $article['content'];
    $art_intro = substr(strip_tags($art_content), 0, 100);
    $cate_id = 318; // 流行歌曲分类
    $user_id = 1;   // 管理员
    $art_status = 3; // 已审核
    $art_ctime = time();
    
    // 检查是否已存在相同标题的文章
    $check_sql = "SELECT art_id FROM ".$DB->table('articles')." WHERE art_title='$art_title'";
    $check_result = $DB->query($check_sql);
    
    if ($DB->num_rows($check_result) > 0) {
        echo "文章 '$art_title' 已存在，跳过<br>\n";
        continue;
    }
    
    // 插入文章
    $insert_sql = "INSERT INTO ".$DB->table('articles')." 
                   (user_id, cate_id, art_title, art_tags, art_intro, art_content, art_status, art_ctime, art_utime) 
                   VALUES 
                   ($user_id, $cate_id, '$art_title', '音乐,流行歌曲,测试', '$art_intro', '$art_content', $art_status, $art_ctime, $art_ctime)";
    
    if ($DB->query($insert_sql)) {
        $art_id = $DB->insert_id();
        echo "✓ 成功添加文章: '$art_title' (ID: $art_id)<br>\n";
        $success_count++;
        
        // 测试提取MP3链接
        require('./source/module/article.php');
        $links = extract_music_urls($art_content);
        if (!empty($links)) {
            echo "  提取到的音乐链接:<br>\n";
            foreach ($links as $link) {
                echo "  - " . htmlspecialchars($link) . "<br>\n";
            }
        } else {
            echo "  ✗ 未提取到音乐链接<br>\n";
        }
    } else {
        echo "✗ 添加文章失败: '$art_title' - " . $DB->error() . "<br>\n";
        $error_count++;
    }
    echo "<br>\n";
}

echo "<h3>添加结果</h3>\n";
echo "成功: $success_count 篇<br>\n";
echo "失败: $error_count 篇<br>\n";

// 测试get_music_links函数
echo "<h3>测试音乐链接获取</h3>\n";
require('./source/module/article.php');
$music_links = get_music_links(318, 10);
echo "获取到的音乐链接数量: " . count($music_links) . "<br>\n";

if (!empty($music_links)) {
    echo "<ul>\n";
    foreach ($music_links as $link) {
        echo "<li>" . htmlspecialchars($link['title']) . " - " . htmlspecialchars($link['url']) . "</li>\n";
    }
    echo "</ul>\n";
} else {
    echo "没有获取到音乐链接<br>\n";
}

echo "<h3>测试AJAX接口</h3>\n";
echo "<p>请在浏览器中访问: <a href='?mod=ajaxget&type=music_list' target='_blank'>?mod=ajaxget&type=music_list</a></p>\n";
?>
