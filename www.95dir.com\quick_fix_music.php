<?php
// 快速修复音乐播放器 - 添加测试数据
define('IN_IWEBDIR', true);
define('APP_PATH', './source/');

require('./source/init.php');

echo "<h2>快速修复音乐播放器</h2>\n";

// 检查分类318是否存在
$cate_check = $DB->fetch_one("SELECT cate_id, cate_name FROM ".$DB->table('categories')." WHERE cate_id=318");
if (!$cate_check) {
    echo "<p style='color: red;'>错误：分类318不存在！</p>\n";
    exit;
}

echo "<p>分类318存在: {$cate_check['cate_name']}</p>\n";

// 删除现有的测试文章（如果有）
$DB->query("DELETE FROM ".$DB->table('articles')." WHERE art_title LIKE '测试音乐%' OR art_title LIKE '示例音乐%'");

// 添加测试音乐文章
$test_songs = array(
    array(
        'title' => '测试音乐1 - 周杰伦《青花瓷》',
        'content' => '这是一首经典的中国风歌曲。<br><audio controls><source src="https://music.163.com/song/media/outer/url?id=185668" type="audio/mpeg"></audio><br>网易云音乐链接: https://music.163.com/song?id=185668'
    ),
    array(
        'title' => '测试音乐2 - 邓紫棋《泡沫》',
        'content' => '邓紫棋的代表作之一。<br>直接MP3链接: https://example.com/music/paomo.mp3<br><audio src="https://music.163.com/song/media/outer/url?id=287025" controls></audio>'
    ),
    array(
        'title' => '测试音乐3 - 林俊杰《江南》',
        'content' => '林俊杰的经典歌曲。<br>音乐链接: https://music.163.com/song/media/outer/url?id=185817<br>这首歌描述了江南的美景。'
    ),
    array(
        'title' => '测试音乐4 - 陈奕迅《十年》',
        'content' => '陈奕迅的经典情歌。<br><source src="https://example.com/music/shinian.mp3" type="audio/mpeg"><br>QQ音乐: https://y.qq.com/n/yqq/song/001234567.html'
    ),
    array(
        'title' => '测试音乐5 - 王菲《红豆》',
        'content' => '王菲演唱的经典歌曲。<br>MP3下载: https://music-cdn.example.com/wangfei-hongdou.mp3<br>酷狗音乐: https://www.kugou.com/song/hongdou.html'
    )
);

$success_count = 0;

foreach ($test_songs as $song) {
    $title = $DB->escape_string($song['title']);
    $content = $DB->escape_string($song['content']);
    $intro = $DB->escape_string(substr(strip_tags($song['content']), 0, 100));
    
    // 创建一些已审核的文章和一些管理员待审核的文章来测试两种状态
    $status = ($success_count < 3) ? 3 : 2; // 前3篇设为已审核，后面的设为待审核

    $sql = "INSERT INTO ".$DB->table('articles')."
            (user_id, cate_id, art_title, art_tags, art_intro, art_content, art_status, art_ctime, art_utime)
            VALUES
            (1, 318, '$title', '音乐,流行歌曲,测试', '$intro', '$content', $status, ".time().", ".time().")";
    
    if ($DB->query($sql)) {
        $status_text = ($status == 3) ? '已审核' : '管理员待审核';
        echo "<p style='color: green;'>✓ 添加成功: {$song['title']} (状态: $status_text)</p>\n";
        $success_count++;
    } else {
        echo "<p style='color: red;'>✗ 添加失败: {$song['title']} - " . $DB->error() . "</p>\n";
    }
}

echo "<h3>添加结果</h3>\n";
echo "<p>成功添加 $success_count 首测试音乐</p>\n";

// 测试音乐链接提取
echo "<h3>测试音乐链接提取</h3>\n";
require('./source/module/article.php');

$music_links = get_music_links(318, 10);
echo "<p>提取到的音乐链接数量: " . count($music_links) . "</p>\n";

if (!empty($music_links)) {
    echo "<ul>\n";
    foreach ($music_links as $link) {
        echo "<li><strong>" . htmlspecialchars($link['title']) . "</strong><br>";
        echo "URL: " . htmlspecialchars($link['url']) . "</li>\n";
    }
    echo "</ul>\n";
} else {
    echo "<p style='color: red;'>没有提取到音乐链接</p>\n";
}

// 测试AJAX接口
echo "<h3>测试AJAX接口</h3>\n";
echo "<p><a href='?mod=ajaxget&type=music_list' target='_blank'>点击测试音乐列表API</a></p>\n";

echo "<h3>测试播放器</h3>\n";
echo "<p><a href='test_player.html' target='_blank'>打开播放器测试页面</a></p>\n";

echo "<h3>完成</h3>\n";
echo "<p style='color: green; font-weight: bold;'>音乐播放器修复完成！现在应该可以在首页看到音乐列表了。</p>\n";
?>
