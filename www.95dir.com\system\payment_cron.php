<?php
/**
 * 付费服务自动过期处理脚本
 * 建议通过crontab每天运行一次：0 2 * * * /usr/bin/php /path/to/payment_cron.php
 */

// 设置执行时间限制
set_time_limit(0);

// 引入系统文件
require('common.php');

$websites_table = $DB->table('websites');
$payment_table = $DB->table('payment_records');
$current_time = time();

// 如果是通过web访问，输出HTML格式
$is_web = isset($_SERVER['HTTP_HOST']);

if ($is_web) {
    echo "<html><head><title>付费服务过期检查</title>";
    echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:#28a745;background:#f0f8f0;padding:10px;margin:10px 0;border-radius:5px;} .warning{color:#ffc107;background:#fff8e1;padding:10px;margin:10px 0;border-radius:5px;} .error{color:#dc3545;background:#f8f0f0;padding:10px;margin:10px 0;border-radius:5px;} .info{color:#007bff;background:#f0f0f8;padding:10px;margin:10px 0;border-radius:5px;} h2{color:#333;} h3{color:#666;} .btn{padding:8px 15px;background:#007bff;color:#fff;text-decoration:none;border-radius:3px;margin:5px;}</style>";
    echo "</head><body>";
    echo "<h2>付费服务过期检查</h2>";
    echo "<div class='info'>开始执行付费服务过期检查...<br>当前时间: " . date('Y-m-d H:i:s', $current_time) . "</div>";
} else {
    echo "开始执行付费服务过期检查...\n";
    echo "当前时间: " . date('Y-m-d H:i:s', $current_time) . "\n\n";
}

// 检查VIP过期
if ($is_web) {
    echo "<h3>检查VIP过期网站</h3>";
} else {
    echo "检查VIP过期网站...\n";
}
$vip_expired_count = 0;

// 查找VIP过期的网站
$sql = "SELECT web_id, web_name, web_url, web_vip_expire, web_ctime 
        FROM $websites_table 
        WHERE web_ispay = 1 
        AND (web_vip_expire > 0 AND web_vip_expire < $current_time)";

$query = $DB->query($sql);
while ($row = $DB->fetch_array($query)) {
    $web_id = $row['web_id'];
    $web_name = $row['web_name'];
    $web_url = $row['web_url'];
    $expire_time = $row['web_vip_expire'];
    
    if ($is_web) {
        echo "<div class='warning'>处理VIP过期网站: {$web_name} (ID: {$web_id})</div>";
    } else {
        echo "处理VIP过期网站: {$web_name} (ID: {$web_id})\n";
    }
    
    // 将VIP改为快审状态
    $update_data = array(
        'web_ispay' => 0,  // 取消VIP状态
        'web_status' => 3  // 改为已审核状态（快审）
    );
    $DB->update($websites_table, $update_data, array('web_id' => $web_id));
    
    // 记录到付费记录表
    $payment_data = array(
        'web_id' => $web_id,
        'web_name' => $web_name,
        'web_url' => $web_url,
        'payment_type' => 3, // 快审
        'payment_amount' => 0.00,
        'payment_time' => $current_time,
        'expire_time' => 0, // 快审不设置过期时间
        'operator' => 'system',
        'status' => 1,
        'remark' => 'VIP过期自动转为快审状态',
        'created_at' => $current_time
    );
    $DB->insert($payment_table, $payment_data);
    
    $vip_expired_count++;
}

if ($is_web) {
    echo "<div class='success'>VIP过期处理完成，共处理 {$vip_expired_count} 个网站</div>";
} else {
    if ($is_web) {
    echo "<div class='success'>VIP过期处理完成，共处理 {$vip_expired_count} 个网站</div>";
} else {
    echo "VIP过期处理完成，共处理 {$vip_expired_count} 个网站\n\n";
}
}

// 检查推荐过期
if ($is_web) {
    echo "<h3>检查推荐过期网站</h3>";
} else {
    echo "检查推荐过期网站...\n";
}
$recommend_expired_count = 0;

// 查找推荐过期的网站
$sql = "SELECT web_id, web_name, web_url, web_recommend_expire, web_ctime
        FROM $websites_table
        WHERE web_isbest = 1
        AND (web_recommend_expire > 0 AND web_recommend_expire < $current_time)";

$query = $DB->query($sql);
while ($row = $DB->fetch_array($query)) {
    $web_id = $row['web_id'];
    $web_name = $row['web_name'];
    $web_url = $row['web_url'];
    $expire_time = $row['web_recommend_expire'];
    
    if ($is_web) {
        echo "<div class='warning'>处理推荐过期网站: {$web_name} (ID: {$web_id})</div>";
    } else {
        echo "处理推荐过期网站: {$web_name} (ID: {$web_id})\n";
    }
    
    // 取消推荐状态
    $update_data = array(
        'web_isbest' => 0  // 取消推荐状态
    );
    $DB->update($websites_table, $update_data, array('web_id' => $web_id));
    
    // 记录到付费记录表
    $payment_data = array(
        'web_id' => $web_id,
        'web_name' => $web_name,
        'web_url' => $web_url,
        'payment_type' => 2, // 推荐
        'payment_amount' => 0.00,
        'payment_time' => $current_time,
        'expire_time' => 0,
        'operator' => 'system',
        'status' => 0, // 已过期
        'remark' => '推荐服务过期自动取消',
        'created_at' => $current_time
    );
    $DB->insert($payment_table, $payment_data);
    
    $recommend_expired_count++;
}

if ($is_web) {
    echo "<div class='success'>推荐过期处理完成，共处理 {$recommend_expired_count} 个网站</div>";
} else {
    echo "推荐过期处理完成，共处理 {$recommend_expired_count} 个网站\n\n";
}

// 更新付费记录表中的过期状态
if ($is_web) {
    echo "<h3>更新付费记录过期状态</h3>";
} else {
    echo "更新付费记录过期状态...\n";
}
$update_sql = "UPDATE $payment_table SET status = 0 WHERE expire_time > 0 AND expire_time < $current_time AND status = 1";
$updated_records = $DB->query($update_sql);
if ($is_web) {
    echo "<div class='success'>更新付费记录状态完成</div>";
} else {
    echo "更新付费记录状态完成\n\n";
}

// 生成统计报告
if ($is_web) {
    echo "<h3>统计报告</h3>";
} else {
    echo "生成统计报告...\n";
}
$total_vip = $DB->get_count($websites_table, 'web_ispay = 1');
$total_recommend = $DB->get_count($websites_table, 'web_isbest = 1');
$total_expired_today = $vip_expired_count + $recommend_expired_count;

if ($is_web) {
    echo "<div class='info'>";
    echo "<h4>统计报告</h4>";
    echo "<p><strong>当前VIP网站数量:</strong> {$total_vip}</p>";
    echo "<p><strong>当前推荐网站数量:</strong> {$total_recommend}</p>";
    echo "<p><strong>今日过期处理数量:</strong> {$total_expired_today}</p>";
    echo "<ul>";
    echo "<li>VIP过期: {$vip_expired_count}</li>";
    echo "<li>推荐过期: {$recommend_expired_count}</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "=== 统计报告 ===\n";
    echo "当前VIP网站数量: {$total_vip}\n";
    echo "当前推荐网站数量: {$total_recommend}\n";
    echo "今日过期处理数量: {$total_expired_today}\n";
    echo "  - VIP过期: {$vip_expired_count}\n";
    echo "  - 推荐过期: {$recommend_expired_count}\n";
}

// 检查即将过期的网站（7天内）
$warning_time = $current_time + (7 * 24 * 3600);
$vip_warning = $DB->get_count($websites_table, "web_ispay = 1 AND web_vip_expire > $current_time AND web_vip_expire < $warning_time");
$recommend_warning = $DB->get_count($websites_table, "web_isbest = 1 AND web_recommend_expire > $current_time AND web_recommend_expire < $warning_time");

if ($is_web) {
    echo "<div class='warning'>";
    echo "<h4>7天内即将过期</h4>";
    echo "<ul>";
    echo "<li>VIP即将过期: {$vip_warning}</li>";
    echo "<li>推荐即将过期: {$recommend_warning}</li>";
    echo "</ul>";
    echo "</div>";

    echo "<div class='success'>";
    echo "<h4>过期检查完成！</h4>";
    echo "<p>结束时间: " . date('Y-m-d H:i:s') . "</p>";
    echo "</div>";
} else {
    echo "7天内即将过期:\n";
    echo "  - VIP即将过期: {$vip_warning}\n";
    echo "  - 推荐即将过期: {$recommend_warning}\n";

    echo "\n过期检查完成！\n";
    echo "结束时间: " . date('Y-m-d H:i:s') . "\n";
}

// 如果是通过web访问，输出HTML格式
if ($is_web) {
    echo "<div style='margin-top: 30px; text-align: center;'>";
    echo "<a href='payment_manage.php' class='btn'>返回付费管理</a>";
    echo "<a href='payment_stats.php' class='btn'>查看付费统计</a>";
    echo "<a href='payment_price.php' class='btn'>价格配置</a>";
    echo "<a href='admin.php' class='btn'>管理首页</a>";
    echo "</div>";
    echo "</body></html>";
}
?>
