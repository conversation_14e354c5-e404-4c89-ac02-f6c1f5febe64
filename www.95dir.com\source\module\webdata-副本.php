<?php
/*
 * 网站数据获取模块 - 简单版本（第一版）
 * 恢复到最原始的简单收录量检测
 */

if (!defined('IN_IWEBDIR')) exit('Access Denied');

/** 获取URL内容 - 增强版本，使用不同的函数名避免冲突 */
function get_url_content_enhanced($url, $timeout = 30) {
    // 记录调试信息
    error_log("正在获取URL内容: $url");

    // 方案1: 使用CURL
    if (extension_loaded('curl')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_ENCODING, ''); // 支持gzip压缩

        // 设置完整的浏览器请求头
        $headers = array(
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding: gzip, deflate, br',
            'Cache-Control: no-cache',
            'Pragma: no-cache',
            'Upgrade-Insecure-Requests: 1'
        );

        // 如果是百度搜索，添加特定的请求头
        if (strpos($url, 'baidu.com') !== false) {
            $headers[] = 'Referer: https://www.baidu.com/';
            $headers[] = 'Sec-Fetch-Dest: document';
            $headers[] = 'Sec-Fetch-Mode: navigate';
            $headers[] = 'Sec-Fetch-Site: same-origin';
            $headers[] = 'Sec-Fetch-User: ?1';
        }

        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $data = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($data && $http_code == 200) {
            error_log("CURL获取成功: $url, 数据长度: " . strlen($data));
            return $data;
        } else {
            error_log("CURL获取失败: $url, HTTP状态码: $http_code, 错误: $error");
        }
    }

    // 方案2: 使用file_get_contents (如果允许URL fopen)
    if (ini_get('allow_url_fopen')) {
        $context = stream_context_create([
            'http' => [
                'timeout' => $timeout,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ]
        ]);

        $data = @file_get_contents($url, false, $context);
        if ($data) {
            error_log("file_get_contents获取成功: $url, 数据长度: " . strlen($data));
            return $data;
        } else {
            error_log("file_get_contents获取失败: $url");
        }
    }

    error_log("所有方法都失败了: $url");
    return false;
}

/** 百度收录量（原PageRank字段） - 增强版本 */
function get_pagerank($url) {
    $clean_url = preg_replace('/^https?:\/\//', '', $url);
    $clean_url = preg_replace('/^www\./', '', $clean_url);
    $clean_url = rtrim($clean_url, '/');

    error_log("开始获取百度收录量 - 域名: $clean_url");

    // 方案1: 使用API接口（推荐）
    $api_url = "https://cn.apihz.cn/api/wangzhan/slbaidu.php?id=10004250&key=4faeb28ed2ca8ac3fbfbd12ebd332f46&domain=" . urlencode($clean_url);
    $api_data = get_url_content_enhanced($api_url, 10); // 减少超时时间到10秒

    if ($api_data) {
        $result = json_decode($api_data, true);
        if (isset($result['code'])) {
            if ($result['code'] == 200 && isset($result['num'])) {
                $count = intval($result['num']);
                error_log("百度收录量API获取成功 - 域名: $clean_url, 收录量: $count");
                return $count;
            } elseif ($result['code'] == 400) {
                error_log("百度收录量API频率限制 - 域名: $clean_url, 消息: " . (isset($result['msg']) ? $result['msg'] : '未知'));
                // API频率限制时，直接返回0，不再尝试其他方法
                return 0;
            } else {
                error_log("百度收录量API错误 - 域名: $clean_url, 错误码: {$result['code']}, 消息: " . (isset($result['msg']) ? $result['msg'] : '未知'));
            }
        }
    }

    // 方案2: 直接搜索百度（备用方案）
    $search_url = "https://www.baidu.com/s?wd=site:" . urlencode($clean_url);
    error_log("尝试直接搜索百度: $search_url");

    $data = get_url_content_enhanced($search_url, 10);
    if (!$data || strlen($data) < 200) {
        error_log("百度搜索页面获取失败或内容过短 - 域名: $clean_url");
        return 0;
    }

    // 多种匹配模式
    $patterns = array(
        '/找到相关结果约\s*([0-9,，\s]+)\s*个/u',
        '/百度为您找到相关结果约\s*([0-9,，\s]+)\s*个/u',
        '/相关结果约\s*([0-9,，\s]+)\s*个/u',
        '/找到约\s*([0-9,，\s]+)\s*条结果/u'
    );

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $data, $matches)) {
            $count = preg_replace('/[^0-9]/', '', $matches[1]);
            $count = intval($count);
            error_log("百度收录量直接搜索成功 - 域名: $clean_url, 收录量: $count, 匹配模式: $pattern");
            return $count;
        }
    }

    error_log("百度收录量获取失败 - 域名: $clean_url, 所有方法都失败");
    return 0;
}

/** 必应收录量（原百度权重字段） - 增强版本 */
function get_baidurank($url) {
    $clean_url = preg_replace('/^https?:\/\//', '', $url);
    $clean_url = preg_replace('/^www\./', '', $clean_url);
    $clean_url = rtrim($clean_url, '/');

    error_log("开始获取必应收录量 - 域名: $clean_url");

    // 方案1: 使用API接口（推荐）
    $api_url = "https://cn.apihz.cn/api/wangzhan/slbiying.php?id=10004250&key=4faeb28ed2ca8ac3fbfbd12ebd332f46&domain=" . urlencode($clean_url);
    $api_data = get_url_content_enhanced($api_url, 10);

    if ($api_data) {
        $result = json_decode($api_data, true);
        if (isset($result['code'])) {
            if ($result['code'] == 200 && isset($result['num'])) {
                $count = intval($result['num']);
                error_log("必应收录量API获取成功 - 域名: $clean_url, 收录量: $count");
                return $count;
            } elseif ($result['code'] == 400) {
                error_log("必应收录量API频率限制 - 域名: $clean_url, 消息: " . (isset($result['msg']) ? $result['msg'] : '未知'));
                return 0;
            } else {
                error_log("必应收录量API错误 - 域名: $clean_url, 错误码: {$result['code']}, 消息: " . (isset($result['msg']) ? $result['msg'] : '未知'));
            }
        }
    }

    // 方案2: 直接搜索必应（备用方案）
    $search_url = "https://www.bing.com/search?q=site:" . urlencode($clean_url);
    error_log("尝试直接搜索必应: $search_url");

    $data = get_url_content_enhanced($search_url, 10);
    if (!$data || strlen($data) < 200) {
        error_log("必应搜索页面获取失败或内容过短 - 域名: $clean_url");
        return 0;
    }

    // 多种匹配模式
    $patterns = array(
        '/([0-9,，\s]+)\s*results/i',
        '/([0-9,，\s]+)\s*个结果/u',
        '/About\s*([0-9,，\s]+)\s*results/i',
        '/共找到\s*([0-9,，\s]+)\s*条结果/u'
    );

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $data, $matches)) {
            $count = preg_replace('/[^0-9]/', '', $matches[1]);
            $count = intval($count);
            error_log("必应收录量直接搜索成功 - 域名: $clean_url, 收录量: $count, 匹配模式: $pattern");
            return $count;
        }
    }

    error_log("必应收录量获取失败 - 域名: $clean_url, 所有方法都失败");
    return 0;
}

/** 360收录量（原搜狗权重字段） - 增强版本 */
function get_sogourank($url) {
    $clean_url = preg_replace('/^https?:\/\//', '', $url);
    $clean_url = preg_replace('/^www\./', '', $clean_url);
    $clean_url = rtrim($clean_url, '/');

    error_log("开始获取360收录量 - 域名: $clean_url");

    // 方案1: 使用API接口（推荐）
    $api_url = "https://cn.apihz.cn/api/wangzhan/sl360.php?id=10004250&key=4faeb28ed2ca8ac3fbfbd12ebd332f46&domain=" . urlencode($clean_url);
    $api_data = get_url_content_enhanced($api_url, 10);

    if ($api_data) {
        $result = json_decode($api_data, true);
        if (isset($result['code'])) {
            if ($result['code'] == 200 && isset($result['num'])) {
                $count = intval($result['num']);
                error_log("360收录量API获取成功 - 域名: $clean_url, 收录量: $count");
                return $count;
            } elseif ($result['code'] == 400) {
                error_log("360收录量API频率限制 - 域名: $clean_url");
                return 0;
            } else {
                error_log("360收录量API失败 - 域名: $clean_url, 响应: " . substr($api_data, 0, 200));
            }
        }
    }

    // 方案2: 直接搜索360（备用方案）
    $search_url = "https://www.so.com/s?q=site:" . urlencode($clean_url);
    error_log("尝试直接搜索360: $search_url");

    $data = get_url_content_enhanced($search_url, 10);
    if (!$data || strlen($data) < 200) {
        error_log("360搜索页面获取失败或内容过短 - 域名: $clean_url");
        return 0;
    }

    // 多种匹配模式
    $patterns = array(
        '/找到相关结果约\s*([0-9,，\s]+)\s*个/u',
        '/相关结果约\s*([0-9,，\s]+)\s*个/u',
        '/找到约\s*([0-9,，\s]+)\s*条结果/u'
    );

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $data, $matches)) {
            $count = preg_replace('/[^0-9]/', '', $matches[1]);
            $count = intval($count);
            error_log("360收录量直接搜索成功 - 域名: $clean_url, 收录量: $count, 匹配模式: $pattern");
            return $count;
        }
    }

    error_log("360收录量获取失败 - 域名: $clean_url, 所有方法都失败");
    return 0;
}

/** 搜狗收录量（原Alexa排名字段） - 增强版本 */
function get_alexarank($url) {
    $clean_url = preg_replace('/^https?:\/\//', '', $url);
    $clean_url = preg_replace('/^www\./', '', $clean_url);
    $clean_url = rtrim($clean_url, '/');

    error_log("开始获取搜狗收录量 - 域名: $clean_url");

    // 方案1: 使用API接口（推荐）
    $api_url = "https://cn.apihz.cn/api/wangzhan/slsougou.php?id=10004250&key=4faeb28ed2ca8ac3fbfbd12ebd332f46&domain=" . urlencode($clean_url);
    $api_data = get_url_content_enhanced($api_url, 10);

    if ($api_data) {
        $result = json_decode($api_data, true);
        if (isset($result['code'])) {
            if ($result['code'] == 200 && isset($result['num'])) {
                $count = intval($result['num']);
                error_log("搜狗收录量API获取成功 - 域名: $clean_url, 收录量: $count");
                return $count;
            } elseif ($result['code'] == 400) {
                error_log("搜狗收录量API频率限制 - 域名: $clean_url");
                return 0;
            } else {
                error_log("搜狗收录量API失败 - 域名: $clean_url, 响应: " . substr($api_data, 0, 200));
            }
        }
    }

    // 方案2: 直接搜索搜狗（备用方案）
    $search_url = "https://www.sogou.com/web?query=site:" . urlencode($clean_url);
    error_log("尝试直接搜索搜狗: $search_url");

    $data = get_url_content_enhanced($search_url, 10);
    if (!$data || strlen($data) < 200) {
        error_log("搜狗搜索页面获取失败或内容过短 - 域名: $clean_url");
        return 0;
    }

    // 多种匹配模式
    $patterns = array(
        '/找到约\s*([0-9,，\s]+)\s*条结果/u',
        '/相关结果约\s*([0-9,，\s]+)\s*个/u',
        '/找到相关结果约\s*([0-9,，\s]+)\s*个/u'
    );

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $data, $matches)) {
            $count = preg_replace('/[^0-9]/', '', $matches[1]);
            $count = intval($count);
            error_log("搜狗收录量直接搜索成功 - 域名: $clean_url, 收录量: $count, 匹配模式: $pattern");
            return $count;
        }
    }

    error_log("搜狗收录量获取失败 - 域名: $clean_url, 所有方法都失败");
    return 0;
}

/** 获取网站标题 */
function get_title($url) {
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = 'http://' . $url;
    }
    
    $data = get_url_content($url);
    if (!$data) {
        return '';
    }
    
    if (preg_match('/<title[^>]*>(.*?)<\/title>/is', $data, $matches)) {
        $title = trim($matches[1]);
        $title = html_entity_decode($title, ENT_QUOTES, 'UTF-8');
        return $title;
    }
    
    return '';
}

/** 获取网站描述 */
function get_description($url) {
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = 'http://' . $url;
    }
    
    $data = get_url_content($url);
    if (!$data) {
        return '';
    }
    
    // 查找meta description
    if (preg_match('/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $data, $matches)) {
        $description = trim($matches[1]);
        $description = html_entity_decode($description, ENT_QUOTES, 'UTF-8');
        return $description;
    }
    
    // 如果没有找到meta description，尝试另一种格式
    if (preg_match('/<meta[^>]*content=["\']([^"\']*)["\'][^>]*name=["\']description["\'][^>]*>/i', $data, $matches)) {
        $description = trim($matches[1]);
        $description = html_entity_decode($description, ENT_QUOTES, 'UTF-8');
        return $description;
    }
    
    return '';
}

/** 获取网站关键词 */
function get_keywords($url) {
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = 'http://' . $url;
    }
    
    $data = get_url_content($url);
    if (!$data) {
        return '';
    }
    
    // 查找meta keywords
    if (preg_match('/<meta[^>]*name=["\']keywords["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $data, $matches)) {
        $keywords = trim($matches[1]);
        $keywords = html_entity_decode($keywords, ENT_QUOTES, 'UTF-8');
        return $keywords;
    }
    
    // 如果没有找到meta keywords，尝试另一种格式
    if (preg_match('/<meta[^>]*content=["\']([^"\']*)["\'][^>]*name=["\']keywords["\'][^>]*>/i', $data, $matches)) {
        $keywords = trim($matches[1]);
        $keywords = html_entity_decode($keywords, ENT_QUOTES, 'UTF-8');
        return $keywords;
    }
    
    return '';
}

/** 获取所有网站数据 */
function get_all_website_data($url) {
    $data = array(
        'title' => get_title($url),
        'description' => get_description($url),
        'keywords' => get_keywords($url),
        'baidu_index' => get_pagerank($url),
        'bing_index' => get_baidurank($url),
        '360_index' => get_sogourank($url),
        'sogou_index' => get_alexarank($url),
        'check_time' => date('Y-m-d H:i:s')
    );

    return $data;
}

/** 获取META信息 */
function get_sitemeta($url) {
	$url = format_url($url);
	$data = get_url_content($url);
	$meta = array();
	if (!empty($data)) {
		#Title
		preg_match('/<TITLE>([\w\W]*?)<\/TITLE>/si', $data, $matches);
		if (!empty($matches[1])) {
			$meta['title'] = $matches[1];
		}

		#Keywords
		preg_match('/<META\s+name="keywords"\s+content="([\w\W]*?)"/si', $data, $matches);
		if (empty($matches[1])) {
			preg_match("/<META\s+name='keywords'\s+content='([\w\W]*?)'/si", $data, $matches);
		}
		if (empty($matches[1])) {
			preg_match('/<META\s+content="([\w\W]*?)"\s+name="keywords"/si', $data, $matches);
		}
		if (empty($matches[1])) {
			preg_match('/<META\s+http-equiv="keywords"\s+content="([\w\W]*?)"/si', $data, $matches);
		}
		if (!empty($matches[1])) {
			$meta['keywords'] = $matches[1];
		}

		#Description
		preg_match('/<META\s+name="description"\s+content="([\w\W]*?)"/si', $data, $matches);
		if (empty($matches[1])) {
			preg_match("/<META\s+name='description'\s+content='([\w\W]*?)'/si", $data, $matches);
		}
		if (empty($matches[1])) {
			preg_match('/<META\s+content="([\w\W]*?)"\s+name="description"/si', $data, $matches);
		}
		if (empty($matches[1])) {
			preg_match('/<META\s+http-equiv="description"\s+content="([\w\W]*?)"/si', $data, $matches);
		}
		if (!empty($matches[1])) {
			$meta['description'] = $matches[1];
		}
	}

	return $meta;
}

/** Server IP */
function get_serverip($url) {
	$domain = get_domain($url);
	if ($domain) {
		$ip = gethostbyname($domain);
	} else {
		$ip = 0;
	}

	return $ip;
}

/** 获取域名 - 检查是否已定义避免重复 */
if (!function_exists('get_domain')) {
    function get_domain($url) {
        // 移除协议前缀
        $url = preg_replace('/^https?:\/\//', '', $url);
        // 移除www前缀
        $url = preg_replace('/^www\./', '', $url);
        // 移除路径部分，只保留域名
        $url = preg_replace('/\/.*$/', '', $url);
        // 移除端口号
        $url = preg_replace('/:\d+$/', '', $url);

        return $url ? $url : false;
    }
}

/** 格式化URL - 检查是否已定义避免重复 */
if (!function_exists('format_url')) {
    function format_url($url) {
        if ($url != "") {
            // 如果URL已经包含协议，直接返回
            if (preg_match('/^https?:\/\//i', $url)) {
                return $url;
            }

            $url_parts = parse_url($url);
            $scheme = isset($url_parts['scheme']) ? $url_parts['scheme'] : 'http';
            $host = isset($url_parts['host']) ? $url_parts['host'] : '';
            $path = isset($url_parts['path']) ? $url_parts['path'] : '';
            $port = !empty($url_parts['port']) ? ':'.$url_parts['port'] : '';

            // 如果没有host，说明输入的可能是域名
            if (empty($host) && !empty($url)) {
                $host = $url;
                $path = '';
            }

            // 构建完整URL，不强制添加尾部斜杠
            $formatted_url = $scheme . '://' . $host . $port . $path;

            return $formatted_url;
        }
        return '';
    }
}

?>
