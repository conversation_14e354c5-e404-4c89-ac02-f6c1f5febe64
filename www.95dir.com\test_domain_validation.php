<?php
/**
 * 测试域名验证功能 - 支持带查询参数的URL
 */

// 引入必要的文件
define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

require(APP_PATH.'include/validate.php');

echo "<h1>域名验证功能测试</h1>";
echo "<p>测试修改后的 is_valid_domain() 函数是否支持带查询参数的URL</p>";

// 测试用例
$test_urls = [
    // 基本域名格式
    'example.com' => true,
    'www.example.com' => true,
    'subdomain.example.com' => true,
    
    // 带协议的URL（应该被正确处理）
    'http://www.example.com' => true,
    'https://www.example.com' => true,
    
    // 带路径的URL
    'www.example.com/path' => true,
    'example.com/path/to/page' => true,
    
    // 带查询参数的URL（重点测试）
    'www.95dir.com/?mod=siteinfo&wid=394' => true,
    'example.com/?param=value' => true,
    'subdomain.example.com/path?param1=value1&param2=value2' => true,
    
    // 复杂的URL
    'www.95dir.com/index.php?mod=siteinfo&wid=394&category=test' => true,
    'api.example.com/v1/data?format=json&limit=10' => true,
    
    // 带端口的URL
    'localhost:8080' => true,
    'example.com:3000/api?version=1' => true,
    
    // 无效的格式
    '' => false,
    'invalid..domain' => false,
    '.example.com' => false,
    'example.' => false,
    'http://' => false,
    '://example.com' => false,
];

echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background-color: #f0f0f0;'>";
echo "<th>测试URL</th>";
echo "<th>期望结果</th>";
echo "<th>实际结果</th>";
echo "<th>测试状态</th>";
echo "</tr>";

$passed = 0;
$failed = 0;

foreach ($test_urls as $url => $expected) {
    $result = is_valid_domain($url);
    $status = ($result === $expected) ? '✓ 通过' : '✗ 失败';
    $status_color = ($result === $expected) ? 'green' : 'red';
    
    if ($result === $expected) {
        $passed++;
    } else {
        $failed++;
    }
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($url) . "</td>";
    echo "<td>" . ($expected ? '有效' : '无效') . "</td>";
    echo "<td>" . ($result ? '有效' : '无效') . "</td>";
    echo "<td style='color: $status_color; font-weight: bold;'>$status</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>测试结果统计</h2>";
echo "<p><strong>通过：</strong> <span style='color: green;'>$passed</span></p>";
echo "<p><strong>失败：</strong> <span style='color: red;'>$failed</span></p>";
echo "<p><strong>总计：</strong> " . ($passed + $failed) . "</p>";

if ($failed == 0) {
    echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 所有测试通过！域名验证功能已成功支持带查询参数的URL。</p>";
} else {
    echo "<p style='color: red; font-weight: bold; font-size: 18px;'>⚠️ 有 $failed 个测试失败，请检查代码。</p>";
}

echo "<h2>前端JavaScript测试</h2>";
echo "<p>以下是前端JavaScript验证的测试：</p>";
echo "<div id='js-test-results'></div>";

?>

<script>
// 前端JavaScript验证测试
function testJavaScriptValidation() {
    const testUrls = [
        'www.95dir.com/?mod=siteinfo&wid=394',
        'example.com/path?param=value',
        'subdomain.example.com/api?version=1&format=json',
        'localhost:8080/admin?tab=settings',
        'invalid..domain',
        ''
    ];
    
    const expectedResults = [true, true, true, true, false, false];
    
    let html = '<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse; width: 100%;">';
    html += '<tr style="background-color: #f0f0f0;">';
    html += '<th>测试URL</th><th>验证结果</th><th>提取的主机名</th><th>状态</th>';
    html += '</tr>';
    
    let jsPassed = 0;
    let jsFailed = 0;
    
    testUrls.forEach((url, index) => {
        const expected = expectedResults[index];
        let result = false;
        let hostname = '';
        
        try {
            // 模拟前端验证逻辑
            const cleanUrl = url.replace(/^https?:\/\//, '').replace(/^www\./, '').replace(/\/$/, '');
            
            let urlToValidate = cleanUrl;
            try {
                const tempUrl = new URL('http://' + cleanUrl);
                urlToValidate = tempUrl.hostname;
                hostname = tempUrl.hostname;
            } catch (e) {
                urlToValidate = cleanUrl.split('/')[0].split('?')[0];
                hostname = urlToValidate;
            }
            
            const domainPattern = /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/;
            result = domainPattern.test(urlToValidate);
        } catch (error) {
            result = false;
            hostname = 'Error: ' + error.message;
        }
        
        const status = (result === expected) ? '✓ 通过' : '✗ 失败';
        const statusColor = (result === expected) ? 'green' : 'red';
        
        if (result === expected) {
            jsPassed++;
        } else {
            jsFailed++;
        }
        
        html += `<tr>`;
        html += `<td>${url}</td>`;
        html += `<td>${result ? '有效' : '无效'}</td>`;
        html += `<td>${hostname}</td>`;
        html += `<td style="color: ${statusColor}; font-weight: bold;">${status}</td>`;
        html += `</tr>`;
    });
    
    html += '</table>';
    html += `<h3>JavaScript测试结果</h3>`;
    html += `<p><strong>通过：</strong> <span style="color: green;">${jsPassed}</span></p>`;
    html += `<p><strong>失败：</strong> <span style="color: red;">${jsFailed}</span></p>`;
    
    if (jsFailed == 0) {
        html += `<p style="color: green; font-weight: bold;">🎉 前端JavaScript验证也全部通过！</p>`;
    } else {
        html += `<p style="color: red; font-weight: bold;">⚠️ 前端JavaScript验证有 ${jsFailed} 个失败。</p>`;
    }
    
    document.getElementById('js-test-results').innerHTML = html;
}

// 页面加载后执行测试
window.onload = testJavaScriptValidation;
</script>

<h2>使用说明</h2>
<ul>
    <li><strong>修改内容：</strong>更新了 <code>is_valid_domain()</code> 函数以支持带路径和查询参数的URL</li>
    <li><strong>支持格式：</strong>
        <ul>
            <li>基本域名：example.com</li>
            <li>带www：www.example.com</li>
            <li>带路径：example.com/path</li>
            <li>带查询参数：example.com/?mod=siteinfo&wid=394</li>
            <li>复杂URL：subdomain.example.com/path?param1=value1&param2=value2</li>
        </ul>
    </li>
    <li><strong>修改文件：</strong>
        <ul>
            <li>source/include/validate.php - 后端验证函数</li>
            <li>themes/default/quicksubmit.html - 快速提交页面</li>
            <li>themes/default/addurl.html - 会员提交页面</li>
            <li>themes/system/website.html - 后台管理页面</li>
            <li>system/ajax_check.php - AJAX检查接口</li>
        </ul>
    </li>
</ul>

<h2>下一步操作</h2>
<ol>
    <li>清除模板缓存：删除 data/compile/ 目录下的编译文件</li>
    <li>测试各个页面的域名提交功能</li>
    <li>验证带查询参数的URL能否正常提交和检查</li>
</ol>
