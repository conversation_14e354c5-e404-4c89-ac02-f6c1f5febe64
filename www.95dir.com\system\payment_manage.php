<?php
require('common.php');
require('../source/module/payment_price.php');

$fileurl = 'payment_manage.php';
$tempfile = 'payment_manage.html';
$websites_table = $DB->table('websites');
$payment_table = $DB->table('payment_records');

if (!isset($action)) $action = 'list';

/** 付费管理列表 */
if ($action == 'list') {
    $pagetitle = '付费管理';
    
    // 筛选条件
    $status_filter = intval($_GET['status']); // 0=全部, 1=正常, 2=即将到期, 3=已过期
    $type_filter = intval($_GET['type']); // 0=全部, 1=VIP, 2=推荐, 3=快审
    $keywords = addslashes(trim($_POST['keywords'] ? $_POST['keywords'] : $_GET['keywords']));
    
    $pageurl = $fileurl;
    $where_conditions = array('w.web_ispay > 0'); // 只显示付费网站
    $params = array();
    
    // 构建查询条件
    if ($type_filter > 0) {
        switch ($type_filter) {
            case 1: // VIP
                $where_conditions[] = 'w.web_ispay = 1';
                break;
            case 2: // 推荐
                $where_conditions[] = 'w.web_isbest = 1';
                break;
            case 3: // 快审
                $where_conditions[] = 'w.web_status = 3 AND w.web_ispay = 0 AND w.best = 0';
                break;
        }
        $params[] = "type=$type_filter";
    }
    
    if (!empty($keywords)) {
        $where_conditions[] = "(w.web_name LIKE '%$keywords%' OR w.web_url LIKE '%$keywords%')";
        $params[] = "keywords=" . urlencode($keywords);
    }
    
    if ($status_filter > 0) {
        $params[] = "status=$status_filter";
    }
    
    $where = implode(' AND ', $where_conditions);
    
    if (!empty($params)) {
        $pageurl .= '?' . implode('&', $params);
    }
    
    // 获取网站列表
    $sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_ispay, w.web_isbest as web_istop, w.web_isbest,
                   w.web_status, w.web_ctime, w.web_vip_expire, w.web_recommend_expire, w.web_fast_expire,
                   c.cate_name
            FROM $websites_table w
            LEFT JOIN " . $DB->table('categories') . " c ON w.cate_id = c.cate_id
            WHERE $where
            ORDER BY w.web_ctime DESC
            LIMIT $start, $pagesize";
    
    $query = $DB->query($sql);
    $websites = array();
    $current_time = time();
    
    while ($row = $DB->fetch_array($query)) {
        // 判断付费类型和到期状态
        $payment_types = array();
        $expire_status = 'normal'; // normal, warning, expired
        $nearest_expire = 0;
        
        // VIP检查
        if ($row['web_ispay'] == 1) {
            $vip_expire = $row['web_vip_expire'] ?: ($row['web_ctime'] + 365 * 24 * 3600); // 默认一年
            $payment_types[] = array(
                'type' => 'VIP',
                'color' => '#ff6600',
                'expire_time' => $vip_expire,
                'expire_date' => date('Y-m-d', $vip_expire)
            );
            if (!$nearest_expire || $vip_expire < $nearest_expire) {
                $nearest_expire = $vip_expire;
            }
        }
        
        // 推荐检查
        if ($row['web_istop'] == 1) {
            $recommend_expire = $row['web_recommend_expire'] ?: ($row['web_ctime'] + 365 * 24 * 3600); // 默认一年
            $payment_types[] = array(
                'type' => '推荐',
                'color' => '#28a745',
                'expire_time' => $recommend_expire,
                'expire_date' => date('Y-m-d', $recommend_expire)
            );
            if (!$nearest_expire || $recommend_expire < $nearest_expire) {
                $nearest_expire = $recommend_expire;
            }
        }
        
        // 快审检查（这里假设快审是一次性的，不需要续费）
        
        // 判断到期状态
        if ($nearest_expire > 0) {
            $days_left = ($nearest_expire - $current_time) / (24 * 3600);
            if ($days_left < 0) {
                $expire_status = 'expired';
            } elseif ($days_left <= 7) {
                $expire_status = 'warning';
            }
        }
        
        // 根据状态筛选过滤
        if ($status_filter > 0) {
            $show = false;
            switch ($status_filter) {
                case 1: // 正常
                    $show = ($expire_status == 'normal');
                    break;
                case 2: // 即将到期
                    $show = ($expire_status == 'warning');
                    break;
                case 3: // 已过期
                    $show = ($expire_status == 'expired');
                    break;
            }
            if (!$show) continue;
        }
        
        $row['payment_types'] = $payment_types;
        $row['expire_status'] = $expire_status;
        $row['nearest_expire'] = $nearest_expire;
        $row['web_ctime_formatted'] = date('Y-m-d', $row['web_ctime']);
        
        // 状态显示
        switch ($expire_status) {
            case 'expired':
                $row['status_text'] = '已过期';
                $row['status_color'] = '#dc3545';
                break;
            case 'warning':
                $row['status_text'] = '即将到期';
                $row['status_color'] = '#ffc107';
                break;
            default:
                $row['status_text'] = '正常';
                $row['status_color'] = '#28a745';
        }
        
        $websites[] = $row;
    }
    
    // 获取总数
    $total = $DB->get_count($websites_table . ' w', $where);
    
    // 分页
    $showpage = '';
    if ($total > $pagesize) {
        $pages = ceil($total / $pagesize);
        $showpage = showpage($pageurl, $total, $curpage, $pagesize);
    }
    
    // 统计数据
    $stats = array();
    $stats['total_vip'] = $DB->get_count($websites_table, 'web_ispay = 1');
    $stats['total_recommend'] = $DB->get_count($websites_table, 'web_isbest = 1');
    $stats['total_paid'] = $DB->get_count($websites_table, 'web_ispay > 0 OR web_istop > 0');
    
    $smarty->assign('websites', $websites);
    $smarty->assign('stats', $stats);
    $smarty->assign('showpage', $showpage);
    $smarty->assign('total', $total);
    $smarty->assign('status_filter', $status_filter);
    $smarty->assign('type_filter', $type_filter);
    $smarty->assign('keywords', $keywords);
}

/** 续费操作 */
if ($action == 'renew') {
    $web_id = intval($_POST['web_id']);
    $payment_type = intval($_POST['payment_type']); // 1=VIP, 2=推荐
    $months = intval($_POST['months']); // 续费月数
    
    if (!$web_id || !$payment_type || !$months) {
        msgbox('参数错误！', $fileurl);
    }
    
    $website = $DB->fetch_one("SELECT * FROM $websites_table WHERE web_id = $web_id");
    if (!$website) {
        msgbox('网站不存在！', $fileurl);
    }
    
    $current_time = time();
    // 根据服务类型计算续费时间
    if ($payment_type == 1) {
        // VIP按年计算
        $extend_seconds = $months * 30 * 24 * 3600; // 按30天计算月份
    } else {
        // 推荐按年计算
        $extend_seconds = $months * 30 * 24 * 3600; // 按30天计算月份
    }
    
    // 获取价格配置并计算金额
    $price_config = get_current_price($payment_type);
    if (!$price_config) {
        msgbox('未找到该服务的价格配置！', $fileurl);
    }

    $type_name = '';
    $expire_field = '';
    $status_field = '';

    switch ($payment_type) {
        case 1: // VIP
            $type_name = 'VIP';
            $expire_field = 'web_vip_expire';
            $status_field = 'web_ispay';
            break;
        case 2: // 推荐
            $type_name = '推荐';
            $expire_field = 'web_recommend_expire';
            $status_field = 'web_istop';
            break;
    }

    // 使用价格配置计算金额和到期时间
    $current_expire = $website[$expire_field] ?: 0;
    $calculation = calculate_payment($payment_type, $months, $current_expire);
    if (!$calculation) {
        msgbox('价格计算失败！', $fileurl);
    }

    $amount = $calculation['amount'];
    $new_expire = $calculation['new_expire'];
    

    
    // 更新网站状态
    $update_data = array(
        $expire_field => $new_expire,
        $status_field => 1
    );
    $DB->update($websites_table, $update_data, array('web_id' => $web_id));
    
    // 记录付费记录
    $payment_data = array(
        'web_id' => $web_id,
        'web_name' => $website['web_name'],
        'web_url' => $website['web_url'],
        'payment_type' => $payment_type,
        'payment_amount' => $amount,
        'payment_time' => $current_time,
        'expire_time' => $new_expire,
        'operator' => $myself['user_email'],
        'status' => 1,
        'remark' => "手动续费{$months}个月",
        'created_at' => $current_time
    );
    $DB->insert($payment_table, $payment_data);
    
    msgbox("网站 {$website['web_name']} 的{$type_name}服务已成功续费{$months}个月！", $fileurl);
}

smarty_output($tempfile);
?>
