<?php
// 调试Meta信息获取问题
define('IN_IWEBDIR', true);
require_once('config.php');
require_once('source/init.php');

$test_domain = 'baidu.com';

echo "<h2>🔍 Meta信息获取调试</h2>";
echo "<p><strong>测试域名:</strong> {$test_domain}</p>";

// 1. 直接测试get_sitemeta函数
echo "<h3>📊 直接测试get_sitemeta函数</h3>";

if (function_exists('get_sitemeta')) {
    echo "<p>✅ get_sitemeta函数存在</p>";
    
    try {
        $meta = get_sitemeta($test_domain);
        echo "<h4>返回结果:</h4>";
        echo "<pre>";
        print_r($meta);
        echo "</pre>";
        
        if (!empty($meta['title']) || !empty($meta['keywords']) || !empty($meta['description'])) {
            echo "<p style='color: green;'>✅ 成功获取到Meta信息</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ 函数执行成功但未获取到有效数据</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 函数执行异常: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ get_sitemeta函数不存在</p>";
}

// 2. 测试AJAX接口返回的JavaScript
echo "<h3>📡 测试AJAX接口</h3>";
echo "<p>AJAX接口链接: <a href='?mod=ajaxget&type=crawl&url=" . urlencode($test_domain) . "' target='_blank'>?mod=ajaxget&type=crawl&url=" . urlencode($test_domain) . "</a></p>";

// 模拟AJAX接口的返回内容
ob_start();
$_GET['type'] = 'crawl';
$_GET['url'] = $test_domain;

try {
    include('module/ajaxget.php');
    $ajax_output = ob_get_contents();
} catch (Exception $e) {
    $ajax_output = "错误: " . $e->getMessage();
}
ob_end_clean();

echo "<h4>AJAX接口返回内容:</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #ddd;'>";
echo htmlspecialchars($ajax_output);
echo "</pre>";

// 3. 分析返回的JavaScript代码
echo "<h3>🔧 JavaScript代码分析</h3>";

if (strpos($ajax_output, '$("#web_name")') !== false) {
    echo "<p>✅ 包含网站名称设置代码</p>";
    
    // 提取网站名称
    if (preg_match('/\$\("#web_name"\)\.attr\("value",\s*"([^"]*)"\)/', $ajax_output, $matches)) {
        echo "<p><strong>网站名称:</strong> " . htmlspecialchars($matches[1]) . "</p>";
    }
} else {
    echo "<p>❌ 未包含网站名称设置代码</p>";
}

if (strpos($ajax_output, '$("#web_tags")') !== false) {
    echo "<p>✅ 包含TAG标签设置代码</p>";
    
    // 提取TAG标签
    if (preg_match('/\$\("#web_tags"\)\.attr\("value",\s*"([^"]*)"\)/', $ajax_output, $matches)) {
        echo "<p><strong>TAG标签:</strong> " . htmlspecialchars($matches[1]) . "</p>";
    }
} else {
    echo "<p>❌ 未包含TAG标签设置代码</p>";
}

if (strpos($ajax_output, '$("#web_intro")') !== false) {
    echo "<p>✅ 包含网站简介设置代码</p>";
    
    // 提取网站简介
    if (preg_match('/\$\("#web_intro"\)\.attr\("value",\s*"([^"]*)"\)/', $ajax_output, $matches)) {
        $intro = htmlspecialchars($matches[1]);
        echo "<p><strong>网站简介:</strong> " . (strlen($intro) > 100 ? substr($intro, 0, 100) . '...' : $intro) . "</p>";
    }
} else {
    echo "<p>❌ 未包含网站简介设置代码</p>";
}

// 4. 检查可能的问题
echo "<h3>⚠️ 可能的问题分析</h3>";
echo "<ul>";

if (empty($ajax_output)) {
    echo "<li><strong>AJAX接口无返回:</strong> 可能是接口路径错误或服务器配置问题</li>";
}

if (strpos($ajax_output, 'error') !== false || strpos($ajax_output, 'Error') !== false) {
    echo "<li><strong>接口返回错误:</strong> 检查错误信息并修复</li>";
}

if (!strpos($ajax_output, '$("#') !== false) {
    echo "<li><strong>返回格式异常:</strong> 返回的不是预期的JavaScript代码</li>";
}

echo "<li><strong>网络问题:</strong> 目标网站可能无法访问或响应缓慢</li>";
echo "<li><strong>编码问题:</strong> 网站内容编码可能导致解析失败</li>";
echo "<li><strong>JavaScript执行:</strong> 浏览器可能无法正确执行返回的JavaScript代码</li>";
echo "</ul>";

// 5. 建议的解决方案
echo "<h3>🔧 建议的解决方案</h3>";
echo "<ol>";
echo "<li><strong>检查网络连接:</strong> 确保服务器可以访问目标网站</li>";
echo "<li><strong>增加调试信息:</strong> 在JavaScript中添加更多console.log输出</li>";
echo "<li><strong>错误处理:</strong> 改进JavaScript的错误处理机制</li>";
echo "<li><strong>超时设置:</strong> 适当增加AJAX请求的超时时间</li>";
echo "<li><strong>备用方案:</strong> 提供手动填写的友好提示</li>";
echo "</ol>";

// 6. 实际测试
echo "<h3>🧪 实际测试</h3>";
echo "<p>在快速提交页面测试: <a href='?mod=quicksubmit' target='_blank'>?mod=quicksubmit</a></p>";
echo "<p>测试步骤:</p>";
echo "<ol>";
echo "<li>输入域名: {$test_domain}</li>";
echo "<li>点击其他字段触发域名检测</li>";
echo "<li>观察是否自动获取Meta信息</li>";
echo "<li>检查浏览器控制台的错误信息</li>";
echo "</ol>";

// 7. 浏览器控制台测试代码
echo "<h3>🖥️ 浏览器控制台测试代码</h3>";
echo "<p>在快速提交页面的浏览器控制台中执行以下代码进行测试:</p>";
echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #ddd;'>";
echo "// 测试jQuery是否可用\n";
echo "console.log('jQuery版本:', typeof $ !== 'undefined' ? $.fn.jquery : '未加载');\n\n";
echo "// 测试AJAX接口\n";
echo "$.ajax({\n";
echo "    type: 'GET',\n";
echo "    url: '{$options['site_url']}?mod=ajaxget&type=crawl&url={$test_domain}',\n";
echo "    success: function(data) {\n";
echo "        console.log('AJAX成功:', data);\n";
echo "        eval(data);\n";
echo "        console.log('网站名称:', $('#web_name').val());\n";
echo "        console.log('TAG标签:', $('#web_tags').val());\n";
echo "        console.log('网站简介:', $('#web_intro').val());\n";
echo "    },\n";
echo "    error: function(xhr, status, error) {\n";
echo "        console.error('AJAX失败:', status, error);\n";
echo "    }\n";
echo "});";
echo "</pre>";
?>
