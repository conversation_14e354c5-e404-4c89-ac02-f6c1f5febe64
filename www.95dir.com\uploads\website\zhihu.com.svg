<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta http-equiv="X-UA-Compatible" content="ie=edge,chrome=1" />
<meta name="renderer" content="webkit" />
<meta name="force-rendering" content="webkit" />
<title>404 - 知乎</title>
<link
rel="shortcut icon"
href="//static.zhihu.com/static/img/favicon.ico"
/>
<style>
body,
h1,
p,
pre {
margin: 0;
}
body {
font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue",
"PingFang SC", "Microsoft YaHei", "Source Han Sans SC",
"Noto Sans CJK SC", "WenQuanYi Micro Hei", sans-serif;
}
a {
text-decoration: none;
}
</style>
<style>
.Button {
background: none;
border: none;
cursor: pointer;
display: inline-block;
font-size: 14px;
padding: 0;
text-align: center;
}
.Button:focus {
outline: none;
transition: box-shadow 0.3s;
}
/* 暂时没有用 focus visiable */
.Button:focus {
box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px rgba(0, 132, 255, 0.3);
}
html[data-theme="dark"] .Button:focus {
box-shadow: 0 0 0 2px #1a1a1a, 0 0 0 4px rgba(58, 118, 208, 0.6);
}
.Button--primary {
border: 1px solid;
border-radius: 3px;
line-height: 32px;
padding: 0 16px;
}
.Button--primary.Button--blue {
background-color: #0084ff;
border-color: #0084ff;
color: #fff;
}
html[data-theme="dark"] .Button--primary.Button--blue {
background-color: #3a76d0;
border-color: #3a76d0;
color: #fff;
}
.Button--primary.Button--blue:hover {
background-color: #0077e6;
border-color: #0077e6;
}
html[data-theme="dark"] .Button--primary.Button--blue:hover {
background-color: #2e69c2;
border-color: #2e69c2;
}
.Button--plain.Button--blue {
color: #0084ff;
}
html[data-theme="dark"] .Button--plain.Button--blue {
color: #3a76d0;
}
.Button--plain.Button--blue:hover {
color: #0077e6;
}
html[data-theme="dark"] .Button--plain.Button--blue:hover {
color: #2e69c2;
}
</style>
<style>
body {
/* cc(GBK99A) */
background: #fff;
}
html[data-theme="dark"] body {
background: #1a1a1a;
}
.ErrorPage-container {
display: flex;
justify-content: center;
margin-top: 180px;
}
@media (max-width: 690px) {
.ErrorPage-container {
align-items: center;
flex-direction: column-reverse;
margin-top: 60px;
}
}
.ErrorPage-title,
.ErrorPage-subtitle {
/* cc(GBK04A) */
color: #646464;
line-height: 1.4;
}
html[data-theme="dark"] .ErrorPage-title,
html[data-theme="dark"] .ErrorPage-subtitle {
color: #999;
}
.ErrorPage-title {
font-size: 40px;
/* @mixin bold; */
font-synthesis: style;
font-weight: 600;
}
html[data-ios] .ErrorPage-title {
font-weight: 500;
}
html[data-android] .ErrorPage-title {
font-weight: 700;
}
@media (max-width: 690px) {
.ErrorPage-title {
font-size: 30px;
}
}
.ErrorPage-subtitle {
font-size: 18px;
margin-top: 10px;
}
.ErrorPage-text {
align-items: flex-start;
display: flex;
flex-direction: column;
justify-content: center;
min-width: 384px;
}
@media (max-width: 690px) {
.ErrorPage-text {
align-items: center;
min-width: auto;
}
}
.ErrorPage-primaryButton {
margin-top: 20px;
}
.ErrorPage-otherButtonContainer {
align-items: center;
/* cc(GBL01A) */
color: #0084ff;
display: flex;
font-size: 14px;
margin-top: 20px;
/* @mixin bold; */
font-synthesis: style;
font-weight: 600;
}
html[data-theme="dark"] .ErrorPage-otherButtonContainer {
color: #3a76d0;
}
html[data-ios] .ErrorPage-otherButtonContainer {
font-weight: 500;
}
html[data-android] .ErrorPage-otherButtonContainer {
font-weight: 700;
}
.ErrorPage-otherButton {
align-items: center;
display: flex;
margin-left: 0.5em;
/* @mixin bold; */
font-synthesis: style;
font-weight: 600;
}
html[data-ios] .ErrorPage-otherButton {
font-weight: 500;
}
html[data-android] .ErrorPage-otherButton {
font-weight: 700;
}
.ErrorPage-errorImageContainer {
align-items: center;
display: flex;
height: 250px;
justify-content: center;
margin-left: 20px;
width: 250px;
}
@media (max-width: 690px) {
.ErrorPage-errorImageContainer {
margin-left: 0;
}
}
</style>
</head>
<body>
<div class="ErrorPage">
<div class="ErrorPage-container">
<div class="ErrorPage-text">
<h1 class="ErrorPage-title">404</h1>
<p class="ErrorPage-subtitle">你似乎来到了没有知识存在的荒原</p>
<a
class="Button Button--primary Button--blue ErrorPage-primaryButton"
href="https://www.zhihu.com"
>
去往首页
</a>
<div class="ErrorPage-otherButtonContainer">
或者
<button
class="Button Button--plain Button--blue ErrorPage-otherButton ErrorPage-goBackButton"
>
返回上页
<svg
class="Zi Zi--ArrowRight"
fill="currentColor"
viewBox="0 0 24 24"
width="24px"
height="24px"
>
<path
d="M9.218 16.78a.737.737 0 0 0 1.052 0l4.512-4.249a.758.758 0 0 0 0-1.063L10.27 7.22a.737.737 0 0 0-1.052 0 .759.759 0 0 0-.001 1.063L13 12l-3.782 3.716a.758.758 0 0 0 0 1.063z"
fill-rule="evenodd"
></path>
</svg>
</button>
</div>
</div>
<div class="ErrorPage-errorImageContainer">
<img
class="ErrorPage-errorImage"
src="//zhstatic.zhihu.com/assets/error/liukanshan_wire.svg"
alt="page error"
/>
</div>
</div>
</div>
<script
async
src="//unpkg.zhimg.com/za-js-sdk@latest/dist/zap.js"
onload="zap.trackPageShow()"
></script>
<script>
// https://stackoverflow.com/a/15724300
function getCookie(name) {
let value = "; " + document.cookie;
let parts = value.split("; " + name + "=");
if (parts.length == 2) {
return parts
.pop()
.split(";")
.shift();
}
return null;
}
if (navigator.userAgent.match(/iPhone|iPad|iPod/)) {
document.documentElement.dataset.ios = "";
}
if (navigator.userAgent.match(/Android/)) {
document.documentElement.dataset.android = "";
}
let queryTheme;
if (typeof URLSearchParams !== "undefined") {
// 这里不启用根据 query 等设置 cookie 的功能这种复杂逻辑
const params = new URLSearchParams(window.location.search);
queryTheme = params.get("theme");
}
const cookieTheme = getCookie("theme");
const theme = queryTheme || cookieTheme || "light";
document.documentElement.dataset.theme = theme;
const goBackButton = document.querySelector(".ErrorPage-goBackButton");
goBackButton.addEventListener("click", function(e) {
history.go(-1);
});
</script>
</body>
</html>
