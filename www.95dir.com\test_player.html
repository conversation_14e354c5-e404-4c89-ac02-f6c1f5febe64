<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐播放器测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
        #musicList { list-style: none; padding: 0; }
        #musicList li { padding: 10px; margin: 5px 0; background: #f5f5f5; border-radius: 3px; cursor: pointer; }
        #musicList li:hover { background: #e0e0e0; }
        #musicList li.active { background: #007cba; color: white; }
        .loading { color: #666; font-style: italic; }
        .debug-info { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 3px; font-size: 12px; }
    </style>
</head>
<body>
    <h1>音乐播放器功能测试</h1>
    
    <div class="test-section">
        <h2>1. AJAX接口测试</h2>
        <button onclick="testAjaxAPI()">测试音乐列表API</button>
        <div id="ajaxResult"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 音乐列表</h2>
        <ul id="musicList">
            <li class="loading">点击上面的按钮加载音乐列表...</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>3. 播放器测试</h2>
        <audio id="testPlayer" controls style="width: 100%;">
            您的浏览器不支持音频播放
        </audio>
        <div id="currentTrack">当前播放: 无</div>
    </div>
    
    <div class="test-section">
        <h2>4. 调试信息</h2>
        <div id="debugInfo"></div>
    </div>

    <script>
        let currentPlaylist = [];
        let currentIndex = 0;
        
        async function testAjaxAPI() {
            const resultDiv = document.getElementById('ajaxResult');
            const musicList = document.getElementById('musicList');
            const debugInfo = document.getElementById('debugInfo');
            
            resultDiv.innerHTML = '<div class="info">正在加载...</div>';
            
            try {
                const response = await fetch('?mod=ajaxget&type=music_list');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">✓ API调用成功，获取到 ${data.count} 首音乐</div>`;
                    
                    // 显示调试信息
                    if (data.debug) {
                        debugInfo.innerHTML = `
                            <div class="debug-info">
                                <strong>调试信息:</strong><br>
                                总文章数: ${data.debug.total_articles}<br>
                                包含MP3的文章数: ${data.debug.mp3_articles}<br>
                                已审核且包含MP3的文章数: ${data.debug.approved_mp3_articles}
                            </div>
                        `;
                    }
                    
                    // 显示音乐列表
                    if (data.music_list && data.music_list.length > 0) {
                        currentPlaylist = data.music_list;
                        renderMusicList();
                    } else {
                        musicList.innerHTML = '<li class="error">没有找到音乐</li>';
                    }
                    
                    if (data.message) {
                        resultDiv.innerHTML += `<div class="info">消息: ${data.message}</div>`;
                    }
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ API调用失败: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ 请求失败: ${error.message}</div>`;
                console.error('API调用错误:', error);
            }
        }
        
        function renderMusicList() {
            const musicList = document.getElementById('musicList');
            
            if (currentPlaylist.length === 0) {
                musicList.innerHTML = '<li class="error">播放列表为空</li>';
                return;
            }
            
            const html = currentPlaylist.map((track, index) => `
                <li onclick="playTrack(${index})" ${index === currentIndex ? 'class="active"' : ''}>
                    <strong>${track.title}</strong><br>
                    <small>${track.url}</small>
                </li>
            `).join('');
            
            musicList.innerHTML = html;
        }
        
        function playTrack(index) {
            if (index < 0 || index >= currentPlaylist.length) return;
            
            currentIndex = index;
            const track = currentPlaylist[index];
            const player = document.getElementById('testPlayer');
            const currentTrackDiv = document.getElementById('currentTrack');
            
            // 处理音频URL
            let audioUrl = processAudioUrl(track.url);
            
            player.src = audioUrl;
            currentTrackDiv.innerHTML = `当前播放: ${track.title}`;
            
            // 更新列表显示
            renderMusicList();
            
            // 尝试播放
            player.play().catch(error => {
                console.error('播放失败:', error);
                currentTrackDiv.innerHTML += ` <span class="error">(播放失败: ${error.message})</span>`;
            });
        }
        
        function processAudioUrl(url) {
            // 处理网易云音乐链接
            if (url.includes('music.163.com')) {
                const match = url.match(/id=(\d+)/);
                if (match) {
                    return `https://music.163.com/song/media/outer/url?id=${match[1]}`;
                }
            }
            
            // 对于直接的音频文件链接，直接返回
            if (url.match(/\.(mp3|m4a|wav|flac|aac|ogg)(\?.*)?$/i)) {
                return url;
            }
            
            // 默认返回原链接
            return url;
        }
        
        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，准备测试音乐播放器功能');
        });
    </script>
</body>
</html>
