<?php
/**
 * 在线统计功能测试脚本
 */

echo "<h1>在线统计功能测试</h1>\n";
echo "<p>测试时间：" . date('Y-m-d H:i:s') . "</p>\n";

// 测试1：检查文件权限
echo "<h2>1. 检查文件和目录权限</h2>\n";

$statsDir = 'data/online_stats';
$statsFile = $statsDir . '/online.php';
$logFile = $statsDir . '/.online_log';

if (!is_dir($statsDir)) {
    echo "<p style='color: red;'>❌ 统计目录不存在: {$statsDir}</p>\n";
} else {
    echo "<p style='color: green;'>✅ 统计目录存在: {$statsDir}</p>\n";
    
    if (!is_writable($statsDir)) {
        echo "<p style='color: orange;'>⚠️ 统计目录不可写</p>\n";
    } else {
        echo "<p style='color: green;'>✅ 统计目录可写</p>\n";
    }
}

if (!file_exists($statsFile)) {
    echo "<p style='color: red;'>❌ 统计脚本不存在: {$statsFile}</p>\n";
} else {
    echo "<p style='color: green;'>✅ 统计脚本存在: {$statsFile}</p>\n";
}

if (!file_exists($logFile)) {
    echo "<p style='color: orange;'>⚠️ 日志文件不存在，将自动创建: {$logFile}</p>\n";
} else {
    echo "<p style='color: green;'>✅ 日志文件存在: {$logFile}</p>\n";
    
    if (!is_writable($logFile)) {
        echo "<p style='color: orange;'>⚠️ 日志文件不可写</p>\n";
    } else {
        echo "<p style='color: green;'>✅ 日志文件可写</p>\n";
    }
}

// 测试2：检查日志文件内容
echo "<h2>2. 检查日志文件内容</h2>\n";

if (file_exists($logFile)) {
    $content = file_get_contents($logFile);
    $data = json_decode($content, true);
    
    if ($data === null) {
        echo "<p style='color: red;'>❌ 日志文件JSON格式错误</p>\n";
        echo "<p>文件内容: " . htmlspecialchars($content) . "</p>\n";
    } else {
        echo "<p style='color: green;'>✅ 日志文件JSON格式正确</p>\n";
        
        if (!isset($data['active']) || !isset($data['total'])) {
            echo "<p style='color: orange;'>⚠️ 日志文件结构不完整</p>\n";
        } else {
            echo "<p style='color: green;'>✅ 日志文件结构正确</p>\n";
            echo "<p>当前在线: " . count($data['active']) . " 人</p>\n";
            echo "<p>总访客: " . $data['total'] . " 人</p>\n";
        }
    }
}

// 测试3：模拟请求统计脚本
echo "<h2>3. 测试统计脚本响应</h2>\n";

// 设置模拟的服务器变量
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
$_SERVER['HTTP_USER_AGENT'] = 'Test Browser';

ob_start();
include $statsFile;
$response = ob_get_clean();

echo "<p>统计脚本响应: " . htmlspecialchars($response) . "</p>\n";

$responseData = json_decode($response, true);
if ($responseData === null) {
    echo "<p style='color: red;'>❌ 统计脚本返回的不是有效JSON</p>\n";
} else {
    echo "<p style='color: green;'>✅ 统计脚本返回有效JSON</p>\n";
    
    if (isset($responseData['online']) && isset($responseData['total'])) {
        echo "<p style='color: green;'>✅ 响应包含必要字段</p>\n";
        echo "<p>在线人数: " . $responseData['online'] . "</p>\n";
        echo "<p>总访客数: " . $responseData['total'] . "</p>\n";
    } else {
        echo "<p style='color: red;'>❌ 响应缺少必要字段</p>\n";
    }
}

// 测试4：检查前端JavaScript路径
echo "<h2>4. 检查前端调用路径</h2>\n";

$footerFile = 'themes/default/footer.html';
if (file_exists($footerFile)) {
    $footerContent = file_get_contents($footerFile);
    
    if (strpos($footerContent, '/data/online_stats/online.php') !== false) {
        echo "<p style='color: green;'>✅ 前端JavaScript调用路径正确</p>\n";
    } else {
        echo "<p style='color: red;'>❌ 前端JavaScript调用路径可能有误</p>\n";
    }
    
    if (strpos($footerContent, 'onlineCount') !== false && strpos($footerContent, 'totalVisitors') !== false) {
        echo "<p style='color: green;'>✅ 前端JavaScript包含必要的DOM元素ID</p>\n";
    } else {
        echo "<p style='color: red;'>❌ 前端JavaScript缺少必要的DOM元素ID</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ Footer模板文件不存在</p>\n";
}

// 测试5：建议修复方案
echo "<h2>5. 修复建议</h2>\n";

echo "<ul>\n";
echo "<li>确保 data/online_stats 目录有写权限</li>\n";
echo "<li>检查服务器是否支持file_get_contents和file_put_contents函数</li>\n";
echo "<li>确保PHP错误日志中没有相关错误信息</li>\n";
echo "<li>在浏览器开发者工具中检查网络请求是否成功</li>\n";
echo "<li>如果问题持续，可以手动访问 /data/online_stats/online.php 查看响应</li>\n";
echo "</ul>\n";

echo "<h2>6. 手动测试链接</h2>\n";
echo "<p><a href='/data/online_stats/online.php' target='_blank'>点击测试统计脚本</a></p>\n";
echo "<p><a href='/' target='_blank'>返回首页查看效果</a></p>\n";

?>
