<?php
/**
 * 链接检测诊断脚本
 * 用于诊断和修复链接检测失败问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>链接检测诊断工具</h1>";

// 1. 检查基本环境
echo "<h2>1. 环境检查</h2>";
echo "<p>PHP版本: " . PHP_VERSION . "</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";

// 2. 检查 cURL 扩展
echo "<h2>2. cURL 扩展检查</h2>";
if (extension_loaded('curl')) {
    echo "<p style='color: green;'>✓ cURL 扩展已加载</p>";
    $curl_version = curl_version();
    echo "<p>cURL 版本: " . $curl_version['version'] . "</p>";
} else {
    echo "<p style='color: red;'>✗ cURL 扩展未加载</p>";
}

// 3. 检查缓存目录
echo "<h2>3. 缓存目录检查</h2>";
$cache_dir = __DIR__ . '/module/cache';
if (is_dir($cache_dir)) {
    echo "<p style='color: green;'>✓ 缓存目录存在: $cache_dir</p>";
    if (is_writable($cache_dir)) {
        echo "<p style='color: green;'>✓ 缓存目录可写</p>";
    } else {
        echo "<p style='color: red;'>✗ 缓存目录不可写</p>";
    }
} else {
    echo "<p style='color: red;'>✗ 缓存目录不存在: $cache_dir</p>";
    if (mkdir($cache_dir, 0755, true)) {
        echo "<p style='color: green;'>✓ 已创建缓存目录</p>";
    } else {
        echo "<p style='color: red;'>✗ 无法创建缓存目录</p>";
    }
}

// 4. 检查 APCu 扩展
echo "<h2>4. APCu 缓存检查</h2>";
if (function_exists('apcu_fetch')) {
    echo "<p style='color: green;'>✓ APCu 扩展可用</p>";
} else {
    echo "<p style='color: orange;'>⚠ APCu 扩展不可用，将使用文件缓存</p>";
}

// 5. 测试状态检测接口
echo "<h2>5. 状态检测接口测试</h2>";

// 测试 URL 列表
$test_urls = [
    'https://www.baidu.com',
    'https://www.95dir.com/?mod=webdir',
    'https://www.95dir.com/?mod=siteinfo&wid=481'
];

foreach ($test_urls as $test_url) {
    echo "<h3>测试 URL: $test_url</h3>";
    
    // 直接调用检测函数
    $result = test_url_status($test_url);
    echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
}

// 6. 检查 JavaScript 端点
echo "<h2>6. JavaScript 端点检查</h2>";
$status_check_file = __DIR__ . '/module/status_check.php';
if (file_exists($status_check_file)) {
    echo "<p style='color: green;'>✓ status_check.php 文件存在</p>";
    if (is_readable($status_check_file)) {
        echo "<p style='color: green;'>✓ status_check.php 文件可读</p>";
    } else {
        echo "<p style='color: red;'>✗ status_check.php 文件不可读</p>";
    }
} else {
    echo "<p style='color: red;'>✗ status_check.php 文件不存在</p>";
}

// 7. 网络连接测试
echo "<h2>7. 网络连接测试</h2>";
$test_connection = test_network_connection();
if ($test_connection['success']) {
    echo "<p style='color: green;'>✓ 网络连接正常</p>";
} else {
    echo "<p style='color: red;'>✗ 网络连接异常: " . $test_connection['error'] . "</p>";
}

/**
 * 测试 URL 状态的函数
 */
function test_url_status($url) {
    $start_time = microtime(true);
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL            => $url,
        CURLOPT_NOBODY         => true,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HEADER         => true,
        CURLOPT_FOLLOWLOCATION => false,
        CURLOPT_CONNECTTIMEOUT => 3,
        CURLOPT_TIMEOUT        => 6,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_USERAGENT      => 'StatusCheckBot/1.0'
    ]);
    
    $head = curl_exec($ch);
    $response_time = (int)round(1000 * (microtime(true) - $start_time));
    
    if ($head === false) {
        $error = curl_error($ch);
        curl_close($ch);
        return [
            'success' => false,
            'error' => $error,
            'response_time' => $response_time
        ];
    }
    
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $server = '';
    if (preg_match('/\r\nServer:\s*([^\r\n]+)/i', $head, $m)) {
        $server = trim($m[1]);
    }
    
    curl_close($ch);
    
    return [
        'success' => true,
        'status' => $http_code,
        'server' => $server,
        'response_time' => $response_time
    ];
}

/**
 * 测试网络连接
 */
function test_network_connection() {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => 'https://www.baidu.com',
        CURLOPT_NOBODY => true,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CONNECTTIMEOUT => 5,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSL_VERIFYPEER => false
    ]);
    
    $result = curl_exec($ch);
    if ($result === false) {
        $error = curl_error($ch);
        curl_close($ch);
        return ['success' => false, 'error' => $error];
    }
    
    curl_close($ch);
    return ['success' => true];
}

echo "<h2>8. 修复建议</h2>";
echo "<ul>";
echo "<li>如果 cURL 扩展未加载，请联系服务器管理员安装 php-curl 扩展</li>";
echo "<li>如果缓存目录不可写，请设置正确的目录权限: chmod 755 module/cache</li>";
echo "<li>如果网络连接异常，请检查服务器的网络配置和防火墙设置</li>";
echo "<li>如果状态检测接口无法访问，请检查 .htaccess 重写规则</li>";
echo "</ul>";

echo "<p><strong>完成诊断。</strong></p>";
?>
