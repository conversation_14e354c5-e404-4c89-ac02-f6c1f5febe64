<?php
    $t_url = isset($_GET['url']) ? $_GET['url'] : '';
    $web_id = isset($_GET['id']) ? intval($_GET['id']) : 0; // 网站ID参数

    // 初始化默认值
    $url = 'https://www.95dir.com';
    $title = '参数缺失，正在返回首页...';

    // 记录今日出站点击统计（按照系统标准方式初始化）
    if ($web_id > 0) {
        try {
            // 按照系统标准方式初始化
            define('IN_IWEBDIR', true);
            define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
            define('APP_PATH', ROOT_PATH.'source/');

            // 引入系统初始化文件
            require(APP_PATH.'init.php');

            // 现在$DB应该已经可用了
            if (isset($DB) && is_object($DB)) {
                // 更新网站的总出站统计（保持兼容性）
                $webdata_table = $DB->table('webdata');
                $DB->query("UPDATE $webdata_table SET web_outstat=web_outstat+1 WHERE web_id='$web_id' LIMIT 1");

                // 记录今日出站点击到spider_stats表
                $today = date('Y-m-d');
                $spider_table = $DB->table('spider_stats');

                // 检查今天的记录是否存在
                $today_record = $DB->fetch_one("SELECT id FROM $spider_table WHERE stat_date = '$today'");

                if ($today_record) {
                    // 更新今日出站次数
                    $DB->query("UPDATE $spider_table SET total_outlinks = total_outlinks + 1 WHERE stat_date = '$today'");
                } else {
                    // 创建今日记录
                    $DB->query("INSERT INTO $spider_table (stat_date, total_outlinks) VALUES ('$today', 1)");
                }
            }
        } catch (Exception $e) {
            // 如果统计出错，不影响跳转功能，继续执行
        }
    }

    if(!empty($t_url)) {
        preg_match('/(http|https):\/\//',$t_url,$matches);
        if($matches){
            $url=$t_url;
            $title='网站转跳中，请耐心等待...';
        } else {
            preg_match('/\./i',$t_url,$matche);
            if($matche){
                $url='http://'.$t_url;
                $title='网站转跳中，请耐心等待...';
            } else {
                $url='https://www.95dir.com';
                $title='参数错误 正在返回首页~~~';
            }
        }
    } else {
        $title='参数缺失，正在返回首页...';
        $url='https://www.95dir.com';
    }
    ?>
    <html>
    <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />    
    <meta name="robots" content="noindex">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title><?php echo $url;?>页面跳转中,请稍候...</title>
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="refresh" content="1;url='<?php echo $url;?>';">
    <meta name="Description" content="<?php echo $url;?>的安全跳转提醒" />
   <style type="text/css">
        body,
        h1,
        p {
            margin: 0;
            padding: 0;
        }

        a {
            text-decoration: none;
        }

        button {
            padding: 0;
            font-family: inherit;
            background: none;
            border: none;
            outline: none;
            cursor: pointer;
        }

        html {
            width: 100%;
            height: 100%;
        }

        body {
            padding-top: 20px;
            color: #222;
            font-size: 13px;
            font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
            line-height: 1.5;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
        }

        @media (max-width: 620px) {
            body {
                font-size: 16px;
            }
        }

        .button {
            display: inline-block;
            padding: 8px;
            color: #fff;
            font-size: 14px;
            line-height: 1;
            background-color: #0077d9;
            border-radius: 3px;
        }
       .buttonzuo {
            display: inline-block;
            padding: 8px;
            color: #fff;
            float: left;
            font-size: 14px;
            line-height: 1;
            background-color: #f44336;
            border-radius: 3px;
        }

        @media (max-width: 620px) {
            .button {
            }
        }

        .button:hover {
            background-color: #0070cd;
        }

        .button:active {
            background-color: #0077d9;
        }

        .link-button {
            color: #105cb6;
            font-size: 13px;
        }

        @media (max-width: 620px) {
            .link-button {
                font-size: 15px;
            }
        }

        .logo{text-align:center;}
        .wrapper {
            margin: auto;
            padding-left: 15px;
            padding-right: 15px;
            max-width: 540px;
        }

        .info {
            margin: auto;
            padding-top: 10px;
        }
        .wrapper {
            padding-top: 25px;
            padding-bottom: 25px;
            background-color: #f7f7f7;
            border: 1px solid #babbbc;
            border-radius: 5px;
        }

        @media (max-width: 620px) {
            .logo,
            .wrapper {
                margin: 0 10px;
            }
        }

        h1 {
            margin-bottom: 12px;
            font-size: 16px;
            font-weight: 700;
            line-height: 1;
        }

        @media (max-width: 620px) {
            h1 {
                font-size: 18px;
            }
        }

        .warning {
            color: #c33;
        }

        .link {
            color: #0077d9;
            margin-top: 12px;
            word-wrap: normal;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
        }
        .link a {color: #0077d9;
           
        }
        .link.is-expanded {
            word-wrap: break-word;
            white-space: normal;
        }

        .actions {
            margin-top: 15px;
            padding-top: 15px;
            text-align: right;
            border-top: 1px solid #d8d8d8;
        }

        .actions .link-button + .link-button {
            margin-left: 30px;
        }
        

        .tip {
            position: relative;
            display: none;
            margin-top: 30px;
            padding-top: 18px;
            color: #999;
            text-align: left;
            background: #f7f7f7;
            border-top: 1px solid #d8d8d8;
            opacity: 0;
            transition: opacity .2s ease-out;
        }

        .tip.is-visible {
            opacity: 1;
        }

        .tip:after, .tip:before {
            position: absolute;
            bottom: 100%;
            right: 5em;
            content: " ";
            height: 0;
            width: 0;
            border: solid transparent;
            pointer-events: none;
        }

        .tip:after {
            margin-right: -6px;
            border-color: rgba(247, 247, 247, 0);
            border-bottom-color: #f7f7f7;
            border-width: 6px;
        }

        .tip:before {
            margin-right: -7px;
            border-color: rgba(216, 216, 216, 0);
            border-bottom-color: #d2d2d2;
            border-width: 7px;
        }
    </style>
    <div class="logo">
    <a href="https://www.95dir.com/">
        <img style="width: 150px;height: 150px;" src="/logo.png"  alt="95分类目录">
    </a>
</div>


    </head>
    <body>
        <div class="wrapper">
    <div class="content">
        <h1><img class="www_36_lc loading-img"  width="24"
    height="24" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAADWUlEQVRoQ+2YvWsUQRiH33fmOMQiRQor7RQSweB9KAQEg1iYKFaZnJJ/wcTOMhLs7Ez8FwLebikasTGCoJDcHSoYQbHRyiKFhajczCu3SS73sbfzsXNJDnLV3s4y83vm3X12dhAG/IcDnh+OAHpVkITISvg2DYyQjwyVcXGt3o9q960CdZELAWC6ERoRn/CgentgAGimOCVJPmsNzJFfx2DjuW8I7xWg+xMZ9enXRwIaaQ2LgJ/Z2aFzvm8l7wBypjBPpB7FzTQiu8uDypLPKngFIDE+rODPFwIYjgUA2GJw7AyGb7d8QXgFkCK/TEB3ksIh4GMeVucOHQCV8qNKwQcCymgA6ozBGJarmz4gvFWgLvKrAHTNLBS+yITVSbNrk6/yAhCnTV04X1pNDdBLmzoAX1pNDZCkTS2EB62mAtBpUwvgQaupAEy0qYdIp1VnAFNtGgCk0qozgJ02tRjOWnUCoFJxUiqZuLJEgB+AbCWKTmqWAE4mYXDGp7C8sapD7Wy3BjDVJke8iUH1aZS/lL8hFUXHvX6uWrUGMNUmByxiWK1sAxTyUqnoOBHCQatWADbadAJw0KoVgJwpLBEpo5WkC0CjOohsmQeVeV21dtuNAWy16QwAaKVVYwBbbboCbM+s+WrVCMBEm50lTwcAYKpVLYCpNrsALDXa7XezTQAtgKk2Y14wVi+yuIfWZBMgEcBGm6bWsLkODbSaCGCjTZtgNtfqtNoTwFabMffwb0DYWQvBLAEdtwm+5/lkrfYEsNVmazgElIzxcSyvr0dLiVu5opL4joC4C0SSVmMBXLTZBoDwnge1863npMjVCKDtnA1ML612Abhqs70C8JPB6VMYhv+iCgiRVfD1OwGcsAndPim4yUaHxjr3VrsAZCk3RwpS718i4gpjdK8RQil8SESzruGbzwODeV6uLXdM1t7fg9amDjBOq20VOAza1EJ0rFabAGm1qRvYVzt2rFabAHWRa3zjetmv9BU2oZ/VTFibarRHACQuXJZQX9uHgb0NwZFdwaDyKgKQorBAoBa99b4PHSHCAx7UFnYqULikUL0mArYPY6ceInrTA05gWHmz9xCL4kWF6ioAZFOP0NcO8C8jerm746H9HuhrFg+dHwF4mMRUXQx8Bf4DeBHHQHvQneAAAAAASUVORK5CYII=" alt="警示" loading="lazy">95分类目录温馨提示：请注意您的账号和财产安全。</h1>
        <p class="info">您即将离开本站，前往：<a class="link"><?php echo $url;?></a>
        </p>
        <p class="info">赞助本站赠送永久快审、显示直链、硬链直达、免跳转。
        <a href="tencent://message/?uin=3632094&site=qq&menu=yes" target="blank" rel="nofollow">
          <img src="public/images/qq.gif" alt="联系QQ" style="display: unset;" loading="lazy"></a>
        </p>
        <p class="info">3秒后自动跳转...</a>
        </p>
    </div>
    <div class="actions">
     <a class="buttonzuo" href="#" onclick="self.location=document.referrer;">返回</a>
     <a class="button" href="<?php echo $url;?>">立即访问</a>
    </div>
</div>

<script>
  function countDown(){
    var time = document.getElementById("Time");
    if (time) {
      if (time.innerHTML === "0") {
        window.location.replace("<?php echo $url;?>")
        clearInterval(intervalId);
      } else {
        time.innerHTML = time.innerHTML - 1;
      }
    }
  }
  const intervalId = window.setInterval("countDown()", 1000);
</script>
    </body>
    </html>