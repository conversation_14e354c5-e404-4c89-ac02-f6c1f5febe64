<?php
/**
 * 付费系统数据库初始化脚本
 * 用于创建付费记录表和添加必要的字段
 */

require('common.php');

$pagetitle = '付费系统初始化';
$tempfile = 'payment_init.html';

if (!isset($action)) $action = 'init';

if ($action == 'init') {
    echo "<h2>付费系统数据库初始化</h2>";
    echo "<p>正在检查和创建必要的数据库表和字段...</p>";
    
    $errors = array();
    $success = array();
    
    try {
        // 1. 创建付费记录表
        $payment_table = $DB->table('payment_records');
        $create_payment_sql = "CREATE TABLE IF NOT EXISTS `{$payment_table}` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `web_id` int(10) unsigned NOT NULL DEFAULT '0',
            `web_name` varchar(100) NOT NULL DEFAULT '',
            `web_url` varchar(255) NOT NULL DEFAULT '',
            `payment_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '付费类型：1=VIP，2=推荐，3=快审',
            `payment_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '付费金额',
            `payment_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '付费时间',
            `expire_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '到期时间',
            `operator` varchar(50) NOT NULL DEFAULT 'admin' COMMENT '操作员',
            `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1=有效，0=已过期',
            `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
            `created_at` int(10) unsigned NOT NULL DEFAULT '0',
            PRIMARY KEY (`id`),
            KEY `web_id` (`web_id`),
            KEY `payment_type` (`payment_type`),
            KEY `payment_time` (`payment_time`),
            KEY `expire_time` (`expire_time`),
            KEY `status` (`status`)
        ) ENGINE=MyISAM DEFAULT CHARSET=utf8";
        
        if ($DB->query($create_payment_sql)) {
            $success[] = "付费记录表创建成功";
        } else {
            $errors[] = "付费记录表创建失败";
        }

        // 2. 创建价格配置表
        $price_table = $DB->table('payment_prices');
        $create_price_sql = "CREATE TABLE IF NOT EXISTS `{$price_table}` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `service_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '服务类型：1=VIP，2=推荐，3=快审',
            `service_name` varchar(50) NOT NULL DEFAULT '' COMMENT '服务名称',
            `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '价格',
            `unit` varchar(20) NOT NULL DEFAULT '' COMMENT '单位：年/月/次',
            `duration_days` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '服务时长（天数）',
            `effective_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '生效时间',
            `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1=有效，0=已停用',
            `operator` varchar(50) NOT NULL DEFAULT 'admin' COMMENT '操作员',
            `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
            `created_at` int(10) unsigned NOT NULL DEFAULT '0',
            PRIMARY KEY (`id`),
            KEY `service_type` (`service_type`),
            KEY `effective_time` (`effective_time`),
            KEY `status` (`status`)
        ) ENGINE=MyISAM DEFAULT CHARSET=utf8";

        if ($DB->query($create_price_sql)) {
            $success[] = "价格配置表创建成功";

            // 插入默认价格配置
            $current_time = time();
            $default_prices = array(
                array(
                    'service_type' => 1,
                    'service_name' => 'VIP服务',
                    'price' => 30.00,
                    'unit' => '年',
                    'duration_days' => 365,
                    'effective_time' => $current_time,
                    'status' => 1,
                    'operator' => 'system',
                    'remark' => '默认VIP价格配置',
                    'created_at' => $current_time
                ),
                array(
                    'service_type' => 2,
                    'service_name' => '推荐服务',
                    'price' => 10.00,
                    'unit' => '年',
                    'duration_days' => 365,
                    'effective_time' => $current_time,
                    'status' => 1,
                    'operator' => 'system',
                    'remark' => '默认推荐价格配置',
                    'created_at' => $current_time
                ),
                array(
                    'service_type' => 3,
                    'service_name' => '快审服务',
                    'price' => 5.00,
                    'unit' => '次',
                    'duration_days' => 0,
                    'effective_time' => $current_time,
                    'status' => 1,
                    'operator' => 'system',
                    'remark' => '默认快审价格配置',
                    'created_at' => $current_time
                )
            );

            foreach ($default_prices as $price_data) {
                $DB->insert($price_table, $price_data);
            }
            $success[] = "默认价格配置插入成功";
        } else {
            $errors[] = "价格配置表创建失败";
        }
        
        // 3. 检查并添加网站表的到期时间字段
        $websites_table = $DB->table('websites');
        
        // 检查字段是否存在
        $check_fields = array(
            'web_vip_expire' => 'VIP到期时间',
            'web_recommend_expire' => '推荐到期时间',
            'web_fast_expire' => '快审到期时间'
        );
        
        foreach ($check_fields as $field => $desc) {
            $check_sql = "SHOW COLUMNS FROM `{$websites_table}` LIKE '{$field}'";
            $result = $DB->query($check_sql);
            
            if ($DB->num_rows($result) == 0) {
                // 字段不存在，添加字段
                $alter_sql = "ALTER TABLE `{$websites_table}` ADD COLUMN `{$field}` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '{$desc}'";
                if ($DB->query($alter_sql)) {
                    $success[] = "网站表添加{$desc}字段成功";
                } else {
                    $errors[] = "网站表添加{$desc}字段失败";
                }
            } else {
                $success[] = "网站表{$desc}字段已存在";
            }
        }
        
        // 4. 初始化现有VIP网站的到期时间
        $vip_websites = $DB->fetch_all("SELECT web_id, web_ctime FROM {$websites_table} WHERE web_ispay = 1 AND web_vip_expire = 0");
        foreach ($vip_websites as $website) {
            $expire_time = $website['web_ctime'] + (365 * 24 * 3600); // 默认一年
            $DB->update($websites_table, array('web_vip_expire' => $expire_time), array('web_id' => $website['web_id']));
        }
        if (count($vip_websites) > 0) {
            $success[] = "初始化了 " . count($vip_websites) . " 个VIP网站的到期时间";
        }
        
        // 5. 初始化现有推荐网站的到期时间
        $recommend_websites = $DB->fetch_all("SELECT web_id, web_ctime FROM {$websites_table} WHERE web_istop = 1 AND web_recommend_expire = 0");
        foreach ($recommend_websites as $website) {
            $expire_time = $website['web_ctime'] + (30 * 24 * 3600); // 默认30天
            $DB->update($websites_table, array('web_recommend_expire' => $expire_time), array('web_id' => $website['web_id']));
        }
        if (count($recommend_websites) > 0) {
            $success[] = "初始化了 " . count($recommend_websites) . " 个推荐网站的到期时间";
        }
        
    } catch (Exception $e) {
        $errors[] = "初始化过程中发生错误: " . $e->getMessage();
    }
    
    // 显示结果
    echo "<h3>初始化结果</h3>";
    
    if (!empty($success)) {
        echo "<div style='color: green; background: #f0f8f0; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>成功:</h4>";
        echo "<ul>";
        foreach ($success as $msg) {
            echo "<li>{$msg}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (!empty($errors)) {
        echo "<div style='color: red; background: #f8f0f0; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>错误:</h4>";
        echo "<ul>";
        foreach ($errors as $msg) {
            echo "<li>{$msg}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (empty($errors)) {
        echo "<div style='color: blue; background: #f0f0f8; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>初始化完成！</h4>";
        echo "<p>付费系统已成功初始化，您现在可以使用以下功能：</p>";
        echo "<ul>";
        echo "<li><a href='payment_stats.php' target='_blank'>付费统计</a> - 查看所有付费记录和统计信息</li>";
        echo "<li><a href='payment_manage.php' target='_blank'>付费管理</a> - 管理VIP和推荐网站，支持续费操作</li>";
        echo "<li><a href='payment_price.php' target='_blank'>价格配置</a> - 管理各种付费服务的价格设置</li>";
        echo "<li><a href='payment_cron.php' target='_blank'>过期检查</a> - 手动执行过期检查和处理</li>";
        echo "</ul>";
        echo "<p><strong>建议设置定时任务：</strong></p>";
        echo "<p>在服务器上设置crontab定时任务，每天自动执行过期检查：</p>";
        echo "<code>0 2 * * * /usr/bin/php " . dirname(__FILE__) . "/payment_cron.php</code>";
        echo "</div>";
    }
    
    echo "<p><a href='admin.php' target='_top'>返回管理首页</a></p>";
}
?>
