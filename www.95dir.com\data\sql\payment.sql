-- 支付订单表
CREATE TABLE IF NOT EXISTS `dir_payment_orders` (
  `order_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL DEFAULT '0',
  `web_id` int(10) unsigned NOT NULL DEFAULT '0',
  `order_no` varchar(32) NOT NULL DEFAULT '',
  `trade_no` varchar(64) NOT NULL DEFAULT '',
  `payment_type` enum('recommend','quick_review','vip') NOT NULL DEFAULT 'recommend',
  `pay_type` varchar(20) NOT NULL DEFAULT '',
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `web_name` varchar(100) NOT NULL DEFAULT '',
  `web_url` varchar(255) NOT NULL DEFAULT '',
  `web_tags` varchar(100) NOT NULL DEFAULT '',
  `web_intro` text NOT NULL,
  `cate_id` smallint(5) unsigned NOT NULL DEFAULT '0',
  `notify_url` varchar(255) NOT NULL DEFAULT '',
  `return_url` varchar(255) NOT NULL DEFAULT '',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0',
  `pay_time` int(10) unsigned NOT NULL DEFAULT '0',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `user_id` (`user_id`),
  KEY `web_id` (`web_id`),
  KEY `status` (`status`),
  KEY `create_time` (`create_time`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='支付订单表';

-- 支付配置表
CREATE TABLE IF NOT EXISTS `dir_payment_config` (
  `config_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `merchant_id` varchar(50) NOT NULL DEFAULT '',
  `merchant_key` varchar(100) NOT NULL DEFAULT '',
  `api_url` varchar(255) NOT NULL DEFAULT '',
  `notify_url` varchar(255) NOT NULL DEFAULT '',
  `return_url` varchar(255) NOT NULL DEFAULT '',
  `recommend_price` decimal(10,2) NOT NULL DEFAULT '10.00',
  `quick_review_price` decimal(10,2) NOT NULL DEFAULT '20.00',
  `vip_price` decimal(10,2) NOT NULL DEFAULT '50.00',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`config_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='支付配置表';

-- 插入默认配置
INSERT INTO `dir_payment_config` (`merchant_id`, `merchant_key`, `api_url`, `notify_url`, `return_url`, `recommend_price`, `quick_review_price`, `vip_price`, `status`, `create_time`) VALUES
('1007', 'ScxVfWEfqMqJlLy5jkLROhSmWlVgEUds', 'https://pay.chaobie.com/', '', '', 10.00, 20.00, 50.00, 1, UNIX_TIMESTAMP());
