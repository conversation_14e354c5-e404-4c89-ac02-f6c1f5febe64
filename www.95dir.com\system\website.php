<?php
/*
 * <AUTHOR>  　 @祥💥　技术支持
 * @Mail         : <EMAIL>
 * @Date         : 2025-02-11 08:56:17
 * @LastEditTime : 2025-02-12 11:44:53
 * @LastEditors  :  　 @祥💥　技术支持
 * @Description  : 
 * @FilePath     : \35dir\system\website.php
 * It's up to you ^_^
 * Copyright (c) 2025 by <EMAIL>, All Rights Reserved. 
 */
require('common.php');
require(APP_PATH.'module/category.php');
require(APP_PATH.'module/website.php');
require(APP_PATH.'module/webdata.php');

/**
 * 智能显示网站链接状态
 * 简化逻辑，直接显示数据库状态
 */
function get_smart_link_status($web, $options) {
    // 1. 付费网站不检测友情链接
    if ($web['web_ispay'] > 0) {
        return '<font color="#ff6600" title="付费网站不检测友情链接">已收款</font>';
    }

    // 2. 根据网站状态显示不同的链接状态
    switch ($web['web_status']) {
        case 1: // 黑名单
            return '<font color="#666666" title="黑名单网站">已拉黑</font>';

        case 2: // 待审核
            return '<font color="#ff9900" title="待审核网站">待审核</font>';

        case 3: // 已审核
            // 检查友情链接检测是否启用
            $linkcheck_enabled = isset($options['is_enabled_linkcheck']) && $options['is_enabled_linkcheck'] == 'yes';
            $has_check_config = !empty($options['check_link_url']) && !empty($options['check_link_name']);

            if (!$linkcheck_enabled || !$has_check_config) {
                // 友情链接检测未启用或未配置
                return '<font color="#999999" title="友情链接检测未启用">未检测</font>';
            }

            // 直接显示数据库中的链接状态
            $status_text = $web['web_islink'] > 0 ? '未链接' : '已链接';
            $color = $web['web_islink'] > 0 ? '#ff0000' : '#00aa00';

            // 显示最后检测时间
            $last_check = $web['web_utime'] ?: 0;
            if ($last_check > 0) {
                $days_ago = floor((time() - $last_check) / (24 * 3600));
                $title = "最后检测: {$days_ago}天前";
            } else {
                $title = "从未检测";
            }

            return "<font color=\"$color\" title=\"$title\">$status_text</font>";

        case 4: // 审核不通过
            return '<font color="#cc6600" title="审核不通过网站">未通过</font>';

        default:
            return '<font color="#999999">未知状态</font>';
    }
}





/**
 * 自动发布文章功能
 * 在网站提交成功后，将网站的AI简介内容发布为文章
 */
function auto_publish_article_from_website($web_id, $web_name, $web_url, $web_tags, $web_intro, $web_ai_intro, $web_cate_id) {
	global $DB, $myself, $options;

	// 获取文章分类ID，优先使用与网站相同的分类，如果不存在则使用默认分类
	$article_cate_id = get_article_category_for_website($web_cate_id);

	// 构建文章数据
	$art_title = $web_name . '官方网站入口'; // 文章标题：网站名称 + 官方网站入口
	$art_tags = $web_tags . ',' . $web_name . '官方网站入口'; // 文章标签：网站标签 + 网站名称官方网站入口
	$art_intro = $web_intro; // 文章简介使用网站简介

	// 构建文章内容，在AI简介前添加网站截图
	$web_url_clean = str_replace(array('http://', 'https://'), '', $web_url);
	$web_url_clean = rtrim($web_url_clean, '/'); // 去除末尾斜杠
	$screenshot_img = '<img src="https://s0.wp.com/mshots/v1/https://' . $web_url_clean . '" width="130" height="110" alt="' . htmlspecialchars($web_name) . '" style="float:left; margin:0 10px 10px 0;">';
	$art_content = $screenshot_img . $web_ai_intro; // 文章内容：截图 + AI简介

	$art_time = time();

	// 处理标签格式
	$art_tags = str_replace('|', ',', $art_tags);
	$art_tags = str_replace('、', ',', $art_tags);
	$art_tags = str_replace('，', ',', $art_tags);
	$art_tags = str_replace(',,', ',', $art_tags);
	if (substr($art_tags, -1) == ',') {
		$art_tags = substr($art_tags, 0, strlen($art_tags) - 1);
	}

	// 检查文章是否已存在
	$query = $DB->query("SELECT art_id FROM ".$DB->table('articles')." WHERE art_title='".addslashes($art_title)."'");
	if ($DB->num_rows($query)) {
		return false; // 文章已存在，不重复创建
	}

	// 构建文章数据数组
	$art_data = array(
		'user_id' => isset($myself['user_id']) ? $myself['user_id'] : 1, // 如果没有用户信息，使用管理员ID
		'cate_id' => $article_cate_id,
		'art_title' => addslashes($art_title),
		'art_tags' => addslashes($art_tags),
		'copy_from' => '本站原创',
		'copy_url' => $options['site_url'],
		'art_intro' => addslashes(strip_tags($art_intro)),
		'art_content' => addslashes($art_content),
		'art_views' => 0,
		'art_ispay' => 0,
		'art_istop' => 0,
		'art_isbest' => 0,
		'art_status' => 3, // 直接设为已发布状态
		'art_ctime' => $art_time,
	);

	// 插入文章数据
	$DB->insert($DB->table('articles'), $art_data);
	$art_id = $DB->insert_id();

	// 更新分类文章数量
	$DB->query("UPDATE ".$DB->table('categories')." SET cate_postcount=cate_postcount+1 WHERE cate_mod='article' AND cate_id=$article_cate_id");

	return $art_id;
}

/**
 * 获取网站对应的文章分类ID
 * 后台管理员提交的文章统一使用分类ID 26
 * 如果分类26不存在，则使用其他可用分类
 */
function get_article_category_for_website($web_cate_id) {
	global $DB;

	// 后台管理员提交的文章统一使用分类ID 26
	$target_cate_id = 26;

	// 检查分类26是否存在且为文章分类
	$query = $DB->query("SELECT cate_id FROM ".$DB->table('categories')." WHERE cate_mod='article' AND cate_id=$target_cate_id LIMIT 1");
	if ($DB->num_rows($query)) {
		return $target_cate_id;
	}

	// 如果分类26不存在，尝试获取与网站分类同名的文章分类
	$web_cate = get_one_category($web_cate_id);
	if ($web_cate) {
		$query = $DB->query("SELECT cate_id FROM ".$DB->table('categories')." WHERE cate_mod='article' AND cate_name='".addslashes($web_cate['cate_name'])."' LIMIT 1");
		if ($DB->num_rows($query)) {
			$row = $DB->fetch_array($query);
			return $row['cate_id'];
		}
	}

	// 如果没有找到对应的文章分类，使用第一个可用的文章分类
	$query = $DB->query("SELECT cate_id FROM ".$DB->table('categories')." WHERE cate_mod='article' AND cate_childcount=0 ORDER BY cate_id ASC LIMIT 1");
	if ($DB->num_rows($query)) {
		$row = $DB->fetch_array($query);
		return $row['cate_id'];
	}

	// 如果还是没有找到，返回26作为默认值
	return 26;
}

/**
 * 同步更新文章功能
 * 当网站信息修改后，同步更新对应的文章
 */
function sync_update_article_from_website($web_id, $web_name, $web_url, $web_tags, $web_intro, $web_ai_intro, $web_cate_id) {
	global $DB, $options;

	// 查找是否存在对应的文章（通过文章标题匹配网站名称，支持带"官方网站入口"后缀的标题）
	$web_name_with_suffix = $web_name . '官方网站入口';
	$query = $DB->query("SELECT art_id, cate_id FROM ".$DB->table('articles')." WHERE art_title='".addslashes($web_name_with_suffix)."' OR art_title='".addslashes($web_name)."' LIMIT 1");
	if (!$DB->num_rows($query)) {
		// 如果没有找到对应的文章，且有AI简介，则创建新文章
		if (!empty($web_ai_intro)) {
			return auto_publish_article_from_website($web_id, $web_name, $web_url, $web_tags, $web_intro, $web_ai_intro, $web_cate_id);
		}
		return false;
	}

	$article = $DB->fetch_array($query);
	$art_id = $article['art_id'];

	// 如果AI简介为空，不更新文章
	if (empty($web_ai_intro)) {
		return false;
	}

	// 获取文章分类ID（后台管理员统一使用分类26）
	$article_cate_id = get_article_category_for_website($web_cate_id);

	// 处理标签格式，并添加"网站名称官方网站入口"标签
	$art_tags = str_replace('|', ',', $web_tags);
	$art_tags = str_replace('、', ',', $art_tags);
	$art_tags = str_replace('，', ',', $art_tags);
	$art_tags = str_replace(',,', ',', $art_tags);
	if (substr($art_tags, -1) == ',') {
		$art_tags = substr($art_tags, 0, strlen($art_tags) - 1);
	}
	$art_tags = $art_tags . ',' . $web_name . '官方网站入口'; // 添加网站名称+官方网站入口标签

	// 构建文章内容，在AI简介前添加网站截图
	$web_url_clean = str_replace(array('http://', 'https://'), '', $web_url);
	$web_url_clean = rtrim($web_url_clean, '/'); // 去除末尾斜杠
	$screenshot_img = '<img src="https://s0.wp.com/mshots/v1/https://' . $web_url_clean . '" width="130" height="110" alt="' . htmlspecialchars($web_name) . '" style="float:left; margin:0 10px 10px 0;">';
	$art_content = $screenshot_img . $web_ai_intro; // 文章内容：截图 + AI简介

	// 构建更新数据
	$art_data = array(
		'cate_id' => $article_cate_id,
		'art_title' => addslashes($web_name . '官方网站入口'), // 标题添加固定后缀
		'art_tags' => addslashes($art_tags),
		'art_intro' => addslashes(strip_tags($web_intro)),
		'art_content' => addslashes($art_content),
	);

	// 更新文章数据
	$where = array('art_id' => $art_id);
	$DB->update($DB->table('articles'), $art_data, $where);

	// 如果分类发生变化，更新分类统计
	if ($article['cate_id'] != $article_cate_id) {
		// 减少原分类的文章数量
		$DB->query("UPDATE ".$DB->table('categories')." SET cate_postcount=cate_postcount-1 WHERE cate_mod='article' AND cate_id=".$article['cate_id']." AND cate_postcount>0");
		// 增加新分类的文章数量
		$DB->query("UPDATE ".$DB->table('categories')." SET cate_postcount=cate_postcount+1 WHERE cate_mod='article' AND cate_id=$article_cate_id");
	}

	return $art_id;
}

$fileurl = 'website.php';
$tempfile = 'website.html';
$table = $DB->table('websites');

if (!isset($action)) $action = 'list';

/** list */
if ($action == 'list') {
	$pagetitle = '站点列表';
	
	$status = intval(trim($_GET['status']));
	$cate_id = intval(trim($_GET['cate_id']));
	$sort = intval(trim($_GET['sort']));
	$order = strtoupper(trim($_GET['order']));
	$keywords = addslashes(trim($_POST['keywords'] ? $_POST['keywords'] : $_GET['keywords']));
	if (empty($order)) $order = 'DESC';
	
	$pageurl = $fileurl.'?status='.$status.'&cate_id='.$cate_id.'&sort='.$sort.'&order='.$order;
	$keyurl = !empty($keywords) ? '&keywords='.urlencode($keywords) : '';
	$pageurl .= $keyurl;
	
	$category_option = get_category_option('webdir', 0, $cate_id, 0);
	
	$smarty->assign('status', $status);
	$smarty->assign('cate_id', $cate_id);
	$smarty->assign('sort', $sort);
	$smarty->assign('order', $order);
	$smarty->assign('keywords', $keywords);
	$smarty->assign('keyurl', $keyurl);
	$smarty->assign('category_option', $category_option);
	
	$where = '';
	$sql = "SELECT a.web_id, a.cate_id, a.web_name, a.web_url, a.web_ispay, a.web_isbest as web_istop, a.web_isbest, a.web_islink, a.web_status, a.web_ctime, b.web_id, b.web_ip, b.web_grank, b.web_brank, b.web_srank, b.web_arank, b.web_instat, b.web_outstat, b.web_views, b.web_errors, b.web_utime FROM $table a LEFT JOIN ".$DB->table('webdata')." b ON a.web_id=b.web_id WHERE";
	switch ($status) {
		case 1 :
			$where .= " a.web_status=1";
			break;
		case 2 :
			$where .= " (a.web_status=2 OR a.web_status=4)"; // 待审核包含审核不通过的
			break;
		case 3 :
			$where .= " a.web_status=3";
			break;
		case 4 :
			$where .= " a.web_status=4"; // 单独的审核不通过状态
			break;
		default :
			$where .= " a.web_status>-1";
			break;
	}
	
	if ($cate_id > 0) {
		$cate = get_one_category($cate_id);
		$where .= " AND a.cate_id IN (".$cate['cate_arrchildid'].")";
	}
	
	if ($keywords) $where .= " AND a.web_name like '%$keywords%'";
	
	switch ($sort) {
		case 1 :
			$field = "a.web_ctime";
			break;
		case 2 :
			$field = "b.web_grank";
			break;
		case 3 :
			$field = "b.web_brank";
			break;
		case 4 :
			$field = "b.web_srank";
			break;
		case 5 :
			$field = "b.web_arank";
			break;
		case 6 :
			$field = "b.web_instat";
			break;
		case 7 :
			$field = "b.web_outstat";
			break;
		case 8 :
			$field = "b.web_views";
			break;
		case 9 :
			$field = "b.web_errors";
			break;
		default :
			$field = "a.web_ctime";
			break;
	}
	
	$sql .= $where." ORDER BY a.web_isbest DESC, $field $order LIMIT $start, $pagesize";
	$query = $DB->query($sql);
	
	$websites = array();
	while ($web = $DB->fetch_array($query)) {
		switch ($web['web_status']) {
			case 1 :
				$web_status = '<font color="#333333">黑名单</font>';
				break;
			case 2 :
				$web_status = '<font color="#ff3300">待审核</font>';
				break;
			case 3 :
				$web_status = '<font color="#008800">已审核</font>';
				break;
			case 4 :
				$web_status = '<font color="#ff6600">审核不通过</font>';
				break;
			default :
				$web_status = '<font color="#999999">未知状态</font>';
				break;
		}
		$web_ispay = $web['web_ispay'] > 0 ? '<font color="#ff0000">付费</font>' : '<font color="#cccccc">付费</font>';
		$web_istop = $web['web_istop'] > 0 ? '<font color="#ff0000">置顶</font>' : '<font color="#cccccc">置顶</font>';
		$web_isbest = $web['web_isbest'] > 0 ? '<font color="#ff0000">推荐</font>' : '<font color="#cccccc">推荐</font>';

		// 智能显示链接状态
		$web_islink = get_smart_link_status($web, $options);

		$web['web_attr'] = $web_ispay.' - '.$web_istop.' - '.$web_isbest.' - '.$web_status.' - '.$web_islink;
		
		$web['web_cate'] = '<a href="'.$fileurl.'?cate_id='.$web['cate_id'].'">'.get_category_name($web['cate_id']).'</a>';
		$web['web_name'] = '<a href="'.format_url($web['web_url']).'" target="_blank">'.$web['web_name'].'</a> '.($web['web_errors'] > 0 ? '<sup style="color: #f00;">error!</sup>' : '');
		$web['web_ip'] = long2ip($web['web_ip']);
		$web['web_arank'] = number_format($web['web_arank']);
		$web['web_ctime'] = date('Y-m-d', $web['web_ctime']);
		$operate_links = array();
		$operate_links[] = '<a href="'.$fileurl.'?act=edit&web_id='.$web['web_id'].'">编辑</a>';

		// 根据网站状态显示不同的操作选项
		if ($web['web_status'] == 1) {
			// 黑名单网站显示恢复选项
			$operate_links[] = '<a href="'.$fileurl.'?act=restore_from_blacklist&web_id='.$web['web_id'].'" style="color: #28a745;" onClick="return confirm(\'确认恢复此网站为待审核状态吗？\');">恢复</a>';
			$operate_links[] = '<a href="blacklist.php?act=detail&id='.$web['web_id'].'" style="color: #007cba;">详情</a>';
		} else {
			// 非黑名单网站显示拉黑选项
			$operate_links[] = '<a href="'.$fileurl.'?act=quick_blacklist&web_id='.$web['web_id'].'" style="color: #dc3545;" onClick="return confirm(\'确认将此网站加入黑名单吗？\');">拉黑</a>';
		}

		$operate_links[] = '<a href="'.$fileurl.'?act=del&web_id='.$web['web_id'].'" onClick="return confirm(\'确认删除此内容吗？\');">删除</a>';
		$web['web_operate'] = implode('&nbsp;|&nbsp;', $operate_links);
		$websites[] = $web;
	}
	unset($web);
	$DB->free_result($query);
	
	$total = $DB->get_count($table.' a', $where);
	$showpage = showpage($pageurl, $total, $curpage, $pagesize);
	
	$smarty->assign('websites', $websites);
	$smarty->assign('showpage', $showpage);
	unset($websites);
}

/** add */
if ($action == 'add') {
	$pagetitle = '添加站点';

	$cate_id = intval($_GET['cate_id']);
	$category_option = get_category_option('webdir', 0, $cate_id, 0);
	
	$smarty->assign('category_option', $category_option);
	$smarty->assign('status', 3);
	$smarty->assign('h_action', 'saveadd');
}

/** edit */
if ($action == 'edit') {
	$pagetitle = '编辑站点';
	
	$web_id = intval($_GET['web_id']);
	$where = "w.web_id=$web_id";
	$row = get_one_website($where);
	if (!$row) {
		msgbox('指定的内容不存在！');
	}
	$category_option = get_category_option('webdir', 0, $row['cate_id'], 0);
	
	$smarty->assign('category_option', $category_option);
	$smarty->assign('ispay', $row['web_ispay']);
	$smarty->assign('istop', $row['web_isbest']);
	$smarty->assign('isbest', $row['web_isbest']);
	$smarty->assign('status', $row['web_status']);
	$smarty->assign('reject_reason', isset($row['web_reject_reason']) ? $row['web_reject_reason'] : '');
	$smarty->assign('row', $row);
	$smarty->assign('h_action', 'saveedit');
}

/** move */
if ($action == 'move') {
	$pagetitle = '移动站点';
			
	$web_ids = (array) ($_POST['web_id'] ? $_POST['web_id'] : $_GET['web_id']);
	if (empty($web_ids)) {
		msgbox('请选择要移动的站点！');
	} else {
		$wids = dimplode($web_ids);
	}
	
	$category_option = get_category_option('webdir', 0, 0, 0);
	$websites = $DB->fetch_all("SELECT web_id, web_name FROM $table WHERE web_id IN ($wids)");
	
	$smarty->assign('category_option', $category_option);
	$smarty->assign('websites', $websites);
	$smarty->assign('h_action', 'savemove');
}

/** attr */
if ($action == 'attr') {
	$pagetitle = '属性设置';
	
	$web_ids = (array) ($_POST['web_id'] ? $_POST['web_id'] : $_GET['web_id']);
	if (empty($web_ids)) {
		msgbox('请选择要设置的站点！');
	} else {
		$wids = dimplode($web_ids);
	}
	
	$category_option = get_category_option('webdir', 0, 0, 0);
	$websites = $DB->fetch_all("SELECT web_id, web_name FROM $table WHERE web_id IN ($wids)");
	
	$smarty->assign('category_option', $category_option);
	$smarty->assign('websites', $websites);
	$smarty->assign('h_action', 'saveattr');
}

/** down */
if ($action == 'down') {
	$pagetitle = '下载站点图片';
}

/** save data */
if (in_array($action, array('saveadd', 'saveedit'))) {
	$cate_id = intval($_POST['cate_id']);
	$web_name = trim($_POST['web_name']);
	$web_url = trim($_POST['web_url']);
	$web_tags = strtolower(addslashes(trim($_POST['web_tags'])));
	$web_pic = trim($_POST['web_pic']);
	$web_intro = addslashes(trim($_POST['web_intro']));
	$web_ai_intro = trim($_POST['web_ai_intro']); 
	$web_ip = trim($_POST['web_ip']);
	$web_grank = intval($_POST['web_grank']);
	$web_brank = intval($_POST['web_brank']);
	$web_srank = intval($_POST['web_srank']);
	$web_arank = intval($_POST['web_arank']);
	$web_instat = intval($_POST['web_instat']);
	$web_outstat = intval($_POST['web_outstat']);
	$web_views = intval($_POST['web_views']);
	$web_errors = intval($_POST['web_errors']);
	$web_ispay = intval($_POST['web_ispay']);
	$web_istop = intval($_POST['web_istop']);
	$web_isbest = intval($_POST['web_isbest']);
	$web_status = intval($_POST['web_status']);
	$web_blacklist_reason = trim($_POST['web_blacklist_reason']);
	$web_blacklist_category = intval($_POST['web_blacklist_category']);
	$web_reject_reason = trim($_POST['reject_reason']);
	$web_time = time();
	
	if ($cate_id <= 0) {
		msgbox('请选择网站所属分类！');
	} else {
		$row = get_one_category($cate_id);
		if ($row['cate_mod'] == 'website' && $row['cate_childcount'] > 0) {
			msgbox('指定的分类下有子分类，请选择子分类进行操作！');
		}
	}
	
	if (empty($web_name)) {
		msgbox('请输入网站名称！');
	}

	// 检查网站名称长度（中文算2个字符）
	$name_length = 0;
	for ($i = 0; $i < mb_strlen($web_name, 'UTF-8'); $i++) {
		$char = mb_substr($web_name, $i, 1, 'UTF-8');
		if (ord($char) > 127) {
			$name_length += 2; // 中文字符算2个字符
		} else {
			$name_length += 1; // 英文字符算1个字符
		}
	}

	if ($name_length > 12) {
		msgbox('网站名称过长！最多12个字符（6个汉字）');
	}
	
	if (empty($web_url)) {
		msgbox('请输入网站域名！');
	} else {
		if (!is_valid_domain($web_url)) {
			msgbox('请输入正确的网站域名！');
		}
	}
	
	if (empty($web_intro)) {
		msgbox('请输入网站简介！');
	}
	
	
	// $web_url = str_replace('http://', '', $web_url);
	$web_tags = str_replace('，', ',', $web_tags);
	$web_tags = str_replace(',,', ',', $web_tags);
	if (substr($web_tags, -1) == ',') {
		$web_tags = substr($web_tags, 0, strlen($web_tags) - 1);
	}
	
	$web_ip = sprintf("%u", ip2long($web_ip));
	
	$web_data = array(
		'cate_id' => $cate_id,
		'web_name' => $web_name,
		'web_url' => $web_url,
		'web_tags' => $web_tags,
		'web_pic' => $web_pic,
		'web_intro' => $web_intro,
		'web_ai_intro' => $web_ai_intro,
		'web_ispay' => $web_ispay,
		'web_istop' => $web_istop,
		'web_isbest' => $web_isbest,
		'web_status' => $web_status,
		'web_ctime' => $web_time,
	);

	// 如果是黑名单状态，添加黑名单相关字段
	if ($web_status == 1) {
		$web_data['web_blacklist_reason'] = $web_blacklist_reason;
		$web_data['web_blacklist_category'] = $web_blacklist_category;
		$web_data['web_blacklist_time'] = $web_time;
		$web_data['web_blacklist_operator'] = $_SESSION['admin_name'] ? $_SESSION['admin_name'] : 'admin';
	}

	// 如果是审核不通过状态，添加审核不通过原因字段
	if ($web_status == 4) {
		// 检查字段是否存在
		$table_name = $DB->table('websites');
		$check_sql = "SHOW COLUMNS FROM `{$table_name}` LIKE 'web_reject_reason'";
		$check_result = $DB->query($check_sql);
		if ($DB->num_rows($check_result) > 0) {
			$web_data['web_reject_reason'] = $web_reject_reason;
		}
	}
	
	$stat_data = array(
		'web_ip' => $web_ip,
		'web_grank' => $web_grank,
		'web_brank' => $web_brank,
		'web_srank' => $web_srank,
		'web_arank' => $web_arank,
		'web_instat' => $web_instat,
		'web_outstat' => $web_outstat,
		'web_views' => $web_views,
		'web_errors' => $web_errors,
		'web_utime' => $web_time,
	);
	
	if ($action == 'saveadd') {
    	$query = $DB->query("SELECT web_id, web_name, web_status, web_ctime FROM $table WHERE web_url='$web_url'");
    	if ($DB->num_rows($query)) {
    		$existing_web = $DB->fetch_array($query);
    		$status_msg = '';

    		switch($existing_web['web_status']) {
    			case 1:
    				$status_msg = '该网站已被拉黑，无法重复添加！';
    				break;
    			case 2:
    				$status_msg = '该网站正在审核中，无法重复添加！';
    				break;
    			case 3:
    				$status_msg = '该网站已收录（收录时间：' . date('Y-m-d', $existing_web['web_ctime']) . '），无法重复添加！';
    				break;
    			case 4:
    				$status_msg = '该网站审核不通过，无法重复添加！';
    				break;
    			default:
    				$status_msg = '您所添加的网站已存在！';
    		}

        	msgbox($status_msg);
    	}

		$web_data['user_id'] = $myself['user_id'];
		$DB->insert($table, $web_data);

		$web_id = $DB->insert_id();
		$stat_data['web_id'] = $web_id;
		$DB->insert($DB->table('webdata'), $stat_data);

		$DB->query("UPDATE ".$DB->table('categories')." SET cate_postcount=cate_postcount+1 WHERE cate_id=$cate_id");
		update_cache('archives');

		// 自动发布文章功能
		if (!empty($web_ai_intro)) {
			auto_publish_article_from_website($web_id, $web_name, $web_url, $web_tags, $web_intro, $web_ai_intro, $cate_id);
		}

		msgbox('网站添加成功！', $fileurl.'?act=add&cate_id='.$cate_id);
	} elseif ($action == 'saveedit') {
		$web_id = intval($_POST['web_id']);
		$where = array('web_id' => $web_id);
		unset($web_data['web_ctime']);

		// 获取修改前的网站信息
		$old_website = $DB->fetch_one("SELECT * FROM $table WHERE web_id = $web_id");
		if (!$old_website) {
			msgbox('网站不存在！', $fileurl);
		}

		$DB->update($table, $web_data, $where);
		$DB->update($DB->table('webdata'), $stat_data, $where);

		$DB->query("UPDATE ".$DB->table('categories')." SET cate_postcount=cate_postcount+1 WHERE cate_id=$cate_id");
		update_cache('archives');

		// 同步更新对应的文章
		sync_update_article_from_website($web_id, $web_name, $web_url, $web_tags, $web_intro, $web_ai_intro, $cate_id);
        require(APP_PATH.'include/sendmail.php');
        require(APP_PATH.'module/prelink.php');
        require('../source/module/payment_price.php');

		// 添加付费记录逻辑（修复：单个网站编辑时也要记录付费统计）
		$payment_table = $DB->table('payment_records');
		$current_time = time();
		$payment_records = array();

		// VIP付费记录（审核通过或编辑时从非VIP变为VIP）
		if ($web_ispay == 1 && $old_website['web_ispay'] != 1) {
			$vip_price = get_current_price(1);
			$amount = 0.00;
			if ($vip_price) {
				$calculation = calculate_payment(1, 1, 0);
				$amount = $calculation['amount'];
			} else {
				$amount = 30.00; // 默认VIP价格
			}

			$remark = '';
			if ($web_status == 3 && $old_website['web_status'] != 3) {
				$remark = '单个网站审核通过（VIP）';
			} else {
				$remark = '单个网站编辑设置VIP属性';
			}

			$payment_records[] = array(
				'web_id' => $web_id,
				'web_name' => $web_name,
				'web_url' => $web_url,
				'payment_type' => 1, // VIP
				'payment_amount' => $amount,
				'payment_time' => $current_time,
				'expire_time' => $old_website['web_vip_expire'] ?: ($current_time + 365 * 24 * 3600),
				'operator' => $myself['user_email'],
				'status' => 1,
				'remark' => $remark,
				'created_at' => $current_time
			);
		}

		// 推荐付费记录（审核通过或编辑时从非推荐变为推荐，且不是VIP+推荐组合）
		if ($web_istop == 1 && $old_website['web_isbest'] != 1 && !($web_ispay == 1 && $old_website['web_ispay'] != 1)) {
			$price_table = $DB->table('payment_prices');
			$check_price_table = $DB->query("SHOW TABLES LIKE '$price_table'");

			$amount = 10.00; // 默认推荐价格
			$expire_time = $current_time + (365 * 24 * 3600); // 默认一年

			if ($DB->num_rows($check_price_table) > 0) {
				$recommend_price_result = $DB->fetch_one("SELECT price, duration_days FROM $price_table WHERE service_type = 2 AND status = 1 ORDER BY effective_time DESC LIMIT 1");
				if ($recommend_price_result) {
					$amount = $recommend_price_result['price'];
					if ($recommend_price_result['duration_days'] > 0) {
						$expire_time = $current_time + ($recommend_price_result['duration_days'] * 24 * 3600);
					}
				}
			}

			$remark = '';
			if ($web_status == 3 && $old_website['web_status'] != 3) {
				$remark = '单个网站审核通过（推荐）';
			} else {
				$remark = '单个网站编辑设置推荐属性';
			}

			$payment_records[] = array(
				'web_id' => $web_id,
				'web_name' => $web_name,
				'web_url' => $web_url,
				'payment_type' => 2, // 推荐
				'payment_amount' => $amount,
				'payment_time' => $current_time,
				'expire_time' => $old_website['web_recommend_expire'] ?: $expire_time,
				'operator' => $myself['user_email'],
				'status' => 1,
				'remark' => $remark,
				'created_at' => $current_time
			);
		}

		// 快审记录
		if ($web_status == 3 && $old_website['web_status'] != 3 && $web_ispay == 0 && $web_istop == 0) {
			$price_table = $DB->table('payment_prices');
			$check_price_table = $DB->query("SHOW TABLES LIKE '$price_table'");

			$amount = 5.00; // 默认快审价格
			if ($DB->num_rows($check_price_table) > 0) {
				$fast_price_result = $DB->fetch_one("SELECT price FROM $price_table WHERE service_type = 3 AND status = 1 ORDER BY effective_time DESC LIMIT 1");
				if ($fast_price_result) {
					$amount = $fast_price_result['price'];
				}
			}

			$payment_records[] = array(
				'web_id' => $web_id,
				'web_name' => $web_name,
				'web_url' => $web_url,
				'payment_type' => 3, // 快审
				'payment_amount' => $amount,
				'payment_time' => $current_time,
				'expire_time' => 0,
				'operator' => $myself['user_email'],
				'status' => 1,
				'remark' => '单个网站审核通过（快审）',
				'created_at' => $current_time
			);
		}

		// 插入付费记录
		foreach ($payment_records as $payment_data) {
			$DB->insert($payment_table, $payment_data);
		}

        if ($web_status == 3) {
            $websites = $DB->fetch_all("SELECT * FROM ".$DB->table('websites')." WHERE web_id=$web_id");
			$site_link = get_website_url($websites[0]['web_id'], true);
			//发送邮件
			if (!empty($options['smtp_host']) && !empty($options['smtp_port']) && !empty($options['smtp_auth']) && !empty($options['smtp_user'])  && !empty($options['smtp_pass'])) {
				$smarty->assign('site_name', $options['site_name']);
				$smarty->assign('site_url', $options['site_url']);
				$smarty->assign('web_name', $websites[0]['web_name']);
				$smarty->assign('site_link', $site_link);
				$mailbody = $smarty->fetch('audit_mail.html');

                $user_id = $websites[0]['user_id'];
                $user = $DB->fetch_all("SELECT * FROM ".$DB->table('users')." WHERE user_id=$user_id");
				$rs = sendmail($user[0]['user_email'], '['.$options['site_name'].'] 网站已通过审核！', $mailbody);
			}
		}
		msgbox('网站修改成功！', $fileurl);
	}
}

/** del */
if ($action == 'del') {
	$web_ids = (array) ($_POST['web_id'] ? $_POST['web_id'] : $_GET['web_id']);

	// 调试信息
	if (empty($web_ids) || (count($web_ids) == 1 && empty($web_ids[0]))) {
		msgbox('错误：未选择要删除的网站！请先选择要删除的网站。');
	}

	// 验证web_ids是否有效
	$valid_ids = array();
	foreach ($web_ids as $id) {
		$id = intval($id);
		if ($id > 0) {
			$valid_ids[] = $id;
		}
	}

	if (empty($valid_ids)) {
		msgbox('错误：选择的网站ID无效！');
	}

	// 检查要删除的网站是否存在
	$check_sql = "SELECT web_id, web_name FROM ".$table." WHERE web_id IN (".dimplode($valid_ids).")";
	$existing_websites = $DB->fetch_all($check_sql);

	if (empty($existing_websites)) {
		msgbox('错误：选择的网站不存在或已被删除！');
	}

	// 执行删除操作
	try {
		$delete_result1 = $DB->delete($table, 'web_id IN ('.dimplode($valid_ids).')');
		$delete_result2 = $DB->delete($DB->table('webdata'), 'web_id IN ('.dimplode($valid_ids).')');

		// 检查删除结果
		if ($delete_result1 === false) {
			msgbox('错误：删除网站主表数据失败！数据库错误：' . $DB->error());
		}

		if ($delete_result2 === false) {
			msgbox('错误：删除网站扩展数据失败！数据库错误：' . $DB->error());
		}

		update_cache('archives');
		unset($web_ids, $valid_ids);

		$deleted_count = count($existing_websites);
		msgbox("网站删除成功！共删除了 {$deleted_count} 个网站。", $fileurl);

	} catch (Exception $e) {
		msgbox('删除操作发生异常：' . $e->getMessage());
	}
}

/** move */
if ($action == 'savemove') {
	$web_ids = (array) $_POST['web_id'];
	$cate_id = intval($_POST['cate_id']);
	if (empty($web_ids)) {
		msgbox('请选择要移动的内容！');
	}
	if ($cate_id <= 0) {
		msgbox('请选择分类！');
	} else {
		$cate = get_one_category($cate_id);
		if ($cate['cate_childcount'] > 0) {
			msgbox('指定的分类下有子分类，请选择子分类进行操作！');
		}
	}
	
	$DB->update($table, array('cate_id' => $cate_id), 'web_id IN ('.dimplode($web_ids).')');
	update_cache('archives');
	
	msgbox('网站移动成功！', $fileurl);
}

/** attr */
if ($action == 'saveattr') {
	$web_ids = (array) $_POST['web_id'];
	$web_ispay = intval($_POST['web_ispay']);
	$web_istop = intval($_POST['web_istop']);
	$web_isbest = intval($_POST['web_isbest']);
	$web_status = intval($_POST['web_status']);
	if (empty($web_ids)) {
		msgbox('请选择要设置的内容！');
	}
	
	require(APP_PATH.'include/sendmail.php');
	require(APP_PATH.'module/prelink.php');
	require('../source/module/payment_price.php');

	$payment_table = $DB->table('payment_records');
	$current_time = time();

	$websites = $DB->fetch_all("SELECT w.web_id, w.web_name, w.web_url, w.web_ispay, w.web_istop, w.web_isbest, w.web_status, u.user_email FROM $table w LEFT JOIN ".$DB->table("users")." u ON w.user_id=u.user_id WHERE w.web_id IN (".dimplode($web_ids).")");
	foreach ($websites as $row) {
		if ($web_status == 3) {
			$site_link = get_website_url($row['web_id'], true);
			//发送邮件
			if (!empty($options['smtp_host']) && !empty($options['smtp_port']) && !empty($options['smtp_auth']) && !empty($options['smtp_user'])  && !empty($options['smtp_pass'])) {
				$smarty->assign('site_name', $options['site_name']);
				$smarty->assign('site_url', $options['site_url']);
				$smarty->assign('web_name', $row['web_name']);
				$smarty->assign('site_link', $site_link);
				$mailbody = $smarty->fetch('audit_mail.html');
				sendmail($row['user_email'], '['.$options['site_name'].'] 网站已通过审核！', $mailbody);
			}
		}

		// 记录付费信息
		$payment_records = array();



		// VIP付费记录（审核通过或编辑时从非VIP变为VIP）
		if ($web_ispay == 1 && $row['web_ispay'] != 1) {
			// 情况1：审核通过时设置为VIP
			// 情况2：编辑时将非VIP网站设置为VIP
			$vip_price = get_current_price(1);
			$amount = 0.00;
			if ($vip_price) {
				$calculation = calculate_payment(1, 1, 0);
				$amount = $calculation['amount'];
			} else {
				$amount = 30.00; // 默认VIP价格
			}

			$remark = '';
			if ($web_status == 3 && $row['web_status'] != 3) {
				$remark = '后台审核通过（VIP）';
			} else {
				$remark = '后台设置VIP属性';
			}

			$payment_records[] = array(
				'web_id' => $row['web_id'],
				'web_name' => $row['web_name'],
				'web_url' => $row['web_url'],
				'payment_type' => 1, // VIP
				'payment_amount' => $amount,
				'payment_time' => $current_time,
				'expire_time' => $row['web_vip_expire'] ?: ($current_time + 365 * 24 * 3600),
				'operator' => $myself['user_email'],
				'status' => 1,
				'remark' => $remark,
				'created_at' => $current_time
			);
		}



		// 推荐付费记录（审核通过或编辑时从非推荐变为推荐，且不是VIP+推荐组合）
		if ($web_istop == 1 && $row['web_isbest'] != 1 && !($web_ispay == 1 && $row['web_ispay'] != 1)) {
			// 情况1：审核通过时设置为推荐（但不是同时设置VIP+推荐）
			// 情况2：编辑时将非推荐网站设置为推荐（但不是同时设置VIP+推荐）
			// 排除VIP+推荐组合，避免重复计费

			$price_table = $DB->table('payment_prices');
			$check_price_table = $DB->query("SHOW TABLES LIKE '$price_table'");

			$amount = 10.00; // 默认推荐价格
			$expire_time = $current_time + (365 * 24 * 3600); // 默认一年

			if ($DB->num_rows($check_price_table) > 0) {
				$recommend_price_result = $DB->fetch_one("SELECT price, duration_days FROM $price_table WHERE service_type = 2 AND status = 1 ORDER BY effective_time DESC LIMIT 1");
				if ($recommend_price_result) {
					$amount = $recommend_price_result['price'];
					if ($recommend_price_result['duration_days'] > 0) {
						$expire_time = $current_time + ($recommend_price_result['duration_days'] * 24 * 3600);
					}
				}
			}

			$remark = '';
			if ($web_status == 3 && $row['web_status'] != 3) {
				$remark = '后台审核通过（推荐）';
			} else {
				$remark = '后台设置推荐属性';
			}

			$payment_records[] = array(
				'web_id' => $row['web_id'],
				'web_name' => $row['web_name'],
				'web_url' => $row['web_url'],
				'payment_type' => 2, // 推荐
				'payment_amount' => $amount,
				'payment_time' => $current_time,
				'expire_time' => $row['web_recommend_expire'] ?: $expire_time,
				'operator' => $myself['user_email'],
				'status' => 1,
				'remark' => $remark,
				'created_at' => $current_time
			);
		}

		// 快审记录（当网站状态改为已审核时，且不是VIP和推荐）
		if ($web_status == 3 && $row['web_status'] != 3 && $web_ispay == 0 && $web_istop == 0) {
			// 检查价格配置表是否存在
			$price_table = $DB->table('payment_prices');
			$check_price_table = $DB->query("SHOW TABLES LIKE '$price_table'");

			$amount = 5.00; // 默认快审价格
			if ($DB->num_rows($check_price_table) > 0) {
				// 如果价格表存在，尝试获取当前价格
				$fast_price_result = $DB->fetch_one("SELECT price FROM $price_table WHERE service_type = 3 AND status = 1 ORDER BY effective_time DESC LIMIT 1");
				if ($fast_price_result) {
					$amount = $fast_price_result['price'];
				}
			}

			$payment_records[] = array(
				'web_id' => $row['web_id'],
				'web_name' => $row['web_name'],
				'web_url' => $row['web_url'],
				'payment_type' => 3, // 快审
				'payment_amount' => $amount,
				'payment_time' => $current_time,
				'expire_time' => 0, // 快审不设置过期时间
				'operator' => $myself['user_email'],
				'status' => 1,
				'remark' => '后台审核通过（快审）',
				'created_at' => $current_time
			);
		}

		// 插入付费记录
		foreach ($payment_records as $payment_data) {
			$DB->insert($payment_table, $payment_data);
		}

		$DB->update($table, array('web_ispay' => $web_ispay, 'web_isbest' => $web_istop, 'web_status' => $web_status), array('web_id' => $row['web_id']));
	}
	
	//$DB->update($table, array('web_istop' => $web_istop, 'web_isbest' => $web_isbest, 'web_status' => $web_status), 'web_id IN ('.dimplode($web_ids).')');
	
	msgbox('网站属性设置成功！', $fileurl);
}

/** metainfo */
if ($action == 'metainfo') {
	$url = trim($_GET['url']);
	if (empty($url)) {
		exit('请输入网站域名！');
	} else {
		if (!is_valid_domain($url)) {
			exit('请输入正确的网站域名！');
		}
	}

	$meta = get_sitemeta($url);
	echo '<script type="text/javascript">';
	echo '$("#web_name").attr("value", "'.addslashes($meta['title']).'");';
	echo '$("#web_tags").attr("value", "'.addslashes($meta['keywords']).'");';
	echo '$("#web_intro").attr("value", "'.addslashes($meta['description']).'");';
	echo '</script>';
	unset($meta);
}

/** webdata */
if ($action == 'webdata') {
	$url = trim($_GET['url']);
	if (empty($url)) {
		echo '<script type="text/javascript">';
		echo '$("#data_btn").val("重新获取").prop("disabled", false);';
		echo 'alert("请输入网站域名！");';
		echo '</script>';
		exit;
	} else {
		if (!is_valid_domain($url)) {
			echo '<script type="text/javascript">';
			echo '$("#data_btn").val("重新获取").prop("disabled", false);';
			echo 'alert("请输入正确的网站域名！");';
			echo '</script>';
			exit;
		}
	}

	// 清理输出缓冲区
	if (ob_get_level()) {
		ob_clean();
	}

	// 设置内容类型和禁用缓存
	header('Content-Type: text/html; charset=utf-8');
	header('Cache-Control: no-cache, must-revalidate');
	header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

	// 立即输出开始状态
	echo '<script type="text/javascript">';
	echo 'console.log("开始获取数据: ' . $url . '");';
	echo '</script>';

	// 强制输出缓冲区
	if (ob_get_level()) {
		ob_flush();
	}
	flush();

	// 添加错误处理和调试信息
	try {
		// 设置更长的执行时间
		set_time_limit(120);

		// 记录开始时间
		$start_total = microtime(true);

		// 逐步获取数据并实时反馈
		echo '<script type="text/javascript">';
		echo 'console.log("正在获取服务器IP...");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		$ip = get_serverip($url);
		$ip = $ip ? $ip : '获取失败';

		echo '<script type="text/javascript">';
		echo '$("#web_ip").val("'.$ip.'");';
		echo 'console.log("IP获取完成: ' . $ip . '");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		echo '<script type="text/javascript">';
		echo 'console.log("正在获取百度收录量...");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		$grank = get_pagerank($url);
		$grank = is_numeric($grank) ? $grank : 0;

		echo '<script type="text/javascript">';
		echo '$("#web_grank").val("'.$grank.'");';
		echo 'console.log("百度收录量获取完成: ' . $grank . '");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		echo '<script type="text/javascript">';
		echo 'console.log("正在获取必应收录量...");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		$brank = get_baidurank($url);
		$brank = is_numeric($brank) ? $brank : 0;

		echo '<script type="text/javascript">';
		echo '$("#web_brank").val("'.$brank.'");';
		echo 'console.log("必应收录量获取完成: ' . $brank . '");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		echo '<script type="text/javascript">';
		echo 'console.log("正在获取360收录量...");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		$srank = get_sogourank($url);
		$srank = is_numeric($srank) ? $srank : 0;

		echo '<script type="text/javascript">';
		echo '$("#web_srank").val("'.$srank.'");';
		echo 'console.log("360收录量获取完成: ' . $srank . '");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		echo '<script type="text/javascript">';
		echo 'console.log("正在获取搜狗收录量...");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		$arank = get_alexarank($url);
		$arank = is_numeric($arank) ? $arank : 0;

		// 计算总耗时
		$total_time = round((microtime(true) - $start_total) * 1000, 2);

		echo '<script type="text/javascript">';
		echo '$("#web_arank").val("'.$arank.'");';
		echo '$("#data_btn").val("重新获取").prop("disabled", false);';
		echo 'console.log("搜狗收录量获取完成: ' . $arank . '");';
		echo 'console.log("数据获取完成 (总耗时: '.$total_time.'ms): IP='.$ip.', 百度='.$grank.', 必应='.$brank.', 360='.$srank.', 搜狗='.$arank.'");';
		echo 'alert("数据获取完成！\\n\\nIP: '.$ip.'\\n百度收录: '.$grank.'\\n必应收录: '.$brank.'\\n360收录: '.$srank.'\\n搜狗收录: '.$arank.'\\n\\n总耗时: '.$total_time.'ms");';
		echo '</script>';

	} catch (Exception $e) {
		echo '<script type="text/javascript">';
		echo '$("#data_btn").val("重新获取").prop("disabled", false);';
		echo 'alert("获取数据时发生错误: '.addslashes($e->getMessage()).'");';
		echo 'console.error("获取数据错误: '.addslashes($e->getMessage()).'");';
		echo '</script>';
	}
}

/** 快速拉黑操作 */
if ($action == 'quick_blacklist') {
	$web_id = intval($_GET['web_id']);

	if ($web_id <= 0) {
		msgbox('参数错误！');
	}

	// 检查网站是否存在
	$website = $DB->fetch_one("SELECT web_id, web_name, web_status FROM $table WHERE web_id = $web_id");
	if (!$website) {
		msgbox('指定的网站不存在！');
	}

	if ($website['web_status'] == 1) {
		msgbox('该网站已在黑名单中！');
	}

	// 更新网站状态为黑名单
	$update_data = array(
		'web_status' => 1,
		'web_blacklist_reason' => '管理员快速拉黑',
		'web_blacklist_category' => 0, // 默认为"其他"分类
		'web_blacklist_time' => time(),
		'web_blacklist_operator' => $_SESSION['admin_name'] ? $_SESSION['admin_name'] : 'admin'
	);

	$where = array('web_id' => $web_id);
	$result = $DB->update($table, $update_data, $where);

	if ($result) {
		msgbox('网站已成功加入黑名单！', $fileurl);
	} else {
		msgbox('操作失败，请重试！');
	}
}

/** 从黑名单恢复操作 */
if ($action == 'restore_from_blacklist') {
	$web_id = intval($_GET['web_id']);

	if ($web_id <= 0) {
		msgbox('参数错误！');
	}

	// 检查网站是否存在且在黑名单中
	$website = $DB->fetch_one("SELECT web_id, web_name, web_status FROM $table WHERE web_id = $web_id");
	if (!$website) {
		msgbox('指定的网站不存在！');
	}

	if ($website['web_status'] != 1) {
		msgbox('该网站不在黑名单中！');
	}

	// 恢复网站状态为待审核，清空黑名单信息
	$update_data = array(
		'web_status' => 2,
		'web_blacklist_reason' => '',
		'web_blacklist_category' => 0,
		'web_blacklist_time' => 0,
		'web_blacklist_operator' => ''
	);

	$where = array('web_id' => $web_id);
	$result = $DB->update($table, $update_data, $where);

	if ($result) {
		msgbox('网站已恢复为待审核状态！', $fileurl);
	} else {
		msgbox('操作失败，请重试！');
	}
}


smarty_output($tempfile);
?>