<?php
/**
 * 收录量批量更新定时任务
 * 建议设置为每天执行一次：0 3 * * * /usr/bin/php /path/to/cron_update_index.php
 */

// 设置执行时间限制
set_time_limit(0);

// 定义常量
define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace("\\", '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 引入核心文件
require(ROOT_PATH.'config.php');
require(ROOT_PATH.'source/init.php');
require(ROOT_PATH.'source/module/webdata.php');

// 检查是否通过web访问
$is_web = isset($_SERVER['HTTP_HOST']);

if ($is_web) {
    echo "<html><head><meta charset='UTF-8'><title>收录量批量更新</title></head><body>";
    echo "<h1>收录量批量更新任务</h1>";
    echo "<p>开始时间: " . date('Y-m-d H:i:s') . "</p>";
} else {
    echo "收录量批量更新任务开始...\n";
    echo "开始时间: " . date('Y-m-d H:i:s') . "\n";
}

// 配置参数
$batch_size = intval($_GET['limit']) ?: 20; // 每次更新的网站数量
$update_cycle = intval($options['data_update_cycle']) ?: 3; // 更新周期（天）
$delay_seconds = 2; // 每个网站之间的延迟（秒）

if ($is_web) {
    echo "<p>配置参数：</p>";
    echo "<ul>";
    echo "<li>批量大小：$batch_size 个网站</li>";
    echo "<li>更新周期：$update_cycle 天</li>";
    echo "<li>延迟时间：$delay_seconds 秒</li>";
    echo "</ul>";
} else {
    echo "配置参数：\n";
    echo "- 批量大小：$batch_size 个网站\n";
    echo "- 更新周期：$update_cycle 天\n";
    echo "- 延迟时间：$delay_seconds 秒\n\n";
}

// 获取需要更新的网站
$websites_table = $DB->table('websites');
$webdata_table = $DB->table('webdata');

// 查询需要更新收录量的网站（已审核的网站）
$cutoff_time = time() - ($update_cycle * 24 * 3600);
$sql = "SELECT w.web_id, w.web_name, w.web_url, COALESCE(d.web_utime, 0) as web_utime,
               COALESCE(d.web_grank, 0) as web_grank, COALESCE(d.web_brank, 0) as web_brank,
               COALESCE(d.web_srank, 0) as web_srank, COALESCE(d.web_arank, 0) as web_arank
        FROM $websites_table w
        LEFT JOIN $webdata_table d ON w.web_id = d.web_id
        WHERE w.web_status = 3
        AND (COALESCE(d.web_utime, 0) = 0 OR COALESCE(d.web_utime, 0) < $cutoff_time)
        ORDER BY COALESCE(d.web_utime, 0) ASC, w.web_id ASC
        LIMIT $batch_size";

$query = $DB->query($sql);
$websites = array();
while ($row = $DB->fetch_array($query)) {
    $websites[] = $row;
}

if (empty($websites)) {
    if ($is_web) {
        echo "<p style='color: orange;'>没有需要更新收录量的网站</p>";
        echo "</body></html>";
    } else {
        echo "没有需要更新收录量的网站\n";
    }
    exit;
}

$total_websites = count($websites);
if ($is_web) {
    echo "<p>找到 <strong>$total_websites</strong> 个需要更新的网站</p>";
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>序号</th><th>网站名称</th><th>域名</th><th>百度</th><th>必应</th><th>360</th><th>搜狗</th><th>状态</th><th>耗时</th>";
    echo "</tr>";
} else {
    echo "找到 $total_websites 个需要更新的网站\n\n";
}

$success_count = 0;
$error_count = 0;
$total_start_time = microtime(true);

foreach ($websites as $index => $website) {
    $web_id = $website['web_id'];
    $web_name = $website['web_name'];
    $web_url = $website['web_url'];
    
    $start_time = microtime(true);
    
    if ($is_web) {
        echo "<tr>";
        echo "<td>" . ($index + 1) . "</td>";
        echo "<td>" . htmlspecialchars($web_name) . "</td>";
        echo "<td>" . htmlspecialchars($web_url) . "</td>";
    } else {
        echo "处理网站 " . ($index + 1) . "/$total_websites: $web_name ($web_url)\n";
    }
    
    try {
        // 获取各搜索引擎收录量
        $baidu_count = get_pagerank($web_url);
        $bing_count = get_baidurank($web_url);
        $so360_count = get_sogourank($web_url);
        $sogou_count = get_alexarank($web_url);
        
        // 更新数据库
        $current_time = time();
        
        // 确保webdata表中有对应记录
        $existing = $DB->fetch_one("SELECT web_id FROM $webdata_table WHERE web_id = $web_id");
        if (!$existing) {
            $DB->insert($webdata_table, array('web_id' => $web_id));
        }
        
        // 更新收录量数据
        $update_data = array(
            'web_grank' => $baidu_count,
            'web_brank' => $bing_count,
            'web_srank' => $so360_count,
            'web_arank' => $sogou_count,
            'web_utime' => $current_time
        );
        
        $where = array('web_id' => $web_id);
        $DB->update($webdata_table, $update_data, $where);
        
        $end_time = microtime(true);
        $execution_time = round(($end_time - $start_time) * 1000, 2);
        
        if ($is_web) {
            echo "<td>" . number_format($baidu_count) . "</td>";
            echo "<td>" . number_format($bing_count) . "</td>";
            echo "<td>" . number_format($so360_count) . "</td>";
            echo "<td>" . number_format($sogou_count) . "</td>";
            echo "<td style='color: green;'>✅ 成功</td>";
            echo "<td>{$execution_time}ms</td>";
            echo "</tr>";
            flush(); // 实时输出
        } else {
            echo "  百度: " . number_format($baidu_count) . ", 必应: " . number_format($bing_count) . 
                 ", 360: " . number_format($so360_count) . ", 搜狗: " . number_format($sogou_count) . 
                 " (耗时: {$execution_time}ms)\n";
        }
        
        $success_count++;
        
    } catch (Exception $e) {
        $end_time = microtime(true);
        $execution_time = round(($end_time - $start_time) * 1000, 2);
        
        if ($is_web) {
            echo "<td colspan='4' style='color: red;'>错误: " . htmlspecialchars($e->getMessage()) . "</td>";
            echo "<td style='color: red;'>❌ 失败</td>";
            echo "<td>{$execution_time}ms</td>";
            echo "</tr>";
        } else {
            echo "  错误: " . $e->getMessage() . " (耗时: {$execution_time}ms)\n";
        }
        
        $error_count++;
    }
    
    // 添加延迟避免频繁请求
    if ($index < $total_websites - 1) {
        sleep($delay_seconds);
    }
}

$total_end_time = microtime(true);
$total_execution_time = round(($total_end_time - $total_start_time), 2);

if ($is_web) {
    echo "</table>";
    echo "<h2>更新完成</h2>";
    echo "<p>结束时间: " . date('Y-m-d H:i:s') . "</p>";
    echo "<p>总耗时: {$total_execution_time} 秒</p>";
    echo "<p>成功更新: <span style='color: green;'>$success_count</span> 个网站</p>";
    echo "<p>更新失败: <span style='color: red;'>$error_count</span> 个网站</p>";
    echo "<p>成功率: " . round(($success_count / $total_websites) * 100, 2) . "%</p>";
    echo "</body></html>";
} else {
    echo "\n更新完成\n";
    echo "结束时间: " . date('Y-m-d H:i:s') . "\n";
    echo "总耗时: {$total_execution_time} 秒\n";
    echo "成功更新: $success_count 个网站\n";
    echo "更新失败: $error_count 个网站\n";
    echo "成功率: " . round(($success_count / $total_websites) * 100, 2) . "%\n";
}
?>
