═══════════════════════════════════════════════════════════════════════════════
                           35DIR网站目录系统 - 模板标签使用说明文档
                                    Version 2.0 - 详细完整版
═══════════════════════════════════════════════════════════════════════════════

【系统概述】
35dir系统采用Smarty模板引擎，提供强大的模板标签功能。本文档详细介绍所有可用的模板标签、
函数和变量，帮助开发者快速掌握模板开发技巧。

【模板引擎配置】
- 模板引擎：Smarty 3.x/4.x（支持版本升级）
- 标签分隔符：{# 和 #}（可在 source/include/smarty.php 中修改）
- 模板目录：themes/default/（前台）、themes/system/（后台）
- 编译目录：data/compile/
- 缓存目录：data/cache/
- 升级方法：覆盖 source/extend/smarty 目录即可

【标签语法规则】
1. 变量输出：{#$变量名#}
2. 函数调用：{#函数名(参数1, 参数2)#}
3. 循环语句：{#foreach from=数组 item=项目#}...{#/foreach#}
4. 条件语句：{#if 条件#}...{#else#}...{#/if#}
5. 注释语法：{#* 这是注释 *#}

═══════════════════════════════════════════════════════════════════════════════
                                  一、网站通用标签
═══════════════════════════════════════════════════════════════════════════════

【基础站点信息】
{#$site_root#}        ─── 网站根目录路径（如：http://www.example.com/）
{#$site_name#}        ─── 网站名称（后台设置的站点名称）
{#$site_title#}       ─── 网站标题（浏览器标题栏显示）
{#$site_url#}         ─── 网站完整URL地址
{#$site_keywords#}    ─── 网站关键词（SEO用途）
{#$site_description#} ─── 网站描述信息（SEO用途）
{#$site_copyright#}   ─── 网站版权信息（支持HTML代码）
{#$site_rss#}         ─── RSS订阅链接
{#$site_path#}        ─── 当前位置导航（面包屑导航）

【系统配置信息】
{#$cfg#}              ─── 完整的系统配置数组（包含所有后台设置）
{#$cfg.参数名#}       ─── 获取特定配置参数
{#$script_time#}      ─── 页面执行时间和查询统计
{#$login_status#}     ─── 用户登录状态信息

【使用示例】
<title>{#$site_title#}</title>
<meta name="keywords" content="{#$site_keywords#}" />
<meta name="description" content="{#$site_description#}" />
<div class="breadcrumb">{#$site_path#}</div>
<footer>{#$site_copyright#}</footer>

═══════════════════════════════════════════════════════════════════════════════
                                二、网站函数标签
═══════════════════════════════════════════════════════════════════════════════

【1. 分类列表函数 - get_categories()】
函数原型：get_categories($cate_id = 0, $top_num = 0, $is_best = false)

参数说明：
- $cate_id：分类ID，默认0表示获取根分类，指定ID获取该分类下的子分类
- $top_num：显示数量，默认0表示显示全部，指定数字限制显示条数
- $is_best：是否只显示推荐分类，true显示推荐，false显示全部

返回字段：
- $cate_id：分类ID
- $cate_name：分类名称
- $cate_mod：分类模块（如webdir）
- $cate_childcount：子分类数量
- $cate_postcount：分类下网站数量
- $cate_link：分类链接地址

使用示例：
<!-- 显示根分类列表 -->
<ul class="category-list">
{#foreach from=get_categories(0, 10, false) item=cate#}
    <li>
        <a href="{#$cate.cate_link#}" title="{#$cate.cate_name#}">
            {#$cate.cate_name#}
        </a>
        <span class="count">({#$cate.cate_postcount#})</span>
    </li>
{#/foreach#}
</ul>

<!-- 显示推荐分类 -->
<div class="hot-categories">
{#foreach from=get_categories(0, 8, true) item=hot#}
    <a href="{#$hot.cate_link#}" class="hot-cate">{#$hot.cate_name#}</a>
{#/foreach#}
</div>

【2. 网站列表函数 - get_websites()】
函数原型：get_websites($cate_id = 0, $top_num = 10, $is_pay = false, $is_best = false, $field = 'ctime', $order = 'desc')

参数说明：
- $cate_id：分类ID，0表示全部分类
- $top_num：显示数量，默认10条
- $is_pay：是否只显示付费网站
- $is_best：是否只显示推荐网站
- $field：排序字段（ctime-收录时间, views-浏览次数, instat-点入次数, outstat-点出次数）
- $order：排序方式（desc-降序, asc-升序）

返回字段：
- $web_id：网站ID
- $web_name：网站名称
- $web_url：网站URL
- $web_furl：格式化的URL
- $web_pic：网站缩略图
- $web_intro：网站简介
- $web_ai_intro：AI生成的简介
- $web_tags：网站标签数组
- $web_ctime：收录时间
- $web_utime：更新时间
- $web_views：浏览次数
- $web_grank：Google PageRank
- $web_brank：百度权重
- $web_srank：搜狗权重
- $web_arank：Alexa排名
- $web_link：网站详情页链接
- $cate_name：所属分类名称

使用示例：
<!-- 最新收录网站 -->
<div class="latest-websites">
{#foreach from=get_websites(0, 12, false, false, 'ctime', 'desc') item=site#}
    <div class="site-item">
        <div class="site-thumb">
            <img src="{#$site.web_pic#}" alt="{#$site.web_name#}" />
        </div>
        <div class="site-info">
            <h3><a href="{#$site.web_link#}" title="{#$site.web_name#}">{#$site.web_name#}</a></h3>
            <p class="intro">{#$site.web_intro#}</p>
            <div class="meta">
                <span class="category">{#$site.cate_name#}</span>
                <span class="views">浏览：{#$site.web_views#}</span>
                <span class="time">{#$site.web_utime#}</span>
            </div>
        </div>
    </div>
{#/foreach#}
</div>

<!-- 推荐网站 -->
<div class="recommend-sites">
{#foreach from=get_websites(0, 8, false, true, 'views', 'desc') item=rec#}
    <a href="{#$rec.web_link#}" class="rec-site" title="{#$rec.web_name#}">
        <img src="{#$rec.web_pic#}" alt="{#$rec.web_name#}" />
        <span>{#$rec.web_name#}</span>
    </a>
{#/foreach#}
</div>

【3. 友情链接函数 - get_links()】
函数原型：get_links()

返回字段：
- $link_id：链接ID
- $link_name：链接名称
- $link_url：链接地址
- $link_logo：链接LOGO地址

使用示例：
<div class="friend-links">
    <h3>友情链接</h3>
    <ul>
    {#foreach from=get_links() item=link#}
        <li>
            <a href="{#$link.link_url#}" target="_blank" title="{#$link.link_name#}">
                {#if $link.link_logo#}
                    <img src="{#$link.link_logo#}" alt="{#$link.link_name#}" />
                {#else#}
                    {#$link.link_name#}
                {#/if#}
            </a>
        </li>
    {#/foreach#}
    </ul>
</div>

【4. 数据归档函数 - get_archives()】
函数原型：get_archives()

返回格式：二维数组，年份->月份->网站数量

使用示例：
<div class="archives">
    <h3>数据归档</h3>
    {#foreach from=get_archives() key=year item=months#}
        <div class="year-group">
            <h4>{#$year#}年</h4>
            <ul class="month-list">
            {#foreach from=$months key=month item=count#}
                <li>
                    <a href="{#arc_link year=$year month=$month#}" title="{#$year#}年{#$month#}月收录{#$count#}个网站">
                        {#$month#}月 ({#$count#})
                    </a>
                </li>
            {#/foreach#}
            </ul>
        </div>
    {#/foreach#}
</div>

【5. 广告代码函数 - get_adcode()】
函数原型：get_adcode($ad_id)

参数说明：
- $ad_id：广告位ID（后台广告管理中设置）

使用示例：
<!-- 顶部横幅广告 -->
<div class="ad-banner">
    {#get_adcode(1)#}
</div>

<!-- 侧边栏广告 -->
<div class="sidebar-ad">
    {#get_adcode(2)#}
</div>

<!-- 内容页广告 -->
<div class="content-ad">
    {#get_adcode(3)#}
</div>

【6. 自定义标签 - $label】
用法：{#$label.标签名称#}

说明：调用后台自定义标签管理中添加的标签内容

使用示例：
<!-- 调用名为"google_analytics"的自定义标签 -->
<div class="analytics">
    {#$label.google_analytics#}
</div>

<!-- 调用名为"custom_js"的自定义标签 -->
<script type="text/javascript">
    {#$label.custom_js#}
</script>

<!-- 调用名为"contact_info"的自定义标签 -->
<div class="contact">
    {#$label.contact_info#}
</div>

【7. 站点统计 - $stat】
用法：{#$stat.统计项#}

可用统计项：
- category：分类总数
- website：网站总数（已审核）
- article：文章总数
- apply：待审核网站数
- vip：VIP网站数
- recommend：推荐网站数
- blacklist：黑名单网站数
- rejected：审核不通过网站数
- user：用户总数
- adver：广告总数
- link：友情链接总数
- feedback：反馈总数
- label：自定义标签总数
- page：单页总数

使用示例：
<div class="site-stats">
    <div class="stat-item">
        <span class="number">{#$stat.category#}</span>
        <span class="label">个分类</span>
    </div>
    <div class="stat-item">
        <span class="number">{#$stat.website#}</span>
        <span class="label">个网站</span>
    </div>
    <div class="stat-item">
        <span class="number">{#$stat.user#}</span>
        <span class="label">位用户</span>
    </div>
</div>

<!-- 完整统计信息 -->
<div class="full-stats">
    <p>本站共收录 <strong>{#$stat.website#}</strong> 个网站，分布在 <strong>{#$stat.category#}</strong> 个分类中</p>
    <p>其中推荐网站 <strong>{#$stat.recommend#}</strong> 个，VIP网站 <strong>{#$stat.vip#}</strong> 个</p>
    <p>注册用户 <strong>{#$stat.user#}</strong> 位，待审核网站 <strong>{#$stat.apply#}</strong> 个</p>
</div>

═══════════════════════════════════════════════════════════════════════════════
                              三、页面专用标签
═══════════════════════════════════════════════════════════════════════════════

【列表页通用标签】
适用页面：directory.html, update.html, archives.html, search.html

{#$weblist#}          ─── 网站列表数组（包含分页后的网站数据）
{#$showpage#}         ─── 分页导航HTML代码

使用示例：
<div class="website-list">
{#foreach from=$weblist item=item name=list_website#}
    <div class="list-item{#if $smarty.foreach.list_website.iteration % 2 == 1#} odd{#/if#}">
        <div class="site-thumb">
            <a href="{#$item.web_link#}">
                <img src="{#$item.web_pic#}" width="100" height="80" alt="{#$item.web_name#}" />
            </a>
        </div>
        <div class="site-info">
            <h3><a href="{#$item.web_link#}" title="{#$item.web_name#}">{#$item.web_name#}</a></h3>
            <p class="intro">{#$item.web_intro#}</p>
            <div class="meta">
                <a href="{#$item.web_url#}" target="_blank" class="visit">{#$item.web_url#}</a>
                <span class="time">{#$item.web_utime#}</span>
            </div>
        </div>
        <div class="site-stats">
            <div class="stat-item">
                <span class="label">PageRank：</span>
                <span class="value">{#$item.web_prank#}/10</span>
            </div>
            <div class="stat-item">
                <span class="label">AlexaRank：</span>
                <span class="value">{#$item.web_arank#}</span>
            </div>
            <div class="stat-item">
                <span class="label">人气指数：</span>
                <span class="value">{#$item.web_views#}</span>
            </div>
            <div class="stat-item">
                <span class="label">收录时间：</span>
                <span class="value">{#$item.web_utime#}</span>
            </div>
        </div>
    </div>
{#foreachelse#}
    <div class="no-data">该目录下暂无内容！</div>
{#/foreach#}
</div>

<!-- 分页导航 -->
<div class="pagination">
    {#$showpage#}
</div>

【首页专用标签 - index.html】
首页可以使用所有通用标签和函数标签，无特殊限制。

【分类页专用标签 - directory.html】
{#$category_name#}    ─── 当前分类名称
{#$child_category#}   ─── 当前分类下的子分类数组

子分类字段：
- $cate_id：分类ID
- $cate_name：分类名称
- $cate_postcount：分类下网站数量
- $cate_link：分类链接

使用示例：
<div class="category-header">
    <h1>{#$category_name#}</h1>
    <div class="category-nav">
        <span class="current">当前分类：{#$category_name#}</span>
    </div>
</div>

<!-- 子分类导航 -->
<div class="sub-categories">
    <h3>子分类</h3>
    <ul class="sub-cate-list">
    {#foreach from=$child_category item=sub#}
        <li{#if $cate_id == $sub.cate_id#} class="current"{#/if#}>
            {#if $cate_id == $sub.cate_id#}
                <span class="current-cate">{#$sub.cate_name#} ({#$sub.cate_postcount#})</span>
            {#else#}
                <a href="{#$sub.cate_link#}" title="{#$sub.cate_name#}">
                    {#$sub.cate_name#} ({#$sub.cate_postcount#})
                </a>
            {#/if#}
        </li>
    {#/foreach#}
    </ul>
</div>

【更新页专用标签 - update.html】
{#$days#}             ─── 当前选择的时间周期
{#$timescope#}        ─── 时间范围选择数组

时间范围字段：
- $time_id：时间周期ID
- $time_text：时间周期名称（如：24小时内、3天内、1周内）
- $time_link：时间周期链接

使用示例：
<div class="update-filter">
    <h2>最近更新</h2>
    <ul class="time-filter">
    {#foreach from=$timescope item=time key=k#}
        <li{#if $days == $time.time_id#} class="active"{#/if#}>
            {#if $days == $time.time_id#}
                <span class="current">{#$time.time_text#}</span>
            {#else#}
                <a href="{#$time.time_link#}">{#$time.time_text#}</a>
            {#/if#}
        </li>
    {#/foreach#}
    </ul>
</div>

【搜索页专用标签 - search.html】
{#$search_word#}      ─── 搜索关键词
{#$search_type#}      ─── 搜索类型（website-网站搜索, tags-标签搜索）
{#$total_found#}      ─── 搜索结果总数

使用示例：
<div class="search-result">
    <div class="search-info">
        <h2>搜索结果</h2>
        <p>关键词：<strong>{#$search_word#}</strong>，找到 <strong>{#$total_found#}</strong> 个相关网站</p>
    </div>

    <!-- 搜索结果列表使用 $weblist 和 $showpage -->
</div>

【网站详情页专用标签 - website.html】
{#$website#}          ─── 当前网站的详细信息数组

网站详情字段：
- $web_id：网站ID
- $web_name：网站名称
- $web_url：网站URL
- $web_pic：网站缩略图
- $web_intro：网站简介
- $web_ai_intro：AI生成简介
- $web_tags：网站标签数组
- $web_ctime：收录时间
- $web_views：浏览次数
- $web_grank：Google PageRank
- $web_brank：百度权重
- $web_srank：搜狗权重
- $web_arank：Alexa排名
- $cate_name：所属分类

使用示例：
<div class="website-detail">
    <div class="site-header">
        <div class="site-thumb">
            <img src="{#$website.web_pic#}" alt="{#$website.web_name#}" />
        </div>
        <div class="site-info">
            <h1>{#$website.web_name#}</h1>
            <p class="site-url">
                <a href="{#$website.web_url#}" target="_blank" rel="nofollow">{#$website.web_url#}</a>
            </p>
            <div class="site-meta">
                <span class="category">分类：{#$website.cate_name#}</span>
                <span class="views">浏览：{#$website.web_views#}</span>
                <span class="time">收录：{#$website.web_ctime#}</span>
            </div>
        </div>
    </div>

    <div class="site-content">
        <div class="site-intro">
            <h3>网站简介</h3>
            <p>{#$website.web_intro#}</p>
            {#if $website.web_ai_intro#}
                <div class="ai-intro">
                    <h4>AI智能简介</h4>
                    <p>{#$website.web_ai_intro#}</p>
                </div>
            {#/if#}
        </div>

        {#if $website.web_tags#}
        <div class="site-tags">
            <h3>相关标签</h3>
            <div class="tag-list">
            {#foreach from=$website.web_tags item=tag#}
                <a href="{#$tag.tag_link#}" class="tag">{#$tag.tag_name#}</a>
            {#/foreach#}
            </div>
        </div>
        {#/if#}

        <div class="site-stats">
            <h3>网站数据</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="label">Google PR</span>
                    <span class="value">{#$website.web_grank#}/10</span>
                </div>
                <div class="stat-item">
                    <span class="label">百度权重</span>
                    <span class="value">{#$website.web_brank#}</span>
                </div>
                <div class="stat-item">
                    <span class="label">搜狗权重</span>
                    <span class="value">{#$website.web_srank#}</span>
                </div>
                <div class="stat-item">
                    <span class="label">Alexa排名</span>
                    <span class="value">{#$website.web_arank#}</span>
                </div>
            </div>
        </div>
    </div>
</div>

═══════════════════════════════════════════════════════════════════════════════
                              四、Smarty内置标签
═══════════════════════════════════════════════════════════════════════════════

【循环控制】
{#foreach from=数组 item=项目 key=键名 name=循环名#}
    <!-- 循环体 -->
    {#if $smarty.foreach.循环名.first#}第一项{#/if#}
    {#if $smarty.foreach.循环名.last#}最后一项{#/if#}
    {#$smarty.foreach.循环名.iteration#} <!-- 当前循环次数 -->
    {#$smarty.foreach.循环名.index#}     <!-- 当前索引（从0开始） -->
{#foreachelse#}
    <!-- 数组为空时显示 -->
{#/foreach#}

【条件判断】
{#if 条件#}
    <!-- 条件为真时执行 -->
{#elseif 其他条件#}
    <!-- 其他条件为真时执行 -->
{#else#}
    <!-- 所有条件都不满足时执行 -->
{#/if#}

【变量修饰符】
{#$变量|修饰符#}
{#$变量|修饰符:参数#}

常用修饰符：
- truncate:长度 - 截取字符串
- date_format:格式 - 格式化日期
- escape - HTML转义
- nl2br - 换行转<br>
- strip_tags - 去除HTML标签
- upper - 转大写
- lower - 转小写
- capitalize - 首字母大写

使用示例：
{#$web_intro|truncate:100:"..."|escape#}
{#$web_ctime|date_format:"%Y-%m-%d %H:%M"#}
{#$web_name|upper#}

【包含文件】
{#include file="文件名.html"#}
{#include file="header.html" title="页面标题"#}

【赋值操作】
{#assign var="变量名" value="值"#}
{#assign var="current_time" value=$smarty.now#}

═══════════════════════════════════════════════════════════════════════════════
                              五、高级应用技巧
═══════════════════════════════════════════════════════════════════════════════

【1. 条件显示内容】
<!-- 根据网站状态显示不同样式 -->
{#foreach from=get_websites(0, 10) item=site#}
    <div class="site-item {#if $site.web_isbest#}best{#/if#} {#if $site.web_ispay#}vip{#/if#}">
        <h3>{#$site.web_name#}</h3>
        {#if $site.web_isbest#}<span class="badge best">推荐</span>{#/if#}
        {#if $site.web_ispay#}<span class="badge vip">VIP</span>{#/if#}
    </div>
{#/foreach#}

【2. 多条件筛选】
<!-- 显示特定分类的推荐网站 -->
{#assign var="tech_sites" value=get_websites(1, 8, false, true)#}
{#if $tech_sites#}
    <div class="tech-recommend">
        <h3>技术推荐</h3>
        {#foreach from=$tech_sites item=site#}
            <a href="{#$site.web_link#}">{#$site.web_name#}</a>
        {#/foreach#}
    </div>
{#/if#}

【3. 数据统计显示】
<!-- 显示各分类网站数量 -->
{#foreach from=get_categories(0, 0, false) item=cate#}
    {#if $cate.cate_postcount > 0#}
        <div class="cate-stat">
            <span class="name">{#$cate.cate_name#}</span>
            <span class="count">{#$cate.cate_postcount#}</span>
            <div class="progress">
                <div class="bar" style="width: {#math equation="x/y*100" x=$cate.cate_postcount y=$stat.website#}%"></div>
            </div>
        </div>
    {#/if#}
{#/foreach#}

【4. 响应式布局】
<!-- 根据设备类型显示不同数量 -->
<div class="responsive-grid">
{#assign var="site_count" value=12#}
{#* 移动端显示6个，桌面端显示12个 *#}
{#foreach from=get_websites(0, $site_count) item=site name=grid#}
    <div class="grid-item">
        <a href="{#$site.web_link#}">
            <img src="{#$site.web_pic#}" alt="{#$site.web_name#}" />
            <span>{#$site.web_name#}</span>
        </a>
    </div>
    {#if $smarty.foreach.grid.iteration % 3 == 0#}
        <div class="clearfix visible-mobile"></div>
    {#/if#}
    {#if $smarty.foreach.grid.iteration % 4 == 0#}
        <div class="clearfix visible-tablet"></div>
    {#/if#}
    {#if $smarty.foreach.grid.iteration % 6 == 0#}
        <div class="clearfix visible-desktop"></div>
    {#/if#}
{#/foreach#}
</div>

【5. SEO优化】
<!-- 动态生成页面标题和描述 -->
{#if $category_name#}
    {#assign var="page_title" value="`$category_name` - `$site_name`"#}
    {#assign var="page_desc" value="`$category_name`分类下的优质网站推荐，包含`$stat.website`个精选网站"#}
{#else#}
    {#assign var="page_title" value=$site_title#}
    {#assign var="page_desc" value=$site_description#}
{#/if#}

<title>{#$page_title#}</title>
<meta name="description" content="{#$page_desc#}" />

<!-- 结构化数据 -->
{#if $website#}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "{#$website.web_name#}",
    "url": "{#$website.web_url#}",
    "description": "{#$website.web_intro|escape:'javascript'#}",
    "datePublished": "{#$website.web_ctime|date_format:'%Y-%m-%d'#}"
}
</script>
{#/if#}

【6. 缓存控制】
<!-- 对于更新频繁的内容，可以设置不同的缓存策略 -->
{#* 最新网站列表，缓存5分钟 *#}
{#assign var="cache_id" value="latest_sites_`$smarty.now|date_format:'%Y%m%d%H%i'`"#}

<!-- 热门推荐，缓存1小时 -->
{#assign var="hot_cache" value="hot_sites_`$smarty.now|date_format:'%Y%m%d%H'`"#}

【7. 多语言支持】
<!-- 如果需要多语言支持 -->
{#assign var="lang" value=$smarty.get.lang|default:'zh'#}
{#if $lang == 'en'#}
    {#assign var="site_title_lang" value=$cfg.site_title_en#}
{#else#}
    {#assign var="site_title_lang" value=$cfg.site_title#}
{#/if#}

【8. 错误处理】
<!-- 优雅处理数据为空的情况 -->
{#assign var="latest_sites" value=get_websites(0, 10)#}
{#if $latest_sites && count($latest_sites) > 0#}
    <div class="latest-sites">
        {#foreach from=$latest_sites item=site#}
            <!-- 网站项目 -->
        {#/foreach#}
    </div>
{#else#}
    <div class="no-data">
        <p>暂无最新网站，请稍后再来查看。</p>
    </div>
{#/if#}

═══════════════════════════════════════════════════════════════════════════════
                              六、性能优化建议
═══════════════════════════════════════════════════════════════════════════════

【1. 合理使用缓存】
- 启用Smarty模板缓存：在 source/include/smarty.php 中设置
- 对于数据变化不频繁的页面，设置较长的缓存时间
- 使用 cache_id 区分不同的缓存版本

【2. 减少数据库查询】
- 在一个页面中，尽量减少函数调用次数
- 使用 assign 将函数结果赋值给变量，避免重复调用
- 合理设置函数参数，只获取需要的数据

示例：
{#assign var="hot_sites" value=get_websites(0, 8, false, true)#}
{#assign var="new_sites" value=get_websites(0, 10, false, false, 'ctime')#}

【3. 图片优化】
- 使用适当的图片尺寸
- 为图片添加 alt 属性
- 考虑使用懒加载

【4. 代码优化】
- 避免在循环中进行复杂计算
- 使用条件判断减少不必要的HTML输出
- 合理使用 strip 去除多余空白

═══════════════════════════════════════════════════════════════════════════════
                              七、常见问题解答
═══════════════════════════════════════════════════════════════════════════════

【Q1：如何修改模板标签分隔符？】
A：编辑 source/include/smarty.php 文件，修改以下两行：
$smarty->left_delimiter = "{#";
$smarty->right_delimiter = "#}";

【Q2：模板缓存如何清理？】
A：删除 data/cache/ 目录下的缓存文件，或在后台系统管理中清理缓存。

【Q3：如何在模板中调用自定义PHP函数？】
A：需要在相应的模块文件中定义函数，然后通过 $smarty->assign() 将结果传递给模板。

【Q4：为什么某些标签不显示内容？】
A：检查以下几点：
1. 标签语法是否正确
2. 数据是否存在
3. 权限设置是否正确
4. 缓存是否需要清理

【Q5：如何实现分页功能？】
A：使用 $weblist 和 $showpage 标签，系统会自动处理分页逻辑。

【Q6：如何自定义404页面？】
A：在模板目录中创建 404.html 文件，使用相应的模板标签。

═══════════════════════════════════════════════════════════════════════════════
                              八、版本更新说明
═══════════════════════════════════════════════════════════════════════════════

【Version 2.0 更新内容】
1. 新增AI智能简介字段 web_ai_intro
2. 增强网站违规检测功能
3. 优化搜索功能，支持标签搜索
4. 新增多种排序方式
5. 改进缓存机制
6. 增强SEO功能

【兼容性说明】
- 向下兼容旧版本模板
- 新增字段在旧模板中不会报错
- 建议升级模板以使用新功能

═══════════════════════════════════════════════════════════════════════════════
                                  结语
═══════════════════════════════════════════════════════════════════════════════

本文档详细介绍了35DIR系统的所有模板标签和使用方法。在实际开发中，建议：

1. 先熟悉基础标签的使用
2. 根据页面需求选择合适的函数
3. 注意性能优化和缓存策略
4. 保持代码的可读性和维护性
5. 定期更新和备份模板文件

如有疑问，请参考Smarty官方文档或联系技术支持。

文档编写：35DIR开发团队
最后更新：2024年
版权所有：保留所有权利
