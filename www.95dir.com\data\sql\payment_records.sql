-- 付费记录表
CREATE TABLE IF NOT EXISTS `dir_payment_records` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `web_id` int(10) unsigned NOT NULL DEFAULT '0',
  `web_name` varchar(100) NOT NULL DEFAULT '',
  `web_url` varchar(255) NOT NULL DEFAULT '',
  `payment_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '付费类型：1=VIP，2=推荐，3=快审',
  `payment_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '付费金额',
  `payment_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '付费时间',
  `expire_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '到期时间',
  `operator` varchar(50) NOT NULL DEFAULT 'admin' COMMENT '操作员',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1=有效，0=已过期',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `created_at` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `web_id` (`web_id`),
  KEY `payment_type` (`payment_type`),
  KEY `payment_time` (`payment_time`),
  KEY `expire_time` (`expire_time`),
  KEY `status` (`status`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- 为现有websites表添加付费相关字段（如果不存在）
ALTER TABLE `dir_websites` 
ADD COLUMN `web_vip_expire` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'VIP到期时间',
ADD COLUMN `web_recommend_expire` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '推荐到期时间',
ADD COLUMN `web_fast_expire` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '快审到期时间';
