<?php
require('common.php');

$fileurl = 'payment_price.php';
$tempfile = 'payment_price.html';
$table = $DB->table('payment_prices');

// 检查价格配置表是否存在
$check_table = $DB->query("SHOW TABLES LIKE '$table'");
if ($DB->num_rows($check_table) == 0) {
    echo "<h2>价格配置管理</h2>";
    echo "<p style='color: red;'>价格配置表不存在，请先初始化数据库。</p>";
    echo "<p><a href='payment_init.php' style='background: #007bff; color: #fff; padding: 10px 15px; text-decoration: none; border-radius: 3px;'>点击初始化数据库</a></p>";
    echo "<p><a href='admin.php'>返回管理首页</a></p>";
    exit;
}

// 只有在表存在时才加载价格模块
require('../source/module/payment_price.php');

if (!isset($action)) $action = 'list';

/** 价格配置列表 */
if ($action == 'list') {
    $pagetitle = '价格配置管理';

    // 获取当前有效价格
    $current_prices = get_all_current_prices();

    // 获取价格历史
    $price_history = get_price_history();

    // 格式化显示
    foreach ($current_prices as &$price) {
        $price['price_display'] = format_price_display($price['price'], $price['unit']);
    }

    foreach ($price_history as &$history) {
        $history['price_display'] = format_price_display($history['price'], $history['unit']);
        $history['service_type_name'] = get_service_type_name($history['service_type']);
    }

    // 服务类型数组
    $service_types = array(
        1 => 'VIP服务',
        2 => '推荐服务',
        3 => '快审服务'
    );

    $smarty->assign('current_prices', $current_prices);
    $smarty->assign('price_history', $price_history);
    $smarty->assign('service_types', $service_types);
    $smarty->assign('h_action', 'add');
}

/** 添加价格配置 */
if ($action == 'add') {
    $pagetitle = '添加价格配置';
    
    $service_types = array(
        1 => 'VIP服务',
        2 => '推荐服务', 
        3 => '快审服务'
    );
    
    $smarty->assign('service_types', $service_types);
    $smarty->assign('h_action', 'saveadd');
}

/** 编辑价格配置 */
if ($action == 'edit') {
    $pagetitle = '编辑价格配置';
    $id = intval($_GET['id']);
    
    $price_config = $DB->fetch_one("SELECT * FROM $table WHERE id = $id");
    if (!$price_config) {
        msgbox('价格配置不存在！', $fileurl);
    }
    
    $service_types = array(
        1 => 'VIP服务',
        2 => '推荐服务',
        3 => '快审服务'
    );
    
    $price_config['effective_date'] = date('Y-m-d', $price_config['effective_time']);
    $price_config['effective_hour'] = date('H:i', $price_config['effective_time']);
    
    $smarty->assign('price_config', $price_config);
    $smarty->assign('service_types', $service_types);
    $smarty->assign('h_action', 'saveedit');
}

/** 保存添加 */
if ($action == 'saveadd') {
    $service_type = intval($_POST['service_type']);
    $service_name = trim($_POST['service_name']);
    $price = floatval($_POST['price']);
    $unit = trim($_POST['unit']);
    $duration_days = intval($_POST['duration_days']);
    $effective_date = trim($_POST['effective_date']);
    $effective_time_str = trim($_POST['effective_time']);
    $remark = trim($_POST['remark']);
    
    // 验证输入
    if (!$service_type || !$service_name || $price <= 0) {
        msgbox('请填写完整的配置信息！');
    }
    
    if (empty($effective_date)) {
        msgbox('请选择生效日期！');
    }
    
    // 转换生效时间
    $effective_datetime = $effective_date . ' ' . ($effective_time_str ?: '00:00:00');
    $effective_timestamp = strtotime($effective_datetime);
    if (!$effective_timestamp) {
        msgbox('生效时间格式错误！');
    }
    
    // 检查是否存在冲突
    if (check_price_conflict($service_type, $effective_timestamp)) {
        msgbox('该服务类型在指定时间已存在价格配置！');
    }
    
    // 添加配置
    if (add_price_config($service_type, $service_name, $price, $unit, $duration_days, $effective_timestamp, $myself['user_email'], $remark)) {
        msgbox('价格配置添加成功！', $fileurl);
    } else {
        msgbox('价格配置添加失败！');
    }
}

/** 保存编辑 */
if ($action == 'saveedit') {
    $id = intval($_POST['id']);
    $service_type = intval($_POST['service_type']);
    $service_name = trim($_POST['service_name']);
    $price = floatval($_POST['price']);
    $unit = trim($_POST['unit']);
    $duration_days = intval($_POST['duration_days']);
    $effective_date = trim($_POST['effective_date']);
    $effective_time_str = trim($_POST['effective_time']);
    $remark = trim($_POST['remark']);
    
    // 验证输入
    if (!$id || !$service_type || !$service_name || $price <= 0) {
        msgbox('请填写完整的配置信息！');
    }
    
    if (empty($effective_date)) {
        msgbox('请选择生效日期！');
    }
    
    // 转换生效时间
    $effective_datetime = $effective_date . ' ' . ($effective_time_str ?: '00:00:00');
    $effective_timestamp = strtotime($effective_datetime);
    if (!$effective_timestamp) {
        msgbox('生效时间格式错误！');
    }
    
    // 检查是否存在冲突
    if (check_price_conflict($service_type, $effective_timestamp, $id)) {
        msgbox('该服务类型在指定时间已存在价格配置！');
    }
    
    // 更新配置
    $data = array(
        'service_type' => $service_type,
        'service_name' => $service_name,
        'price' => $price,
        'unit' => $unit,
        'duration_days' => $duration_days,
        'effective_time' => $effective_timestamp,
        'operator' => $myself['user_email'],
        'remark' => $remark
    );
    
    if ($DB->update($table, $data, array('id' => $id))) {
        msgbox('价格配置更新成功！', $fileurl);
    } else {
        msgbox('价格配置更新失败！');
    }
}

/** 停用配置 */
if ($action == 'disable') {
    $id = intval($_GET['id']);
    if (update_price_status($id, 0)) {
        msgbox('价格配置已停用！', $fileurl);
    } else {
        msgbox('操作失败！');
    }
}

/** 启用配置 */
if ($action == 'enable') {
    $id = intval($_GET['id']);
    if (update_price_status($id, 1)) {
        msgbox('价格配置已启用！', $fileurl);
    } else {
        msgbox('操作失败！');
    }
}

/** 删除配置 */
if ($action == 'delete') {
    $id = intval($_GET['id']);
    
    // 检查是否有相关的付费记录
    $payment_table = $DB->table('payment_records');
    $config = $DB->fetch_one("SELECT service_type, effective_time FROM $table WHERE id = $id");
    if ($config) {
        $count = $DB->get_count($payment_table, "payment_type = {$config['service_type']} AND payment_time >= {$config['effective_time']}");
        if ($count > 0) {
            msgbox('该价格配置已有相关付费记录，不能删除！请使用停用功能。');
        }
    }
    
    if ($DB->delete($table, array('id' => $id))) {
        msgbox('价格配置删除成功！', $fileurl);
    } else {
        msgbox('删除失败！');
    }
}

smarty_output($tempfile);
?>
