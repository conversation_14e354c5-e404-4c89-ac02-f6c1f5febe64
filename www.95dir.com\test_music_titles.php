<?php
// 测试音乐标题提取功能
define('IN_IWEBDIR', true);
define('APP_PATH', './source/');

require('./source/init.php');
require('./source/module/article.php');

echo "<h2>测试音乐标题提取功能</h2>\n";

// 测试不同格式的音乐内容
$test_contents = array(
    array(
        'name' => '带标题的链接格式1',
        'content' => '
        这里有几首好听的歌：
        周杰伦 - 青花瓷：https://music.163.com/song/media/outer/url?id=185668
        邓紫棋 - 泡沫：https://example.com/music/paomo.mp3
        林俊杰 - 江南 https://y.qq.com/n/yqq/song/001234567.html
        '
    ),
    array(
        'name' => '带标题的链接格式2',
        'content' => '
        推荐歌曲：
        1. 陈奕迅《十年》 https://music.163.com/song/media/outer/url?id=185692
        2. 王菲《红豆》：https://example.com/music/hongdou.mp3
        3. 张学友 - 吻别 https://www.kugou.com/song/wenbie.html
        '
    ),
    array(
        'name' => 'HTML audio标签格式',
        'content' => '
        <audio title="许嵩 - 有何不可" src="https://music.163.com/song/media/outer/url?id=123456" controls></audio>
        <audio controls><source title="薛之谦 - 演员" src="https://example.com/music/yanyuan.mp3" type="audio/mpeg"></audio>
        '
    ),
    array(
        'name' => '纯链接格式（无标题）',
        'content' => '
        这些是纯链接：
        https://music.163.com/song/media/outer/url?id=999999
        https://example.com/music/unknown.mp3
        https://y.qq.com/n/yqq/song/unknown.html
        '
    )
);

foreach ($test_contents as $index => $test) {
    echo "<h3>" . ($index + 1) . ". {$test['name']}</h3>\n";
    echo "<h4>原始内容：</h4>\n";
    echo "<div style='background:#f5f5f5; padding:10px; margin:10px 0;'>" . nl2br(htmlspecialchars($test['content'])) . "</div>\n";
    
    echo "<h4>提取结果：</h4>\n";
    $music_data = extract_music_urls_with_titles($test['content']);
    
    if (!empty($music_data)) {
        echo "<table border='1' style='border-collapse:collapse; width:100%;'>\n";
        echo "<tr><th>序号</th><th>提取的标题</th><th>URL</th></tr>\n";
        foreach ($music_data as $i => $data) {
            echo "<tr>";
            echo "<td>" . ($i + 1) . "</td>";
            echo "<td>" . htmlspecialchars($data['title'] ?: '(无标题)') . "</td>";
            echo "<td>" . htmlspecialchars($data['url']) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p style='color: red;'>没有提取到音乐数据</p>\n";
    }
    echo "<hr>\n";
}

// 创建测试文章
echo "<h3>创建测试文章</h3>\n";

// 删除现有的测试文章
$DB->query("DELETE FROM ".$DB->table('articles')." WHERE art_title LIKE '标题提取测试%'");

$test_article = array(
    'title' => '标题提取测试 - 多种格式',
    'content' => '
    这篇文章包含多种音乐链接格式：
    
    1. 周杰伦 - 青花瓷：https://music.163.com/song/media/outer/url?id=185668
    2. 邓紫棋《泡沫》 https://example.com/music/paomo.mp3
    3. 林俊杰 - 江南 https://y.qq.com/n/yqq/song/001234567.html
    
    HTML格式：
    <audio title="陈奕迅 - 十年" src="https://music.163.com/song/media/outer/url?id=185692" controls></audio>
    
    纯链接（应该使用文章标题）：
    https://example.com/music/unknown.mp3
    '
);

$title = $DB->escape_string($test_article['title']);
$content = $DB->escape_string($test_article['content']);
$intro = $DB->escape_string('测试音乐标题提取功能的文章');

$sql = "INSERT INTO ".$DB->table('articles')." 
        (user_id, cate_id, art_title, art_tags, art_intro, art_content, art_status, art_ctime, art_utime) 
        VALUES 
        (1, 318, '$title', '测试,音乐,标题提取', '$intro', '$content', 3, ".time().", ".time().")";

if ($DB->query($sql)) {
    $art_id = $DB->insert_id();
    echo "<p style='color: green;'>✓ 测试文章创建成功 (ID: $art_id)</p>\n";
    
    // 测试get_music_links函数
    echo "<h3>测试get_music_links函数结果</h3>\n";
    $music_links = get_music_links(318, 20);
    
    echo "<table border='1' style='border-collapse:collapse; width:100%;'>\n";
    echo "<tr><th>显示标题</th><th>提取的标题</th><th>原始文章标题</th><th>URL</th></tr>\n";
    
    foreach ($music_links as $link) {
        if (strpos($link['title'], '标题提取测试') !== false) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($link['title']) . "</td>";
            echo "<td>" . htmlspecialchars($link['extracted_title'] ?: '(无)') . "</td>";
            echo "<td>" . htmlspecialchars($link['original_title']) . "</td>";
            echo "<td>" . htmlspecialchars($link['url']) . "</td>";
            echo "</tr>\n";
        }
    }
    echo "</table>\n";
    
} else {
    echo "<p style='color: red;'>✗ 创建测试文章失败: " . $DB->error() . "</p>\n";
}

echo "<h3>预期效果说明</h3>\n";
echo "<ul>\n";
echo "<li><strong>有标题的链接</strong>：应该显示提取的音乐标题，如 '周杰伦 - 青花瓷 1 [流行歌曲]'</li>\n";
echo "<li><strong>无标题的链接</strong>：应该显示文章标题，如 '标题提取测试 - 多种格式 5 [流行歌曲]'</li>\n";
echo "<li><strong>序号</strong>：应该是连续的 1、2、3、4、5</li>\n";
echo "</ul>\n";

echo "<h3>测试AJAX接口</h3>\n";
echo "<p><a href='?mod=ajaxget&type=music_list' target='_blank'>点击查看AJAX接口返回的数据</a></p>\n";
?>
