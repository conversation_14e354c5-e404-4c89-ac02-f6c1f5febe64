<?php
/*
 * <AUTHOR>  　 @祥💥　技术支持
 * @Mail         : <EMAIL>
 * @Date         : 2025-02-11 08:56:39
 * @LastEditTime : 2025-02-14 14:50:41
 * @LastEditors  :  　 @祥💥　技术支持
 * @Description  : 
 * @FilePath     : \35dir\member\module\website.php
 * It's up to you ^_^
 * Copyright (c) 2025 by <EMAIL>, All Rights Reserved. 
 */
if (!defined('IN_HANFOX')) exit('Access Denied');

require(APP_PATH.'module/category.php');
require(APP_PATH.'module/website.php');

$pageurl = '?mod=website';
$tplfile = 'website.html';
$table = $DB->table('websites');

$action = isset($_GET['act']) ? $_GET['act'] : 'list';
$smarty->assign('action', $action); 

if (!$smarty->isCached($tplfile)) {
	/** list */
	if ($action == 'list') {
		$pagename = '网站管理';
		$smarty->assign('site_title', $pagename.' - '.$options['site_name']);
		$smarty->assign('site_path', get_sitepath().' &raquo; '.$pagename);
		
		$pagesize = 10;
		$curpage = intval($_GET['page']);
		if ($curpage > 1) {
			$start = ($curpage - 1) * $pagesize;
		} else {
			$start = 0;
			$curpage = 1;
		}
		
		$where = "w.user_id=".$myself['user_id'];
	
		$websites = get_website_list($where, 'ctime', 'DESC', $start, $pagesize);
		$total = $DB->get_count($table.' w', $where);
		$showpage = showpage($pageurl, $total, $curpage, $pagesize);
		
		$smarty->assign('pagename', $pagename);
		$smarty->assign('websites', $websites);
		$smarty->assign('total', $total);
		$smarty->assign('showpage', $showpage);
	}
	
	/** add */
	if ($action == 'add') {
		$pagename = '网站提交';
		
		$smarty->assign('pagename', $pagename);
		$smarty->assign('site_title', $pagename.' - '.$options['site_name']);
		$smarty->assign('site_path', get_sitepath().' &raquo; '.$pagename);
		$smarty->assign('category_option', get_category_option('webdir', 0, 0, 0));	
		$smarty->assign('do', 'saveadd');
	}
	
	/** edit */
	if ($action == 'edit') {
		$pagename = '网站编辑';
		
		$web_id = intval($_GET['wid']);
		$where = "w.user_id=$myself[user_id] AND w.web_id=$web_id";
		$web = get_one_website($where);
		if (!$web) {
			msgbox('您要修改的内容不存在或无权限！');
		}
		$web['web_ip'] = long2ip($web['web_ip']);
		
		$smarty->assign('pagename', $pagename);
		$smarty->assign('site_title', $pagename.' - '.$options['site_title']);
		$smarty->assign('site_path', get_sitepath().' &raquo; '.$pagename);	
		$smarty->assign('category_option', get_category_option('webdir', 0, $web['cate_id'], 0));
		$smarty->assign('web', $web);	
		$smarty->assign('do', 'saveedit');
	}
	
	/** save */
	if (in_array($_POST['do'], array('saveadd', 'saveedit'))) {
		$cate_id = intval($_POST['cate_id']);
		$web_name = trim($_POST['web_name']);
		$web_url = trim($_POST['web_url']);
		$web_tags = trim($_POST['web_tags']);
		$web_intro = trim($_POST['web_intro']);
		$web_ip = trim($_POST['web_ip']);
		$web_grank = intval($_POST['web_grank']);
		$web_brank = intval($_POST['web_brank']);
		$web_srank = intval($_POST['web_srank']);
		$web_arank = intval($_POST['web_arank']);
		$web_time = time();
		
		if ($cate_id <= 0) {
			msgbox('请选择网站所属分类！');
		} else {
			$cate = get_one_category($cate_id);
			if ($cate['cate_childcount'] > 0) {
				msgbox('指定的分类下有子分类，请选择子分类进行操作！');
			}
		}
	
		if (empty($web_name)) {
			msgbox('请输入网站名称！');
		} else {
			// 检查网站名称长度（中文算2个字符）
			$name_length = 0;
			for ($i = 0; $i < mb_strlen($web_name, 'UTF-8'); $i++) {
				$char = mb_substr($web_name, $i, 1, 'UTF-8');
				if (ord($char) > 127) {
					$name_length += 2; // 中文字符算2个字符
				} else {
					$name_length += 1; // 英文字符算1个字符
				}
			}

			if ($name_length > 12) {
				msgbox('网站名称过长！最多12个字符（6个汉字）');
			}

			if (!censor_words($options['filter_words'], $web_name)) {
				msgbox('网站名称中含有非法关键词！');
			}
		}
		
		if (empty($web_url)) {
			msgbox('请输入网站域名！');
		} else {
			if (!is_valid_domain($web_url)) {
				msgbox('请输入正确的网站域名！');
			}
		}
		
		if (!empty($web_tags)) {
			if (!censor_words($options['filter_words'], $web_tags)) {
				msgbox('TAG标签中含有非法关键词！');
			}
			
			$web_tags = str_replace('，', ',', $web_tags);
			$web_tags = str_replace(',,', ',', $web_tags);
			if (substr($web_tags, -1) == ',') {
				$web_tags = substr($web_tags, 0, strlen($web_tags) - 1);
			}
		}
			
		if (empty($web_intro)) {
			msgbox('请输入网站简介！');
		} else {
			if (!censor_words($options['filter_words'], $web_intro)) {
				msgbox('网站简介中含有非法关键词！');	
			}
		}
		
		$web_ip = sprintf("%u", ip2long($web_ip));
		
		$web_data = array(
			'cate_id' => $cate_id,
			'user_id' => $myself['user_id'],
			'web_name' => $web_name,
			'web_url' => $web_url,
			'web_tags' => $web_tags,
			'web_intro' => $web_intro,
			'web_status' => 2,
			'web_ctime' => $web_time,
		);
		
		if ($_POST['do'] == 'saveadd') {
    		$query = $DB->query("SELECT web_id, web_name, web_status, web_ctime FROM $table WHERE web_url='$web_url'");
    		if ($DB->num_rows($query)) {
    			$existing_web = $DB->fetch_array($query);
    			$status_msg = '';

    			switch($existing_web['web_status']) {
    				case 1:
    					$status_msg = '该网站已被拉黑，无法重复提交！';
    					break;
    				case 2:
    					$status_msg = '该网站正在审核中，请勿重复提交！';
    					break;
    				case 3:
    					$status_msg = '该网站已收录（收录时间：' . date('Y-m-d', $existing_web['web_ctime']) . '），请勿重复提交！';
    					break;
    				case 4:
    					$status_msg = '该网站审核不通过，请修改后重新提交！';
    					break;
    				default:
    					$status_msg = '您所提交的网站已存在！';
    			}

        		msgbox($status_msg);
    		}
			$DB->insert($table, $web_data);
			$insert_id = $DB->insert_id();
			
			$stat_data = array(
				'web_id' => $insert_id,
				'web_ip' => $web_ip,
				'web_grank' => $web_grank,
				'web_brank' => $web_brank,
				'web_srank' => $web_srank,
				'web_arank' => $web_arank,
				'web_utime' => $web_time,
			);
			$DB->insert($DB->table('webdata'), $stat_data);
            $options['wechat_robot'] ? send_msg($web_data,$options,'wechat_robot') : '';
            $options['dingding_robot'] ? send_msg($web_data,$options,'dingding_robot') : '';

            // 发送邮件通知管理员
            try {
                send_admin_email_notification($web_data, $options, '提交');
            } catch (Exception $e) {
                // 记录错误但不影响用户体验
                error_log("邮件通知发送异常: " . $e->getMessage(), 3, ROOT_PATH . "data/email_error.log");
            }

			msgbox('网站提交成功！', $pageurl);
		} elseif ($_POST['do'] == 'saveedit') {
			$web_id = intval($_POST['web_id']);
			$where = array('web_id' => $web_id);
			$DB->update($table, $web_data, $where);
			
			$stat_data = array(
				'web_ip' => $web_ip,
				'web_grank' => $web_grank,
				'web_brank' => $web_brank,
				'web_srank' => $web_srank,
				'web_arank' => $web_arank,
				'web_utime' => $web_time,
			);
			$DB->update($DB->table('webdata'), $stat_data, $where);
			$options['wechat_robot'] ? send_msg($web_data,$options,'wechat_robot') : '';
            $options['dingding_robot'] ? send_msg($web_data,$options,'dingding_robot') : '';

            // 发送邮件通知管理员
            try {
                send_admin_email_notification($web_data, $options, '编辑');
            } catch (Exception $e) {
                // 记录错误但不影响用户体验
                error_log("邮件通知发送异常: " . $e->getMessage(), 3, ROOT_PATH . "data/email_error.log");
            }

			msgbox('网站编辑成功！', $pageurl);
		}
	}
}

// 发送审核信息
function send_msg($web_data, $options, $type='wechat_robot'){
    $action = $_POST['do'] == 'saveadd' ? '新站点' : '编辑站点';
    $messageData = [
        "msgtype" => "text",
        "text" => [
            "content" => "有".$action."需要审核\n站点名称：".$web_data['web_name']."\n站点地址：".$web_data['web_url']."\n提交时间：".date('Y-m-d H:i:s',$web_data['web_ctime']),
        ]
    ];
    $jsonData = json_encode($messageData);
    $ch = curl_init($type == 'wechat_robot' ? $options['wechat_robot'] : $options['dingding_robot']);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json; charset=utf-8',
        'Content-Length: ' . strlen($jsonData)
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    curl_close($ch);
}

// 发送管理员邮件通知
function send_admin_email_notification($web_data, $options, $action_type) {
    global $smarty, $myself, $DB;

    // 确保必要的全局变量存在
    if (!isset($myself)) {
        $myself = array('user_id' => 0);
    }

    // 检查必要的配置
    $required_configs = ['smtp_host', 'smtp_port', 'smtp_auth', 'smtp_user', 'smtp_pass', 'admin_email'];
    foreach ($required_configs as $config) {
        if (empty($options[$config])) {
            return false;
        }
    }

    // 包含邮件发送函数
    require_once(APP_PATH.'include/sendmail.php');

    // 获取用户信息
    $user_email = '未知用户';
    if (isset($myself['user_id']) && $myself['user_id']) {
        $user_info = $DB->fetch_one("SELECT user_email FROM ".$DB->table('users')." WHERE user_id=".$myself['user_id']);
        if ($user_info && isset($user_info['user_email'])) {
            $user_email = $user_info['user_email'];
        }
    }

    // 构建邮件内容
    $subject = '[' . $options['site_name'] . '] 有新的网站' . $action_type . '需要审核';

    // 优化邮件内容排版
    $mailbody = '
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>网站' . $action_type . '通知</title>
    <style>
        body { font-family: "Microsoft YaHei", Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f5f5f5; }
        .container { max-width: 650px; margin: 20px auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; font-weight: 300; }
        .content { padding: 30px; }
        .greeting { font-size: 16px; margin-bottom: 20px; color: #555; }
        .info-table { width: 100%; border-collapse: collapse; margin: 25px 0; background: #fff; border-radius: 6px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .info-table td { padding: 15px; border-bottom: 1px solid #eee; vertical-align: top; }
        .info-table tr:last-child td { border-bottom: none; }
        .label { background: #f8f9fa; font-weight: 600; color: #495057; width: 120px; border-right: 1px solid #eee; }
        .value { color: #333; }
        .value a { color: #007bff; text-decoration: none; }
        .value a:hover { text-decoration: underline; }
        .action-btn { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 25px; text-decoration: none; border-radius: 25px; font-weight: 500; margin: 20px 0; transition: transform 0.2s; }
        .action-btn:hover { transform: translateY(-2px); color: white; text-decoration: none; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #6c757d; border-top: 1px solid #eee; }
        .footer a { color: #007bff; text-decoration: none; }
        .highlight { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 20px 0; border-radius: 0 4px 4px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>' . htmlspecialchars($options['site_name']) . '</h1>
            <p style="margin: 5px 0 0 0; opacity: 0.9;">网站' . $action_type . '审核通知</p>
        </div>

        <div class="content">
            <div class="greeting">
                <strong>尊敬的管理员，您好！</strong>
            </div>

            <div class="highlight">
                <strong>📝 有会员' . $action_type . '网站需要您审核</strong>
            </div>

            <p>以下是详细信息：</p>

            <table class="info-table">
                <tr>
                    <td class="label">📋 操作类型</td>
                    <td class="value"><strong style="color: #28a745;">' . $action_type . '</strong></td>
                </tr>
                <tr>
                    <td class="label">🌐 网站名称</td>
                    <td class="value"><strong>' . htmlspecialchars($web_data['web_name']) . '</strong></td>
                </tr>
                <tr>
                    <td class="label">🔗 网站地址</td>
                    <td class="value"><a href="' . htmlspecialchars($web_data['web_url']) . '" target="_blank">' . htmlspecialchars($web_data['web_url']) . '</a></td>
                </tr>
                <tr>
                    <td class="label">📄 网站简介</td>
                    <td class="value">' . htmlspecialchars(isset($web_data['web_intro']) ? $web_data['web_intro'] : '暂无简介') . '</td>
                </tr>
                <tr>
                    <td class="label">👤 提交用户</td>
                    <td class="value">' . htmlspecialchars($user_email) . '</td>
                </tr>
                <tr>
                    <td class="label">⏰ 提交时间</td>
                    <td class="value">' . date('Y年m月d日 H:i:s', $web_data['web_ctime']) . '</td>
                </tr>
            </table>

            <div style="text-align: center; margin: 30px 0;">
                <a href="' . $options['site_url'] . 'system/" target="_blank" class="action-btn">
                    🚀 立即前往后台审核
                </a>
            </div>

            <p style="color: #6c757d; font-size: 14px; margin-top: 30px;">
                💡 <strong>温馨提示：</strong>请及时处理网站审核，以提升用户体验。
            </p>
        </div>

        <div class="footer">
            <p>此邮件由 <a href="' . $options['site_url'] . '" target="_blank">' . htmlspecialchars($options['site_name']) . '</a> 系统自动发送，请勿直接回复。</p>
            <p style="margin: 5px 0 0 0;">© ' . date('Y') . ' ' . htmlspecialchars($options['site_name']) . ' All Rights Reserved.</p>
        </div>
    </div>
</body>
</html>';

    // 发送邮件
    $result = sendmail($options['admin_email'], $subject, $mailbody);

    return $result;
}

smarty_output($tplfile);
?>