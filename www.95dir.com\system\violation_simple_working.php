<?php
/*
 * 简化版违规网站管理后台
 */

// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require('common.php');
} catch (Exception $e) {
    die('加载 common.php 失败: ' . $e->getMessage());
}

// 检查基本环境
if (!defined('IN_ADMIN')) {
    die('权限验证失败，请确保已登录后台管理系统');
}

if (!isset($DB)) {
    die('数据库连接失败');
}

// 检查违规检查模块
$violation_check_file = ROOT_PATH.'system/violation_check.php';
$violation_functions_available = false;
if (file_exists($violation_check_file)) {
    try {
        require($violation_check_file);
        $violation_functions_available = function_exists('check_website_violations');
    } catch (Exception $e) {
        // 如果加载失败，继续使用基本功能
    }
}

// 检查数据库表
$tables_exist = true;
try {
    $check_table = $DB->query("SHOW TABLES LIKE '".TABLE_PREFIX."violation_config'");
    if ($DB->num_rows($check_table) == 0) {
        $tables_exist = false;
    }
} catch (Exception $e) {
    $tables_exist = false;
}

$action = isset($_GET['action']) ? $_GET['action'] : 'list';

// 处理POST请求
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($action == 'save_config' && isset($_POST['config'])) {
        handle_save_config();
    } elseif ($action == 'check' && isset($_POST['start_check'])) {
        handle_manual_check();
    }
}

// 处理GET操作
if (isset($_GET['web_id']) && $action == 'restore') {
    handle_restore();
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>违规网站管理</title>
    <link href="../themes/system/skin/global.css" rel="stylesheet" type="text/css" />
    <style>
        .container { padding: 20px; }
        .nav-tabs { margin: 10px 0; border-bottom: 1px solid #ddd; padding-bottom: 10px; }
        .nav-tabs a { margin-right: 15px; text-decoration: none; padding: 5px 10px; }
        .nav-tabs a.active { background: #007cba; color: white; }
        .message { padding: 10px; margin: 10px 0; border-radius: 3px; }
        .success { background: #dff0d8; color: #3c763d; }
        .error { background: #f2dede; color: #a94442; }
        .warning { background: #fcf8e3; color: #8a6d3b; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f5f5f5; }
        .btn { padding: 5px 10px; text-decoration: none; border-radius: 3px; }
        .btn-success { background: #5cb85c; color: white; }
        .btn-danger { background: #d9534f; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>违规网站管理</h1>
        
        <div class="nav-tabs">
            <a href="?action=list" <?php echo $action=='list'?'class="active"':''; ?>>违规列表</a>
            <a href="?action=config" <?php echo $action=='config'?'class="active"':''; ?>>配置管理</a>
            <a href="?action=logs" <?php echo $action=='logs'?'class="active"':''; ?>>检查日志</a>
            <a href="?action=check" <?php echo $action=='check'?'class="active"':''; ?>>手动检查</a>
            <a href="?action=upgrade" <?php echo $action=='upgrade'?'class="active"':''; ?>>系统升级</a>
        </div>
        
        <?php if (!$tables_exist): ?>
        <div class="message warning">
            <strong>注意：</strong>违规检查系统尚未安装。
            <a href="../install_violation_system.php">点击安装</a>
        </div>
        <?php endif; ?>
        
        <?php
        // 处理消息显示
        if (isset($_GET['message'])) {
            $msg_type = isset($_GET['type']) ? $_GET['type'] : 'success';
            echo "<div class='message $msg_type'>" . htmlspecialchars($_GET['message']) . "</div>";
        }
        
        // 处理操作
        try {
            switch ($action) {
                case 'list':
                    show_violation_list();
                    break;
                case 'config':
                    show_violation_config();
                    break;
                case 'logs':
                    show_violation_logs();
                    break;
                case 'check':
                    show_manual_check();
                    break;
                case 'upgrade':
                    show_system_upgrade();
                    break;
                case 'restore':
                    handle_restore();
                    break;
                case 'save_config':
                    handle_save_config();
                    break;
                case 'do_upgrade':
                    handle_system_upgrade();
                    break;
                default:
                    show_violation_list();
            }
        } catch (Exception $e) {
            echo "<div class='message error'>错误: " . $e->getMessage() . "</div>";
        }
        ?>
    </div>
</body>
</html>

<?php

// 显示违规网站列表
function show_violation_list() {
    global $DB, $tables_exist;
    
    echo "<h2>违规网站列表</h2>";
    
    if (!$tables_exist) {
        echo "<p>请先安装违规检查系统</p>";
        return;
    }
    
    try {
        // 获取分页参数
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $pagesize = 20;
        $start = ($page - 1) * $pagesize;

        // 获取统计信息
        $total_sql = "SELECT COUNT(*) as count FROM ".$DB->table('websites')." WHERE web_violation_status=1";
        $total_result = $DB->fetch_one($total_sql);
        $total_violations = $total_result ? $total_result['count'] : 0;

        // 获取今日新增违规
        $today_sql = "SELECT COUNT(*) as count FROM ".$DB->table('websites')."
                      WHERE web_violation_status=1 AND DATE(web_violation_time) = CURDATE()";
        $today_result = $DB->fetch_one($today_sql);
        $today_violations = $today_result ? $today_result['count'] : 0;

        echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>统计信息</h3>";
        echo "<div style='display: flex; gap: 30px;'>";
        echo "<div><strong>总违规网站数：</strong> <span style='color: #d9534f; font-size: 18px;'>$total_violations</span></div>";
        echo "<div><strong>今日新增：</strong> <span style='color: #f0ad4e; font-size: 18px;'>$today_violations</span></div>";
        echo "</div>";
        echo "</div>";

        if ($total_violations > 0) {
            // 获取违规网站列表
            $sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_intro, w.web_violation_reason,
                           w.web_violation_time, c.cate_name
                    FROM ".$DB->table('websites')." w
                    LEFT JOIN ".$DB->table('categories')." c ON w.cate_id=c.cate_id
                    WHERE w.web_violation_status=1
                    ORDER BY w.web_violation_time DESC
                    LIMIT $start, $pagesize";

            $violations = $DB->fetch_all($sql);

            if (!empty($violations)) {
                echo "<div style='margin: 20px 0;'>";
                echo "<strong>显示第 " . ($start + 1) . " - " . min($start + $pagesize, $total_violations) . " 条，共 $total_violations 条记录</strong>";
                echo "</div>";

                echo "<table>";
                echo "<tr><th width='60'>ID</th><th width='200'>网站名称</th><th width='120'>分类</th><th width='250'>违规原因</th><th width='130'>违规时间</th><th width='100'>操作</th></tr>";

                foreach ($violations as $violation) {
                    echo "<tr>";
                    echo "<td>" . intval($violation['web_id']) . "</td>";
                    echo "<td>";
                    echo "<strong>" . htmlspecialchars($violation['web_name']) . "</strong>";
                    if ($violation['web_intro']) {
                        echo "<br><small style='color: #666;'>" . htmlspecialchars(mb_substr($violation['web_intro'], 0, 50)) . "...</small>";
                    }
                    echo "</td>";
                    echo "<td>" . htmlspecialchars($violation['cate_name']) . "</td>";
                    echo "<td style='color: #d9534f;'>" . htmlspecialchars($violation['web_violation_reason']) . "</td>";
                    echo "<td>" . date('m-d H:i', strtotime($violation['web_violation_time'])) . "</td>";
                    echo "<td>";
                    echo '<a href="?action=restore&web_id=' . intval($violation['web_id']) . '" class="btn btn-success" onclick="return confirm(\'确认恢复此网站吗？\')">恢复</a>';
                    echo "</td>";
                    echo "</tr>";
                }

                echo "</table>";

                // 分页导航
                $total_pages = ceil($total_violations / $pagesize);
                if ($total_pages > 1) {
                    echo "<div style='text-align: center; margin: 20px 0;'>";

                    if ($page > 1) {
                        echo "<a href='?action=list&page=" . ($page - 1) . "' class='btn'>上一页</a> ";
                    }

                    for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++) {
                        if ($i == $page) {
                            echo "<span style='padding: 8px 12px; background: #337ab7; color: white; margin: 0 2px;'>$i</span> ";
                        } else {
                            echo "<a href='?action=list&page=$i' style='padding: 8px 12px; margin: 0 2px; text-decoration: none; border: 1px solid #ddd;'>$i</a> ";
                        }
                    }

                    if ($page < $total_pages) {
                        echo "<a href='?action=list&page=" . ($page + 1) . "' class='btn'>下一页</a>";
                    }

                    echo "</div>";
                }
            } else {
                echo "<div class='message info'>查询结果为空。</div>";
            }
        } else {
            echo "<div class='message success'>🎉 当前没有违规网站！</div>";
        }

        // 操作说明
        echo "<div class='message info'>";
        echo "<h4>说明：</h4>";
        echo "<ul>";
        echo "<li>违规网站已自动隐藏，前台不会显示URL和跳转链接</li>";
        echo "<li>如果发现误判，可以点击\"恢复\"按钮恢复网站正常状态</li>";
        echo "<li>系统会定期自动检查网站内容，发现违规自动标记</li>";
        echo "</ul>";
        echo "</div>";

    } catch (Exception $e) {
        echo "<div class='message error'>查询失败: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
}

// 显示配置管理
function show_violation_config() {
    global $DB, $tables_exist;
    
    echo "<h2>配置管理</h2>";
    
    if (!$tables_exist) {
        echo "<p>请先安装违规检查系统</p>";
        return;
    }
    
    try {
        // 获取配置
        $configs = array();
        $sql = "SELECT * FROM ".$DB->table('violation_config');
        $results = $DB->fetch_all($sql);
        
        foreach ($results as $row) {
            $configs[$row['config_key']] = $row['config_value'];
        }
        
        echo "<form method='post' action='?action=save_config'>";
        echo "<table>";
        echo "<tr><th>配置项</th><th>配置值</th><th>说明</th></tr>";
        
        $config_items = array(
            'violation_keywords' => array('name' => '违规关键词', 'type' => 'textarea', 'desc' => '用逗号分隔，支持中英文'),
            'check_interval' => array('name' => '检查间隔(小时)', 'type' => 'number', 'desc' => '自动检查间隔，建议24小时'),
            'check_batch_size' => array('name' => '每次检查数量', 'type' => 'number', 'desc' => '每次检查的网站数，建议50个'),
            'auto_check_enabled' => array('name' => '自动检查', 'type' => 'select', 'desc' => '是否启用自动检查'),
            'live_content_check' => array('name' => '实时内容检测', 'type' => 'select', 'desc' => '是否检查网站实际内容'),
            'check_title' => array('name' => '检查网站标题', 'type' => 'select', 'desc' => '是否检查网站实际标题'),
            'check_description' => array('name' => '检查网站描述', 'type' => 'select', 'desc' => '是否检查meta描述'),
            'check_keywords' => array('name' => '检查网站关键词', 'type' => 'select', 'desc' => '是否检查meta关键词'),
            'check_content' => array('name' => '检查页面内容', 'type' => 'select', 'desc' => '是否检查页面文本内容'),
            'content_check_length' => array('name' => '内容检查长度', 'type' => 'number', 'desc' => '检查页面内容的字符数，建议2000')
        );
        
        foreach ($config_items as $key => $item) {
            // 设置默认值
            $default_values = array(
                'violation_keywords' => '色情,赌博,毒品,诈骗,枪支,炸药',
                'check_interval' => '24',
                'check_batch_size' => '50',
                'auto_check_enabled' => '1',
                'live_content_check' => '1',
                'check_title' => '1',
                'check_description' => '1',
                'check_keywords' => '1',
                'check_content' => '1',
                'content_check_length' => '2000'
            );

            $value = isset($configs[$key]) ? $configs[$key] : $default_values[$key];
            echo "<tr>";
            echo "<td><strong>" . $item['name'] . "</strong></td>";
            echo "<td>";

            if ($item['type'] == 'textarea') {
                echo "<textarea name='config[$key]' rows='5' cols='60' style='width: 100%;'>" . htmlspecialchars($value) . "</textarea>";
            } elseif ($item['type'] == 'select') {
                echo "<select name='config[$key]' style='width: 100px;'>";
                echo "<option value='1'" . ($value=='1'?' selected':'') . ">启用</option>";
                echo "<option value='0'" . ($value=='0'?' selected':'') . ">禁用</option>";
                echo "</select>";
            } elseif ($item['type'] == 'number') {
                echo "<input type='number' name='config[$key]' value='" . htmlspecialchars($value) . "' style='width: 100px;' min='1'>";
            } else {
                echo "<input type='" . $item['type'] . "' name='config[$key]' value='" . htmlspecialchars($value) . "' style='width: 200px;'>";
            }

            echo "</td>";
            echo "<td><small>" . $item['desc'] . "</small></td>";
            echo "</tr>";
        }
        
        echo "</table>";
        echo "<button type='submit' class='btn btn-success'>保存配置</button>";
        echo "</form>";
        
    } catch (Exception $e) {
        echo "<div class='message error'>加载配置失败: " . $e->getMessage() . "</div>";
    }
}

// 显示检查日志
function show_violation_logs() {
    global $DB, $tables_exist;
    
    echo "<h2>检查日志</h2>";
    
    if (!$tables_exist) {
        echo "<p>请先安装违规检查系统</p>";
        return;
    }
    
    try {
        $sql = "SELECT l.*, w.web_name 
                FROM ".$DB->table('violation_logs')." l 
                LEFT JOIN ".$DB->table('websites')." w ON l.web_id=w.web_id 
                ORDER BY l.check_time DESC 
                LIMIT 50";
        
        $logs = $DB->fetch_all($sql);
        
        if (empty($logs)) {
            echo "<p>暂无检查日志</p>";
        } else {
            echo "<table>";
            echo "<tr><th>网站ID</th><th>网站名称</th><th>违规原因</th><th>检查时间</th></tr>";
            
            foreach ($logs as $log) {
                echo "<tr>";
                echo "<td>" . $log['web_id'] . "</td>";
                echo "<td>" . htmlspecialchars($log['web_name']) . "</td>";
                echo "<td style='color: red;'>" . htmlspecialchars($log['violation_reason']) . "</td>";
                echo "<td>" . $log['check_time'] . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        }
        
    } catch (Exception $e) {
        echo "<div class='message error'>查询日志失败: " . $e->getMessage() . "</div>";
    }
}

// 显示手动检查
function show_manual_check() {
    global $DB, $tables_exist, $violation_functions_available;

    echo "<h2>手动检查</h2>";

    if (!$tables_exist) {
        echo "<div class='message warning'>请先安装违规检查系统才能使用手动检查功能。</div>";
        return;
    }

    // 检查是否有检查结果
    $check_results = null;
    if (isset($_SESSION['check_results'])) {
        $check_results = $_SESSION['check_results'];
        unset($_SESSION['check_results']);
    }

    // 显示检查表单
    echo "<div style='background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>执行违规检查</h3>";
    echo "<p>点击下面的按钮开始检查网站违规情况。系统会检查最近未检查的网站。</p>";

    if ($violation_functions_available) {
        echo "<form method='post' action='?action=check' id='checkForm'>";
        echo "<div style='margin: 20px 0;'>";
        echo "<label>检查数量：</label>";
        echo "<select name='check_count' style='padding: 5px; margin: 0 10px;'>";
        echo "<option value='10'>10个网站</option>";
        echo "<option value='20' selected>20个网站</option>";
        echo "<option value='50'>50个网站</option>";
        echo "</select>";
        echo "</div>";

        echo "<div style='margin: 20px 0;'>";
        echo "<button type='submit' name='start_check' class='btn btn-primary' id='checkBtn' style='padding: 12px 30px; font-size: 16px;'>";
        echo "<span id='btnText'>开始检查</span>";
        echo "<span id='btnLoading' style='display: none;'>检查中...</span>";
        echo "</button>";
        echo "</div>";
        echo "</form>";
    } else {
        echo "<div class='message warning'>";
        echo "<p>违规检查功能暂不可用，但您可以：</p>";
        echo "<ul>";
        echo "<li>查看现有的违规网站列表</li>";
        echo "<li>配置违规关键词</li>";
        echo "<li>查看检查日志</li>";
        echo "<li>手动恢复误判的网站</li>";
        echo "</ul>";
        echo "</div>";
    }
    echo "</div>";

    // 显示检查结果
    if ($check_results) {
        echo "<div style='background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>检查结果</h3>";

        echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0;'>";

        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; border: 1px solid #dee2e6;'>";
        echo "<div style='font-size: 24px; font-weight: bold; color: #337ab7;'>" . $check_results['total_checked'] . "</div>";
        echo "<div style='font-size: 12px; color: #666;'>总检查数</div>";
        echo "</div>";

        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; text-align: center; border: 1px solid #f5c6cb;'>";
        echo "<div style='font-size: 24px; font-weight: bold; color: #d9534f;'>" . $check_results['violations_found'] . "</div>";
        echo "<div style='font-size: 12px; color: #666;'>发现违规</div>";
        echo "</div>";

        echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; text-align: center; border: 1px solid #bee5eb;'>";
        echo "<div style='font-size: 24px; font-weight: bold; color: #0c5460;'>" . ($check_results['total_checked'] - $check_results['violations_found'] - $check_results['errors']) . "</div>";
        echo "<div style='font-size: 12px; color: #666;'>检查正常</div>";
        echo "</div>";

        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; text-align: center; border: 1px solid #ffeaa7;'>";
        echo "<div style='font-size: 24px; font-weight: bold; color: #856404;'>" . $check_results['errors'] . "</div>";
        echo "<div style='font-size: 12px; color: #666;'>检查错误</div>";
        echo "</div>";

        // 显示检测类型统计
        if (isset($check_results['detection_stats'])) {
            echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; text-align: center; border: 1px solid #d6d8db;'>";
            echo "<div style='font-size: 16px; font-weight: bold; color: #383d41;'>检测详情</div>";
            echo "<div style='font-size: 11px; color: #666; margin-top: 5px;'>";
            if (isset($check_results['detection_stats']['live_content'])) {
                echo "实时检测: " . $check_results['detection_stats']['live_content'] . "<br>";
            }
            if (isset($check_results['detection_stats']['local_data'])) {
                echo "本地数据: " . $check_results['detection_stats']['local_data'];
            }
            echo "</div>";
            echo "</div>";
        }

        echo "</div>";

        if ($check_results['violations_found'] > 0 && isset($check_results['details'])) {
            echo "<h4>违规详情</h4>";
            echo "<div style='overflow-x: auto;'>";
            echo "<table style='width: 100%; border-collapse: collapse;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 12px; border: 1px solid #ddd;'>网站ID</th>";
            echo "<th style='padding: 12px; border: 1px solid #ddd;'>网站名称</th>";
            echo "<th style='padding: 12px; border: 1px solid #ddd;'>网站URL</th>";
            echo "<th style='padding: 12px; border: 1px solid #ddd;'>违规原因</th>";
            echo "<th style='padding: 12px; border: 1px solid #ddd;'>检测类型</th>";
            echo "</tr>";

            foreach ($check_results['details'] as $detail) {
                echo "<tr>";
                echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . intval($detail['web_id']) . "</td>";
                echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($detail['web_name']) . "</td>";
                echo "<td style='padding: 8px; border: 1px solid #ddd; font-size: 12px;'>";
                if (isset($detail['web_url']) && $detail['web_url']) {
                    echo "<a href='" . htmlspecialchars($detail['web_url']) . "' target='_blank' style='color: #666;'>" . htmlspecialchars(mb_substr($detail['web_url'], 0, 30)) . "...</a>";
                } else {
                    echo "无URL";
                }
                echo "</td>";
                echo "<td style='padding: 8px; border: 1px solid #ddd; color: #d9534f;'>" . htmlspecialchars($detail['reason']) . "</td>";
                echo "<td style='padding: 8px; border: 1px solid #ddd; font-size: 11px;'>";
                if (strpos($detail['reason'], '实时检测') !== false) {
                    echo "<span style='background: #ffeaa7; padding: 2px 6px; border-radius: 3px;'>实时检测</span>";
                } else {
                    echo "<span style='background: #ddd; padding: 2px 6px; border-radius: 3px;'>本地数据</span>";
                }
                echo "</td>";
                echo "</tr>";
            }

            echo "</table>";
            echo "</div>";

            // 添加处理建议
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h5>处理建议：</h5>";
            echo "<ul style='margin: 5px 0; padding-left: 20px;'>";
            echo "<li>违规网站已自动隐藏，前台不会显示</li>";
            echo "<li>如发现误判，可在违规列表中点击\"恢复\"</li>";
            echo "<li>建议联系网站管理员整改内容</li>";
            echo "<li>可调整违规关键词配置以优化检测精度</li>";
            echo "</ul>";
            echo "</div>";

        } elseif ($check_results['violations_found'] == 0) {
            echo "<div style='background: #dff0d8; padding: 20px; border-radius: 5px; text-align: center; color: #3c763d; border: 1px solid #d6e9c6;'>";
            echo "<h4 style='margin: 0 0 10px 0;'>✓ 本次检查未发现违规网站</h4>";
            echo "<p style='margin: 0; font-size: 14px;'>所有检查的网站内容均符合规范</p>";
            echo "</div>";
        }

        echo "</div>";
    }

    // 系统状态信息
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>系统状态</h3>";

    try {
        // 获取待检查网站数
        $pending_sql = "SELECT COUNT(*) as count FROM ".$DB->table('websites')."
                        WHERE web_status=3 AND (web_check_time IS NULL OR web_check_time < DATE_SUB(NOW(), INTERVAL 24 HOUR))";
        $pending_result = $DB->fetch_one($pending_sql);
        $pending_count = $pending_result ? $pending_result['count'] : 0;

        // 获取总网站数
        $total_sql = "SELECT COUNT(*) as count FROM ".$DB->table('websites')." WHERE web_status=3";
        $total_result = $DB->fetch_one($total_sql);
        $total_count = $total_result ? $total_result['count'] : 0;

        // 获取违规网站数
        $violation_sql = "SELECT COUNT(*) as count FROM ".$DB->table('websites')." WHERE web_violation_status=1";
        $violation_result = $DB->fetch_one($violation_sql);
        $violation_count = $violation_result ? $violation_result['count'] : 0;

        echo "<div style='display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;'>";

        echo "<div>";
        echo "<strong>总网站数：</strong> $total_count<br>";
        echo "<strong>待检查数：</strong> $pending_count<br>";
        echo "<strong>违规网站数：</strong> $violation_count";
        echo "</div>";

        echo "<div>";
        echo "<strong>检查功能：</strong> " . ($violation_functions_available ? '可用' : '基础版') . "<br>";
        echo "<strong>数据库表：</strong> " . ($tables_exist ? '正常' : '缺失') . "<br>";
        echo "<strong>系统状态：</strong> " . ($tables_exist ? '正常' : '需要安装');
        echo "</div>";

        echo "<div>";
        echo "<strong>最后检查：</strong> ";
        if ($tables_exist) {
            $last_check_sql = "SELECT MAX(check_time) as last_time FROM ".$DB->table('violation_logs');
            $last_check_result = $DB->fetch_one($last_check_sql);
            if ($last_check_result && $last_check_result['last_time']) {
                echo date('m-d H:i', strtotime($last_check_result['last_time']));
            } else {
                echo '从未检查';
            }
        } else {
            echo '系统未安装';
        }
        echo "</div>";

        echo "</div>";

    } catch (Exception $e) {
        echo "<div class='message error'>获取系统状态失败: " . htmlspecialchars($e->getMessage()) . "</div>";
    }

    echo "</div>";

    // 使用说明
    echo "<div class='message info'>";
    echo "<h4>使用说明：</h4>";
    echo "<ul>";
    echo "<li>手动检查会检查最近未检查的网站（优先检查24小时内未检查的）</li>";
    echo "<li>检查内容包括：网站标题、描述、页面内容和可访问性</li>";
    echo "<li>发现违规的网站会自动隐藏，不在前台显示</li>";
    echo "<li>如果检查功能不可用，请确保已正确安装违规检查系统</li>";
    echo "</ul>";
    echo "</div>";

    // JavaScript
    echo "<script>";
    echo "if (document.getElementById('checkForm')) {";
    echo "    document.getElementById('checkForm').addEventListener('submit', function() {";
    echo "        document.getElementById('checkBtn').disabled = true;";
    echo "        document.getElementById('btnText').style.display = 'none';";
    echo "        document.getElementById('btnLoading').style.display = 'inline';";
    echo "    });";
    echo "}";
    echo "</script>";
}

// 显示系统升级
function show_system_upgrade() {
    global $DB, $tables_exist;

    echo "<h2>系统升级</h2>";

    if (!$tables_exist) {
        echo "<div class='message error'>请先安装基础违规检测系统才能进行升级。</div>";
        echo "<p><a href='../install_violation_system.php'>点击安装基础系统</a></p>";
        return;
    }

    // 检查是否需要升级
    $need_upgrade = false;
    $missing_configs = array();

    $required_configs = array(
        'live_content_check' => '实时内容检测',
        'check_title' => '检查网站标题',
        'check_description' => '检查网站描述',
        'check_keywords' => '检查网站关键词',
        'check_content' => '检查页面内容',
        'content_check_length' => '内容检查长度'
    );

    try {
        foreach ($required_configs as $key => $name) {
            $check_sql = "SELECT config_key FROM ".$DB->table('violation_config')." WHERE config_key='$key'";
            $result = $DB->fetch_one($check_sql);
            if (!$result) {
                $need_upgrade = true;
                $missing_configs[$key] = $name;
            }
        }

        if (!$need_upgrade) {
            echo "<div class='message success'>";
            echo "<h3>✓ 系统已是最新版本</h3>";
            echo "<p>所有增强功能配置项都已存在，无需升级。</p>";
            echo "</div>";

            echo "<div class='message info'>";
            echo "<h4>当前功能：</h4>";
            echo "<ul>";
            echo "<li>✅ 实时网站内容抓取</li>";
            echo "<li>✅ 多层次违规检测</li>";
            echo "<li>✅ 智能内容分析</li>";
            echo "<li>✅ 灵活配置系统</li>";
            echo "<li>✅ 详细检测报告</li>";
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div class='message warning'>";
            echo "<h3>发现可升级项目</h3>";
            echo "<p>检测到以下配置项缺失，升级后将获得增强的违规检测功能：</p>";
            echo "<ul>";
            foreach ($missing_configs as $key => $name) {
                echo "<li><strong>$name</strong> ($key)</li>";
            }
            echo "</ul>";
            echo "</div>";

            echo "<div class='message info'>";
            echo "<h4>升级后新增功能：</h4>";
            echo "<ul>";
            echo "<li><strong>实时网站内容抓取</strong>：模拟浏览器访问收录的网站</li>";
            echo "<li><strong>多层次违规检测</strong>：检查网站标题、描述、关键词、页面内容</li>";
            echo "<li><strong>智能内容分析</strong>：提取关键内容区域进行精准检测</li>";
            echo "<li><strong>灵活配置系统</strong>：可以单独控制各项检测功能</li>";
            echo "<li><strong>详细检测报告</strong>：提供完整的统计信息和处理建议</li>";
            echo "</ul>";
            echo "</div>";

            echo "<form method='post' action='?action=do_upgrade'>";
            echo "<div style='text-align: center; margin: 30px 0;'>";
            echo "<button type='submit' class='btn btn-primary' style='padding: 15px 30px; font-size: 16px;'>开始升级</button>";
            echo "</div>";
            echo "</form>";
        }

    } catch (Exception $e) {
        echo "<div class='message error'>检查升级状态失败: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
}

// 处理系统升级
function handle_system_upgrade() {
    global $DB;

    try {
        // 新增配置项
        $new_configs = array(
            'live_content_check' => array(
                'value' => '1',
                'desc' => '是否启用实时内容检测功能'
            ),
            'check_title' => array(
                'value' => '1',
                'desc' => '是否检查网站实际标题'
            ),
            'check_description' => array(
                'value' => '1',
                'desc' => '是否检查网站Meta描述'
            ),
            'check_keywords' => array(
                'value' => '1',
                'desc' => '是否检查网站Meta关键词'
            ),
            'check_content' => array(
                'value' => '1',
                'desc' => '是否检查网站页面内容'
            ),
            'content_check_length' => array(
                'value' => '2000',
                'desc' => '检查页面内容的最大字符数'
            )
        );

        $added_count = 0;
        $error_count = 0;

        foreach ($new_configs as $key => $config) {
            // 检查配置项是否已存在
            $check_sql = "SELECT config_key FROM ".$DB->table('violation_config')." WHERE config_key='$key'";
            $check_result = $DB->fetch_one($check_sql);

            if (!$check_result) {
                // 添加新配置项
                $insert_data = array(
                    'config_key' => $key,
                    'config_value' => $config['value'],
                    'config_desc' => $config['desc'],
                    'update_time' => date('Y-m-d H:i:s')
                );

                $result = $DB->insert($DB->table('violation_config'), $insert_data);
                if ($result) {
                    $added_count++;
                } else {
                    $error_count++;
                }
            }
        }

        // 检查是否需要添加默认违规关键词
        $keywords_sql = "SELECT config_value FROM ".$DB->table('violation_config')." WHERE config_key='violation_keywords'";
        $keywords_result = $DB->fetch_one($keywords_sql);

        if (!$keywords_result || empty($keywords_result['config_value'])) {
            $default_keywords = '色情,赌博,毒品,诈骗,枪支,炸药,假证,洗钱,传销,高利贷,病毒,木马,钓鱼,欺诈';
            $update_data = array(
                'config_value' => $default_keywords,
                'update_time' => date('Y-m-d H:i:s')
            );
            $where = array('config_key' => 'violation_keywords');
            $DB->update($DB->table('violation_config'), $update_data, $where);
        }

        if ($error_count > 0) {
            $message = "升级部分完成，成功添加 $added_count 个配置项，$error_count 个配置项添加失败";
            header("Location: ?action=upgrade&message=" . urlencode($message) . "&type=warning");
        } else {
            $message = "升级成功！已添加 $added_count 个新配置项，系统功能已增强";
            header("Location: ?action=upgrade&message=" . urlencode($message) . "&type=success");
        }

    } catch (Exception $e) {
        header("Location: ?action=upgrade&message=" . urlencode('升级失败: ' . $e->getMessage()) . "&type=error");
    }
    exit;
}

// 处理恢复操作
function handle_restore() {
    global $DB;
    
    $web_id = isset($_GET['web_id']) ? intval($_GET['web_id']) : 0;
    
    if ($web_id > 0) {
        try {
            $data = array(
                'web_violation_status' => 0,
                'web_violation_reason' => '',
                'web_violation_time' => null
            );
            
            $where = array('web_id' => $web_id);
            $DB->update($DB->table('websites'), $data, $where);
            
            header("Location: ?action=list&message=" . urlencode('网站已恢复正常状态') . "&type=success");
        } catch (Exception $e) {
            header("Location: ?action=list&message=" . urlencode('恢复失败: ' . $e->getMessage()) . "&type=error");
        }
    } else {
        header("Location: ?action=list&message=" . urlencode('参数错误') . "&type=error");
    }
    exit;
}

// 处理保存配置
function handle_save_config() {
    global $DB;

    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['config'])) {
        try {
            foreach ($_POST['config'] as $key => $value) {
                $data = array(
                    'config_value' => $value,
                    'update_time' => date('Y-m-d H:i:s')
                );

                $where = array('config_key' => $key);
                $DB->update($DB->table('violation_config'), $data, $where);
            }

            header("Location: ?action=config&message=" . urlencode('配置保存成功') . "&type=success");
        } catch (Exception $e) {
            header("Location: ?action=config&message=" . urlencode('保存失败: ' . $e->getMessage()) . "&type=error");
        }
    } else {
        header("Location: ?action=config&message=" . urlencode('请求方法错误') . "&type=error");
    }
    exit;
}

// 处理手动检查
function handle_manual_check() {
    global $DB, $violation_functions_available;

    if (!$violation_functions_available) {
        header("Location: ?action=check&message=" . urlencode('违规检查功能不可用') . "&type=error");
        exit;
    }

    $check_count = isset($_POST['check_count']) ? intval($_POST['check_count']) : 20;
    $check_count = max(1, min(100, $check_count)); // 限制在1-100之间

    try {
        // 执行简化的检查
        $results = check_website_violations_simple($check_count);

        // 将结果存储到session中
        session_start();
        $_SESSION['check_results'] = $results;

        $message = "检查完成！检查了 {$results['total_checked']} 个网站，发现 {$results['violations_found']} 个违规网站。";
        header("Location: ?action=check&message=" . urlencode($message) . "&type=success");

    } catch (Exception $e) {
        header("Location: ?action=check&message=" . urlencode('检查失败: ' . $e->getMessage()) . "&type=error");
    }
    exit;
}

// 增强的违规检查函数
function check_website_violations_simple($limit = 20) {
    global $DB;

    $results = array(
        'total_checked' => 0,
        'violations_found' => 0,
        'errors' => 0,
        'details' => array(),
        'detection_stats' => array(
            'live_content' => 0,
            'local_data' => 0
        )
    );

    try {
        // 获取违规关键词
        $config_sql = "SELECT config_value FROM ".$DB->table('violation_config')." WHERE config_key='violation_keywords'";
        $config_result = $DB->fetch_one($config_sql);
        $keywords_str = $config_result ? $config_result['config_value'] : '';

        if (empty($keywords_str)) {
            throw new Exception('未配置违规关键词，请先在配置管理中设置');
        }

        $keywords = array_map('trim', explode(',', $keywords_str));
        $keywords = array_filter($keywords); // 移除空值

        if (empty($keywords)) {
            throw new Exception('违规关键词配置无效');
        }

        // 获取待检查的网站
        $sql = "SELECT web_id, web_name, web_url, web_intro
                FROM ".$DB->table('websites')."
                WHERE web_status=3 AND (web_violation_status IS NULL OR web_violation_status=0)
                AND (web_check_time IS NULL OR web_check_time < DATE_SUB(NOW(), INTERVAL 1 HOUR))
                ORDER BY COALESCE(web_check_time, '1970-01-01') ASC
                LIMIT $limit";

        $websites = $DB->fetch_all($sql);

        foreach ($websites as $website) {
            $results['total_checked']++;

            try {
                // 更新检查时间
                $update_data = array('web_check_time' => date('Y-m-d H:i:s'));
                $update_where = array('web_id' => $website['web_id']);
                $DB->update($DB->table('websites'), $update_data, $update_where);

                // 检查网站内容
                $violation_reason = check_website_content_simple($website, $keywords);

                if ($violation_reason) {
                    // 标记为违规
                    $violation_data = array(
                        'web_violation_status' => 1,
                        'web_violation_reason' => $violation_reason,
                        'web_violation_time' => date('Y-m-d H:i:s')
                    );

                    $DB->update($DB->table('websites'), $violation_data, $update_where);

                    // 记录日志
                    $log_data = array(
                        'web_id' => $website['web_id'],
                        'violation_reason' => $violation_reason,
                        'check_time' => date('Y-m-d H:i:s'),
                        'check_ip' => $_SERVER['REMOTE_ADDR']
                    );

                    $DB->insert($DB->table('violation_logs'), $log_data);

                    $results['violations_found']++;

                    // 统计检测类型
                    if (strpos($violation_reason, '实时检测') !== false) {
                        $results['detection_stats']['live_content']++;
                    } else {
                        $results['detection_stats']['local_data']++;
                    }

                    $results['details'][] = array(
                        'web_id' => $website['web_id'],
                        'web_name' => $website['web_name'],
                        'web_url' => $website['web_url'],
                        'reason' => $violation_reason
                    );
                }

            } catch (Exception $e) {
                $results['errors']++;
                // 继续检查下一个网站
            }
        }

    } catch (Exception $e) {
        throw $e;
    }

    return $results;
}

// 增强的内容检查函数
function check_website_content_simple($website, $keywords) {
    // 1. 检查网站名称
    foreach ($keywords as $keyword) {
        if (stripos($website['web_name'], $keyword) !== false) {
            return "网站名称包含违规关键词: $keyword";
        }
    }

    // 2. 检查网站描述
    if ($website['web_intro']) {
        foreach ($keywords as $keyword) {
            if (stripos($website['web_intro'], $keyword) !== false) {
                return "网站描述包含违规关键词: $keyword";
            }
        }
    }

    // 3. 检查网站实际内容（新增功能）
    if ($website['web_url']) {
        $content_check = check_website_live_content($website['web_url'], $keywords);
        if ($content_check) {
            return $content_check;
        }
    }

    return false; // 未发现违规
}

// 检查网站实际内容（核心新功能）
function check_website_live_content($url, $keywords) {
    global $DB;

    try {
        // 获取检测配置
        $config_sql = "SELECT config_key, config_value FROM ".$DB->table('violation_config')."
                       WHERE config_key IN ('live_content_check', 'check_title', 'check_description', 'check_keywords', 'check_content', 'content_check_length')";
        $config_results = $DB->fetch_all($config_sql);

        $configs = array();
        foreach ($config_results as $row) {
            $configs[$row['config_key']] = $row['config_value'];
        }

        // 检查是否启用实时内容检测
        if (isset($configs['live_content_check']) && $configs['live_content_check'] != '1') {
            return false; // 未启用实时检测
        }

        // 获取网站内容
        $website_data = fetch_website_content($url);

        if (!$website_data) {
            return false; // 无法访问不算违规
        }

        // 检查网站标题
        if ((!isset($configs['check_title']) || $configs['check_title'] == '1') && !empty($website_data['title'])) {
            foreach ($keywords as $keyword) {
                if (stripos($website_data['title'], $keyword) !== false) {
                    return "网站标题包含违规关键词: $keyword (实时检测)";
                }
            }
        }

        // 检查meta描述
        if ((!isset($configs['check_description']) || $configs['check_description'] == '1') && !empty($website_data['description'])) {
            foreach ($keywords as $keyword) {
                if (stripos($website_data['description'], $keyword) !== false) {
                    return "网站描述包含违规关键词: $keyword (实时检测)";
                }
            }
        }

        // 检查meta关键词
        if ((!isset($configs['check_keywords']) || $configs['check_keywords'] == '1') && !empty($website_data['keywords'])) {
            foreach ($keywords as $keyword) {
                if (stripos($website_data['keywords'], $keyword) !== false) {
                    return "网站关键词包含违规词: $keyword (实时检测)";
                }
            }
        }

        // 检查页面文本内容
        if ((!isset($configs['check_content']) || $configs['check_content'] == '1') && !empty($website_data['content'])) {
            foreach ($keywords as $keyword) {
                if (stripos($website_data['content'], $keyword) !== false) {
                    return "网站内容包含违规关键词: $keyword (实时检测)";
                }
            }
        }

        return false; // 未发现违规

    } catch (Exception $e) {
        // 网络错误不算违规
        return false;
    }
}

// 获取网站内容（增强版）
function fetch_website_content($url) {
    if (!$url || !filter_var($url, FILTER_VALIDATE_URL)) {
        return false;
    }

    try {
        // 设置用户代理，模拟真实浏览器
        $user_agents = array(
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        );

        $user_agent = $user_agents[array_rand($user_agents)];

        // 创建上下文
        $context = stream_context_create(array(
            'http' => array(
                'timeout' => 15, // 增加超时时间
                'user_agent' => $user_agent,
                'follow_location' => true,
                'max_redirects' => 5,
                'ignore_errors' => true,
                'header' => array(
                    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language: zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
                    'Accept-Encoding: gzip, deflate',
                    'Connection: keep-alive',
                    'Upgrade-Insecure-Requests: 1'
                )
            ),
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false
            )
        ));

        // 获取网页内容
        $html = file_get_contents($url, false, $context);

        if ($html === false) {
            return false;
        }

        // 处理编码问题
        $html = mb_convert_encoding($html, 'UTF-8', 'auto');

        // 解析网页内容
        return parse_website_content($html);

    } catch (Exception $e) {
        return false;
    }
}

// 解析网站内容
function parse_website_content($html) {
    $data = array(
        'title' => '',
        'description' => '',
        'keywords' => '',
        'content' => ''
    );

    // 提取标题
    if (preg_match('/<title[^>]*>(.*?)<\/title>/is', $html, $matches)) {
        $data['title'] = trim(strip_tags($matches[1]));
    }

    // 提取meta描述
    if (preg_match('/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $html, $matches)) {
        $data['description'] = trim($matches[1]);
    }

    // 提取meta关键词
    if (preg_match('/<meta[^>]*name=["\']keywords["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $html, $matches)) {
        $data['keywords'] = trim($matches[1]);
    }

    // 移除脚本和样式
    $html = preg_replace('/<script[^>]*>.*?<\/script>/is', '', $html);
    $html = preg_replace('/<style[^>]*>.*?<\/style>/is', '', $html);
    $html = preg_replace('/<noscript[^>]*>.*?<\/noscript>/is', '', $html);

    // 提取主要内容区域
    $content_areas = array();

    // 尝试提取常见的内容区域
    $content_selectors = array(
        '/<body[^>]*>(.*?)<\/body>/is',
        '/<main[^>]*>(.*?)<\/main>/is',
        '/<article[^>]*>(.*?)<\/article>/is',
        '/<div[^>]*class=["\'][^"\']*content[^"\']*["\'][^>]*>(.*?)<\/div>/is',
        '/<div[^>]*id=["\'][^"\']*content[^"\']*["\'][^>]*>(.*?)<\/div>/is'
    );

    foreach ($content_selectors as $pattern) {
        if (preg_match($pattern, $html, $matches)) {
            $content_areas[] = $matches[1];
        }
    }

    // 如果没有找到特定内容区域，使用整个body
    if (empty($content_areas)) {
        if (preg_match('/<body[^>]*>(.*?)<\/body>/is', $html, $matches)) {
            $content_areas[] = $matches[1];
        } else {
            $content_areas[] = $html;
        }
    }

    // 处理内容
    $all_content = implode(' ', $content_areas);

    // 移除HTML标签
    $text_content = strip_tags($all_content);

    // 清理空白字符
    $text_content = preg_replace('/\s+/', ' ', $text_content);
    $text_content = trim($text_content);

    // 限制内容长度，避免内存问题
    $data['content'] = mb_substr($text_content, 0, 2000);

    return $data;
}
?>
