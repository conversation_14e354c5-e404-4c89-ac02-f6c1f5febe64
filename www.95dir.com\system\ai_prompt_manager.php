<?php
// ai_prompt_manager.php - AI提示词管理脚本
header('Content-Type: text/html; charset=utf-8');

// 引入系统配置
define('IN_ADMIN', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');
require(APP_PATH.'init.php');
require(APP_PATH.'module/static.php');

// 处理应用提示词的请求
if (isset($_POST['apply_prompt']) && isset($_POST['prompt_key'])) {
    $prompt_key = $_POST['prompt_key'];
    $prompts = getPromptTemplates();
    
    if (isset($prompts[$prompt_key])) {
        $selected_prompt = $prompts[$prompt_key]['content'];
        
        // 更新数据库中的提示词配置
        $table = $DB->table('options');
        $existing = $DB->fetch_one("SELECT option_name FROM $table WHERE option_name = 'ai_prompt_template'");
        
        if ($existing) {
            // 更新现有配置
            $udata = array('option_value' => $selected_prompt);
            $uwhere = array('option_name' => 'ai_prompt_template');
            
            if ($DB->update($table, $udata, $uwhere)) {
                // 更新配置缓存
                update_cache('options');
                $success_message = "✅ 成功应用提示词模板：" . $prompts[$prompt_key]['name'];
            } else {
                $error_message = "❌ 应用提示词失败，请重试";
            }
        } else {
            // 插入新配置
            $idata = array(
                'option_name' => 'ai_prompt_template',
                'option_value' => $selected_prompt
            );
            
            if ($DB->insert($table, $idata)) {
                update_cache('options');
                $success_message = "✅ 成功应用提示词模板：" . $prompts[$prompt_key]['name'];
            } else {
                $error_message = "❌ 应用提示词失败，请重试";
            }
        }
    } else {
        $error_message = "❌ 无效的提示词模板";
    }
}

// 获取当前配置的提示词
$current_prompt = isset($options['ai_prompt_template']) ? $options['ai_prompt_template'] : '';

// 提示词模板数据
function getPromptTemplates() {
    return array(
        'general' => array(
            'name' => '通用网站模板',
            'description' => '适用于各类网站的通用模板，平衡详细度和适用性',
            'content' => "你是一个专业的网站内容撰写助手，请根据以下信息为网站生成一段简洁、有吸引力的AI简介（输出600字左右的HTML内容）：\n" .
                        "使用<p><ul><li><strong>等基础标签\n" .
                        "包含SEO关键词但保持自然\n" .
                        "分3-5个段落，每个段落有明确主题\n" .
                        "符合中文阅读习惯\n" .
                        "突出网站特色，语言流畅自然\n" .
                        "只要一个文本网址不要链接"
        ),
        
        'ecommerce' => array(
            'name' => '电商购物网站',
            'description' => '专为电商、购物、零售类网站设计的详细模板',
            'content' => '你是一位电商行业的资深内容策划师，请为电商网站撰写一篇专业详细的简介：

🛒 电商网站专业简介要求：
- 字数：1000-1500字，突出商业价值
- 重点：商品品质、购物体验、服务保障
- 风格：商业化、可信赖、用户导向

🎯 核心内容结构：
1. <h3>平台概述</h3> - 品牌定位、经营理念、市场地位
2. <h3>商品优势</h3> - 产品种类、品质保证、货源优势
3. <h3>购物体验</h3> - 界面设计、搜索功能、下单流程
4. <h3>物流配送</h3> - 配送范围、速度保证、包装服务
5. <h3>售后服务</h3> - 退换政策、客服支持、质量保障
6. <h3>安全保障</h3> - 支付安全、隐私保护、交易保障
7. <h3>用户口碑</h3> - 用户评价、成功案例、信誉证明

💡 重点突出：
- 使用<strong>强调：正品保证、快速配送、优质服务
- 使用<ul><li>列举：支付方式、配送选项、售后政策
- 使用<blockquote>引用：用户好评、服务承诺
- 包含具体数据：配送时间、用户数量、商品种类

🔒 信任要素：
- 强调资质认证和安全保障
- 突出用户评价和口碑
- 展示专业的客服和售后体系'
        ),
        
        'tech' => array(
            'name' => '科技技术网站',
            'description' => '适用于科技公司、技术服务、软件开发类网站',
            'content' => '你是一位科技行业的专业内容策划师，请为科技网站撰写一篇技术导向的详细简介：

💻 科技网站专业简介要求：
- 字数：1200-1600字，突出技术实力
- 重点：技术创新、解决方案、行业经验
- 风格：专业、前沿、技术导向

🚀 核心内容结构：
1. <h3>公司概述</h3> - 技术愿景、发展历程、行业地位
2. <h3>核心技术</h3> - 技术栈、创新能力、研发实力
3. <h3>产品服务</h3> - 解决方案、产品特色、应用场景
4. <h3>技术优势</h3> - 核心竞争力、技术壁垒、创新点
5. <h3>行业经验</h3> - 成功案例、客户群体、项目经验
6. <h3>团队实力</h3> - 技术团队、专业背景、研发能力
7. <h3>未来规划</h3> - 技术路线、发展方向、创新计划

⚡ 技术重点：
- 使用<strong>强调：核心技术、创新能力、技术优势
- 使用<ul><li>列举：技术栈、服务项目、解决方案
- 使用<code>标签：技术名词、编程语言、框架名称
- 包含技术指标：性能数据、效率提升、技术参数

🎯 专业表达：
- 突出技术创新和研发能力
- 展示成功案例和项目经验
- 强调技术团队的专业性
- 体现对行业趋势的把握'
        ),
        
        'education' => array(
            'name' => '教育培训网站',
            'description' => '专为教育机构、在线学习、培训平台设计',
            'content' => '你是一位教育行业的专业内容策划师，请为教育网站撰写一篇教育导向的详细简介：

📚 教育网站专业简介要求：
- 字数：1000-1400字，突出教育价值
- 重点：教学质量、师资力量、学习效果
- 风格：专业、权威、关怀学员

🎓 核心内容结构：
1. <h3>机构概述</h3> - 教育理念、办学宗旨、发展历程
2. <h3>课程体系</h3> - 课程设置、教学内容、学习路径
3. <h3>师资力量</h3> - 教师团队、专业背景、教学经验
4. <h3>教学特色</h3> - 教学方法、创新模式、个性化服务
5. <h3>学习支持</h3> - 学习工具、辅导服务、技术支持
6. <h3>学员成果</h3> - 学习效果、就业情况、成功案例
7. <h3>服务保障</h3> - 学习保障、售后服务、持续支持

📖 教育重点：
- 使用<strong>强调：教学质量、师资力量、学习效果
- 使用<ul><li>列举：课程类别、教学服务、学习工具
- 使用<blockquote>引用：学员评价、教育理念
- 包含教育数据：通过率、就业率、学员数量

🌟 价值体现：
- 突出教育成果和学员成功
- 展示专业的师资团队
- 强调个性化的教学服务
- 体现对学员发展的关注'
        ),
        
        'medical' => array(
            'name' => '医疗健康网站',
            'description' => '适用于医疗机构、健康服务、医药类网站',
            'content' => '你是一位医疗健康行业的专业内容策划师，请为医疗网站撰写一篇专业权威的详细简介：

🏥 医疗网站专业简介要求：
- 字数：1100-1500字，突出专业权威
- 重点：医疗技术、专家团队、服务质量
- 风格：专业、权威、关爱患者

⚕️ 核心内容结构：
1. <h3>机构概述</h3> - 医疗理念、服务宗旨、发展历程
2. <h3>专科特色</h3> - 重点科室、特色技术、诊疗优势
3. <h3>专家团队</h3> - 医师团队、专业资质、临床经验
4. <h3>医疗技术</h3> - 先进设备、诊疗技术、创新疗法
5. <h3>服务流程</h3> - 就诊流程、预约服务、便民措施
6. <h3>质量保障</h3> - 医疗质量、安全管理、服务标准
7. <h3>患者关怀</h3> - 人文关怀、康复指导、健康教育

🩺 医疗重点：
- 使用<strong>强调：专业技术、权威专家、优质服务
- 使用<ul><li>列举：诊疗项目、医疗设备、服务特色
- 使用<blockquote>引用：患者好评、医疗理念
- 包含医疗数据：成功率、康复率、服务量

💙 人文关怀：
- 突出专业的医疗技术
- 展示权威的专家团队
- 强调优质的医疗服务
- 体现对患者的人文关怀

⚠️ 注意：内容需符合医疗广告法规，避免夸大宣传'
        ),

        'finance' => array(
            'name' => '金融理财网站',
            'description' => '适用于银行、保险、投资、理财类网站',
            'content' => '你是一位金融行业的专业内容策划师，请为金融网站撰写一篇专业可信的详细简介：

💰 金融网站专业简介要求：
- 字数：1000-1400字，突出专业可信
- 重点：资质认证、风险控制、收益保障
- 风格：专业、稳健、值得信赖

🏦 核心内容结构：
1. <h3>机构概述</h3> - 公司背景、资质认证、监管合规
2. <h3>产品服务</h3> - 金融产品、理财方案、投资选择
3. <h3>风险管理</h3> - 风控体系、安全保障、合规管理
4. <h3>专业团队</h3> - 专家团队、从业资质、行业经验
5. <h3>技术安全</h3> - 系统安全、数据保护、交易保障
6. <h3>客户服务</h3> - 服务流程、客户支持、投资顾问
7. <h3>业绩表现</h3> - 历史业绩、客户案例、市场表现

💎 金融重点：
- 使用<strong>强调：资质认证、风险控制、专业服务
- 使用<ul><li>列举：金融产品、服务项目、安全措施
- 使用<blockquote>引用：监管要求、投资理念
- 包含关键数据：资产规模、客户数量、合规记录

🛡️ 信任建立：
- 突出监管合规和资质认证
- 展示专业的风险管理能力
- 强调客户资金安全保障
- 体现长期稳健的经营理念

⚠️ 注意：严格遵守金融广告法规，不做收益承诺'
        ),

        'travel' => array(
            'name' => '旅游出行网站',
            'description' => '专为旅游、酒店、出行服务类网站设计',
            'content' => '你是一位旅游行业的专业内容策划师，请为旅游网站撰写一篇充满吸引力的详细简介：

✈️ 旅游网站专业简介要求：
- 字数：900-1300字，突出体验价值
- 重点：旅游体验、服务品质、目的地特色
- 风格：生动、吸引人、充满想象

🌍 核心内容结构：
1. <h3>平台概述</h3> - 旅游理念、服务宗旨、品牌特色
2. <h3>产品服务</h3> - 旅游线路、酒店预订、出行服务
3. <h3>目的地优势</h3> - 热门景点、特色体验、文化魅力
4. <h3>服务保障</h3> - 行程安排、导游服务、安全保障
5. <h3>预订便利</h3> - 预订流程、支付方式、客服支持
6. <h3>用户体验</h3> - 客户评价、旅行故事、满意度
7. <h3>特色亮点</h3> - 独家线路、优惠政策、增值服务

🎒 旅游重点：
- 使用<strong>强调：独特体验、优质服务、超值价格
- 使用<ul><li>列举：旅游产品、服务项目、目的地选择
- 使用<blockquote>引用：客户好评、旅行感受
- 包含吸引数据：景点数量、客户满意度、服务覆盖

🌟 体验价值：
- 突出独特的旅游体验
- 展示专业的服务团队
- 强调便捷的预订流程
- 体现对客户旅行的用心安排'
        ),

        'news' => array(
            'name' => '新闻媒体网站',
            'description' => '适用于新闻门户、媒体平台、资讯类网站',
            'content' => '你是一位媒体行业的专业内容策划师，请为新闻媒体网站撰写一篇权威专业的详细简介：

📰 新闻媒体网站专业简介要求：
- 字数：1000-1400字，突出权威性和时效性
- 重点：新闻价值、报道质量、媒体影响力
- 风格：权威、客观、专业可信

📺 核心内容结构：
1. <h3>媒体概述</h3> - 媒体定位、办媒理念、发展历程
2. <h3>内容特色</h3> - 报道领域、内容优势、独家资源
3. <h3>编辑团队</h3> - 记者团队、专业背景、行业经验
4. <h3>报道质量</h3> - 新闻标准、事实核查、客观公正
5. <h3>技术平台</h3> - 发布平台、移动端、多媒体展示
6. <h3>读者服务</h3> - 用户体验、互动功能、个性化推荐
7. <h3>社会影响</h3> - 媒体影响力、社会责任、公信力

📊 媒体重点：
- 使用<strong>强调：权威报道、独家新闻、专业分析
- 使用<ul><li>列举：报道领域、栏目设置、服务功能
- 使用<blockquote>引用：媒体理念、社会责任
- 包含媒体数据：访问量、影响力指标、覆盖范围

🎯 价值体现：
- 突出新闻的权威性和时效性
- 展示专业的编辑记者团队
- 强调客观公正的报道立场
- 体现媒体的社会责任担当'
        ),

        'food' => array(
            'name' => '餐饮美食网站',
            'description' => '适用于餐厅、美食平台、食品类网站',
            'content' => '你是一位餐饮行业的专业内容策划师，请为美食网站撰写一篇诱人美味的详细简介：

🍽️ 美食网站专业简介要求：
- 字数：900-1200字，突出美味体验
- 重点：菜品特色、食材品质、用餐体验
- 风格：诱人、温馨、充满食欲

👨‍🍳 核心内容结构：
1. <h3>品牌概述</h3> - 餐饮理念、品牌故事、文化传承
2. <h3>菜品特色</h3> - 招牌菜品、口味特点、制作工艺
3. <h3>食材品质</h3> - 原料选择、新鲜保证、健康理念
4. <h3>用餐环境</h3> - 装修风格、氛围营造、服务设施
5. <h3>服务体验</h3> - 服务流程、员工素质、客户关怀
6. <h3>特色服务</h3> - 外卖配送、预订服务、定制需求
7. <h3>客户口碑</h3> - 顾客评价、回头客、推荐指数

🥘 美食重点：
- 使用<strong>强调：美味可口、新鲜食材、贴心服务
- 使用<ul><li>列举：菜品种类、服务项目、特色优势
- 使用<blockquote>引用：顾客好评、美食理念
- 包含诱人描述：口感体验、视觉效果、香味特色

🌟 体验价值：
- 突出菜品的美味和特色
- 展示优质的食材和工艺
- 强调温馨的用餐环境
- 体现对顾客的用心服务'
        )
    );
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>AI提示词管理器</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #333; margin-bottom: 10px; }
        .header p { color: #666; }
        .message { padding: 15px; margin: 20px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .current-prompt { background: #e7f3ff; padding: 15px; border-radius: 5px; margin-bottom: 30px; }
        .prompt-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; }
        .prompt-card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; background: white; }
        .prompt-card h3 { color: #007cba; margin-top: 0; }
        .prompt-card .description { color: #666; margin-bottom: 15px; font-style: italic; }
        .prompt-preview { background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 12px; max-height: 150px; overflow-y: auto; margin-bottom: 15px; }
        .apply-btn { background: #007cba; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; width: 100%; }
        .apply-btn:hover { background: #005a8b; }
        .current-badge { background: #28a745; color: white; padding: 2px 8px; border-radius: 3px; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI提示词管理器</h1>
            <p>选择适合您网站类型的专业提示词模板，一键应用到后台配置</p>
        </div>

        <?php if (isset($success_message)): ?>
            <div class="message success"><?php echo $success_message; ?></div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="message error"><?php echo $error_message; ?></div>
        <?php endif; ?>

        <div class="current-prompt">
            <h3>📋 当前配置的提示词</h3>
            <?php if (!empty($current_prompt)): ?>
                <div style="background: white; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto; font-size: 14px;">
                    <?php echo nl2br(htmlspecialchars(substr($current_prompt, 0, 500))); ?>
                    <?php if (strlen($current_prompt) > 500): ?>
                        <span style="color: #666;">... (已截取前500字符)</span>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <p style="color: #666;">暂未配置提示词模板</p>
            <?php endif; ?>
        </div>

        <h2>🎯 选择提示词模板</h2>
        <div class="prompt-grid">
            <?php 
            $prompts = getPromptTemplates();
            foreach ($prompts as $key => $prompt): 
                $is_current = !empty($current_prompt) && strpos($current_prompt, substr($prompt['content'], 0, 100)) !== false;
            ?>
                <div class="prompt-card">
                    <h3>
                        <?php echo htmlspecialchars($prompt['name']); ?>
                        <?php if ($is_current): ?>
                            <span class="current-badge">当前使用</span>
                        <?php endif; ?>
                    </h3>
                    <div class="description"><?php echo htmlspecialchars($prompt['description']); ?></div>
                    <div class="prompt-preview">
                        <?php echo nl2br(htmlspecialchars(substr($prompt['content'], 0, 300))); ?>...
                    </div>
                    <form method="post" style="margin: 0;">
                        <input type="hidden" name="prompt_key" value="<?php echo $key; ?>">
                        <button type="submit" name="apply_prompt" class="apply-btn" 
                                <?php echo $is_current ? 'disabled style="background: #6c757d;"' : ''; ?>>
                            <?php echo $is_current ? '✅ 已应用' : '🚀 应用此模板'; ?>
                        </button>
                    </form>
                </div>
            <?php endforeach; ?>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;">
            <h3>💡 使用说明</h3>
            <ul>
                <li><strong>选择模板：</strong>根据您的网站类型选择最适合的提示词模板</li>
                <li><strong>一键应用：</strong>点击"应用此模板"按钮即可将模板应用到后台配置</li>
                <li><strong>即时生效：</strong>应用后立即生效，可直接在网站编辑页面使用AI生成功能</li>
                <li><strong>自定义修改：</strong>应用后可在后台"选项设置"中进一步自定义修改</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 20px;">
            <a href="option.php?opt=misc" style="color: #007cba; text-decoration: none;">
                ← 返回选项设置页面
            </a>
        </div>
    </div>
</body>
</html>
