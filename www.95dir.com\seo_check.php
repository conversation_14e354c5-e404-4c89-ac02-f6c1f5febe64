<?php
/**
 * SEO技术检查脚本
 * 检查网站常见的SEO技术问题
 */

// 引入配置文件
require_once 'config.php';

class SEOChecker {
    private $db;
    private $issues = [];
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * 执行所有SEO检查
     */
    public function runAllChecks() {
        echo "<h1>95目录网 SEO技术检查报告</h1>\n";
        echo "<p>检查时间：" . date('Y-m-d H:i:s') . "</p>\n";
        
        $this->checkDuplicateContent();
        $this->checkMissingMetaTags();
        $this->checkBrokenLinks();
        $this->checkImageAltTags();
        $this->checkPageTitles();
        $this->checkCanonicalTags();
        $this->checkSitemapStatus();
        $this->checkRobotsFile();
        
        $this->generateReport();
    }
    
    /**
     * 检查重复内容
     */
    private function checkDuplicateContent() {
        echo "<h2>1. 检查重复内容</h2>\n";
        
        // 检查重复的网站标题
        $sql = "SELECT web_name, COUNT(*) as count FROM " . $this->db->table('websites') . " 
                WHERE web_status = 3 GROUP BY web_name HAVING count > 1";
        $duplicates = $this->db->fetch_all($sql);
        
        if (!empty($duplicates)) {
            echo "<p style='color: orange;'>⚠️ 发现重复网站标题：</p>\n";
            foreach ($duplicates as $dup) {
                echo "<li>{$dup['web_name']} (重复{$dup['count']}次)</li>\n";
                $this->issues[] = "重复网站标题：{$dup['web_name']}";
            }
        } else {
            echo "<p style='color: green;'>✅ 未发现重复网站标题</p>\n";
        }
        
        // 检查重复的网站描述
        $sql = "SELECT web_intro, COUNT(*) as count FROM " . $this->db->table('websites') . " 
                WHERE web_status = 3 AND web_intro != '' GROUP BY web_intro HAVING count > 1 LIMIT 10";
        $duplicateIntros = $this->db->fetch_all($sql);
        
        if (!empty($duplicateIntros)) {
            echo "<p style='color: orange;'>⚠️ 发现重复网站描述：</p>\n";
            foreach ($duplicateIntros as $dup) {
                $intro = mb_substr($dup['web_intro'], 0, 50, 'UTF-8') . '...';
                echo "<li>{$intro} (重复{$dup['count']}次)</li>\n";
                $this->issues[] = "重复网站描述";
            }
        } else {
            echo "<p style='color: green;'>✅ 未发现重复网站描述</p>\n";
        }
    }
    
    /**
     * 检查缺失的Meta标签
     */
    private function checkMissingMetaTags() {
        echo "<h2>2. 检查缺失的Meta标签</h2>\n";
        
        // 检查缺失描述的网站
        $sql = "SELECT COUNT(*) as count FROM " . $this->db->table('websites') . " 
                WHERE web_status = 3 AND (web_intro = '' OR web_intro IS NULL)";
        $missingIntro = $this->db->get_count_sql($sql);
        
        if ($missingIntro > 0) {
            echo "<p style='color: orange;'>⚠️ 发现 {$missingIntro} 个网站缺失描述</p>\n";
            $this->issues[] = "{$missingIntro}个网站缺失描述";
        } else {
            echo "<p style='color: green;'>✅ 所有网站都有描述</p>\n";
        }
        
        // 检查缺失标签的网站
        $sql = "SELECT COUNT(*) as count FROM " . $this->db->table('websites') . " 
                WHERE web_status = 3 AND (web_tags = '' OR web_tags IS NULL)";
        $missingTags = $this->db->get_count_sql($sql);
        
        if ($missingTags > 0) {
            echo "<p style='color: orange;'>⚠️ 发现 {$missingTags} 个网站缺失标签</p>\n";
            $this->issues[] = "{$missingTags}个网站缺失标签";
        } else {
            echo "<p style='color: green;'>✅ 所有网站都有标签</p>\n";
        }
    }
    
    /**
     * 检查损坏的链接
     */
    private function checkBrokenLinks() {
        echo "<h2>3. 检查损坏的链接</h2>\n";
        
        // 检查可能的无效URL
        $sql = "SELECT web_id, web_name, web_url FROM " . $this->db->table('websites') . " 
                WHERE web_status = 3 AND (web_url LIKE '%localhost%' OR web_url LIKE '%127.0.0.1%' 
                OR web_url LIKE '%test%' OR web_url = '') LIMIT 20";
        $suspiciousUrls = $this->db->fetch_all($sql);
        
        if (!empty($suspiciousUrls)) {
            echo "<p style='color: orange;'>⚠️ 发现可疑URL：</p>\n";
            foreach ($suspiciousUrls as $url) {
                echo "<li>{$url['web_name']} - {$url['web_url']}</li>\n";
                $this->issues[] = "可疑URL：{$url['web_name']}";
            }
        } else {
            echo "<p style='color: green;'>✅ 未发现明显的无效URL</p>\n";
        }
    }
    
    /**
     * 检查图片Alt标签
     */
    private function checkImageAltTags() {
        echo "<h2>4. 检查图片Alt标签</h2>\n";
        
        // 检查缺失图片的网站
        $sql = "SELECT COUNT(*) as count FROM " . $this->db->table('websites') . " 
                WHERE web_status = 3 AND (web_pic = '' OR web_pic IS NULL)";
        $missingImages = $this->db->get_count_sql($sql);
        
        if ($missingImages > 0) {
            echo "<p style='color: orange;'>⚠️ 发现 {$missingImages} 个网站缺失图片</p>\n";
            $this->issues[] = "{$missingImages}个网站缺失图片";
        } else {
            echo "<p style='color: green;'>✅ 所有网站都有图片</p>\n";
        }
    }
    
    /**
     * 检查页面标题
     */
    private function checkPageTitles() {
        echo "<h2>5. 检查页面标题</h2>\n";
        
        // 检查标题长度
        $sql = "SELECT web_id, web_name FROM " . $this->db->table('websites') . " 
                WHERE web_status = 3 AND (CHAR_LENGTH(web_name) < 5 OR CHAR_LENGTH(web_name) > 60) LIMIT 20";
        $badTitles = $this->db->fetch_all($sql);
        
        if (!empty($badTitles)) {
            echo "<p style='color: orange;'>⚠️ 发现标题长度不合适的网站：</p>\n";
            foreach ($badTitles as $title) {
                $length = mb_strlen($title['web_name'], 'UTF-8');
                echo "<li>{$title['web_name']} (长度：{$length})</li>\n";
                $this->issues[] = "标题长度不合适：{$title['web_name']}";
            }
        } else {
            echo "<p style='color: green;'>✅ 所有网站标题长度合适</p>\n";
        }
    }
    
    /**
     * 检查Canonical标签
     */
    private function checkCanonicalTags() {
        echo "<h2>6. 检查Canonical标签</h2>\n";
        echo "<p style='color: green;'>✅ 模板文件中已添加Canonical标签</p>\n";
    }
    
    /**
     * 检查Sitemap状态
     */
    private function checkSitemapStatus() {
        echo "<h2>7. 检查Sitemap状态</h2>\n";
        
        if (file_exists('sitemap.xml')) {
            $lastModified = date('Y-m-d H:i:s', filemtime('sitemap.xml'));
            echo "<p style='color: green;'>✅ Sitemap文件存在，最后修改时间：{$lastModified}</p>\n";
        } else {
            echo "<p style='color: red;'>❌ Sitemap文件不存在</p>\n";
            $this->issues[] = "Sitemap文件不存在";
        }
    }
    
    /**
     * 检查Robots文件
     */
    private function checkRobotsFile() {
        echo "<h2>8. 检查Robots.txt文件</h2>\n";
        
        if (file_exists('robots.txt')) {
            $content = file_get_contents('robots.txt');
            if (strpos($content, 'Sitemap:') !== false) {
                echo "<p style='color: green;'>✅ Robots.txt文件存在且包含Sitemap引用</p>\n";
            } else {
                echo "<p style='color: orange;'>⚠️ Robots.txt文件存在但缺少Sitemap引用</p>\n";
                $this->issues[] = "Robots.txt缺少Sitemap引用";
            }
        } else {
            echo "<p style='color: red;'>❌ Robots.txt文件不存在</p>\n";
            $this->issues[] = "Robots.txt文件不存在";
        }
    }
    
    /**
     * 生成检查报告
     */
    private function generateReport() {
        echo "<h2>SEO检查总结</h2>\n";
        
        if (empty($this->issues)) {
            echo "<p style='color: green; font-size: 18px;'>🎉 恭喜！未发现严重的SEO问题</p>\n";
        } else {
            echo "<p style='color: orange; font-size: 18px;'>⚠️ 发现 " . count($this->issues) . " 个需要关注的问题：</p>\n";
            echo "<ol>\n";
            foreach ($this->issues as $issue) {
                echo "<li>{$issue}</li>\n";
            }
            echo "</ol>\n";
        }
        
        echo "<h3>SEO优化建议</h3>\n";
        echo "<ul>\n";
        echo "<li>定期检查和更新网站内容，避免重复内容</li>\n";
        echo "<li>为所有网站添加完整的描述和标签</li>\n";
        echo "<li>定期检查链接有效性</li>\n";
        echo "<li>保持Sitemap文件更新</li>\n";
        echo "<li>监控网站加载速度</li>\n";
        echo "<li>确保移动端友好性</li>\n";
        echo "</ul>\n";
    }
}

// 执行SEO检查
if (isset($_GET['check']) && $_GET['check'] == 'seo') {
    $checker = new SEOChecker($DB);
    $checker->runAllChecks();
} else {
    echo "<h1>SEO技术检查工具</h1>\n";
    echo "<p><a href='?check=seo'>点击开始SEO检查</a></p>\n";
}
?>
