# 站长推荐选项卡功能说明

## 功能概述
将首页的"待审核"、"黑名单"、"审核不通过"功能集成到"站长推荐"容器中，通过选项卡切换的方式展示不同类型的网站。

## 主要修改

### 1. HTML结构修改
- **文件位置**: `themes/default/index.html`
- **修改内容**:
  - 将原来的单一"站长推荐"容器改为带选项卡的多功能容器
  - 添加了4个选项卡：站长推荐、待审核、黑名单、审核不通过
  - 每个选项卡都有对应的内容区域
  - 删除了原来单独的待审核、黑名单、审核不通过容器（避免重复）

### 2. CSS样式添加
- **选项卡标题样式**: 
  - 使用flexbox布局，4个选项卡平均分布
  - 悬停效果和活动状态样式
  - 不同状态使用不同的颜色和图标
- **选项卡内容样式**:
  - 内容区域的显示/隐藏控制
  - 不同类型网站的徽章样式（推荐、待审、黑名单、不通过）
- **响应式设计**:
  - 移动端优化，小屏幕下隐藏图标
  - 字体大小和间距的适配

### 3. JavaScript功能
- **选项卡切换逻辑**:
  - 点击选项卡标题切换对应内容
  - 活动状态的添加和移除
  - 点击动画效果
- **初始化**:
  - 页面加载完成后自动初始化选项卡功能
  - 默认显示"站长推荐"选项卡

## 功能特点

### 1. 用户体验
- **一键切换**: 鼠标点击即可切换不同类型的网站列表
- **视觉反馈**: 选项卡有明显的活动状态和悬停效果
- **动画效果**: 点击时有缩放动画，提升交互体验

### 2. 界面设计
- **统一风格**: 所有选项卡使用相同的网格布局和卡片样式
- **状态区分**: 不同类型的网站使用不同颜色的徽章和图标
- **图片处理**: 
  - 待审核和审核不通过：色调调整（hue-rotate）
  - 黑名单：灰度处理（grayscale）

### 3. 数据展示
- **站长推荐**: 显示35个推荐网站，链接到外部网站
- **待审核**: 显示35个待审核网站，链接到详情页面
- **黑名单**: 显示35个黑名单网站，链接到黑名单详情页
- **审核不通过**: 显示35个审核不通过网站，链接到详情页面

## 技术实现

### 1. 数据获取
使用现有的函数获取不同类型的网站数据：
- `get_websites(0, 35, false, true)` - 获取推荐网站
- `get_pending_websites(35)` - 获取待审核网站
- `get_blacklist_websites(35)` - 获取黑名单网站
- `get_rejected_websites(35)` - 获取审核不通过网站

### 2. 选项卡实现
- **HTML**: 使用`data-tab`属性关联选项卡标题和内容
- **CSS**: 使用`.active`类控制显示状态
- **JavaScript**: 事件监听器处理点击切换逻辑

### 3. 响应式适配
- **桌面端**: 完整显示图标和文字
- **平板端**: 减小字体和间距
- **手机端**: 隐藏图标，只显示文字

## 使用说明

### 1. 用户操作
1. 访问网站首页
2. 在右侧找到"站长推荐"容器
3. 点击不同的选项卡标题（站长推荐、待审核、黑名单、审核不通过）
4. 查看对应类型的网站列表

### 2. 管理员维护
- 网站数据通过后台管理系统维护
- 选项卡功能无需额外配置，自动生效
- 如需调整显示数量，修改函数参数即可

## 兼容性
- **浏览器**: 支持所有现代浏览器（Chrome、Firefox、Safari、Edge）
- **设备**: 支持桌面端、平板端、手机端
- **现有功能**: 不影响网站其他功能，完全向后兼容

## 后续优化建议
1. 可以考虑添加数量统计显示
2. 可以添加自动刷新功能
3. 可以考虑添加搜索和筛选功能
4. 可以添加更多的动画效果
