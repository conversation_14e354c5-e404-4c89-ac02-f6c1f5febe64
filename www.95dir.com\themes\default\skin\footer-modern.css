/* 现代化页脚样式 */
.modern-footer {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    margin-top: 40px;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Aria<PERSON>, sans-serif;
    box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.1);
}

/* 徽章区域 */
.footer-badge {
    text-align: center;
    padding: 20px 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.badge-link {
    display: inline-block;
    transition: transform 0.3s ease;
}

.badge-link:hover {
    transform: translateY(-2px);
}

.rating-badge {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

/* 主要内容区域 */
.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

/* 导航区域 */
.footer-nav {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.nav-section h4 {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.3);
    position: relative;
}

.nav-section h4::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30px;
    height: 2px;
    background: #ffd700;
}

.nav-links {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.nav-links a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    padding: 6px 0;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    padding-left: 12px;
    font-size: 14px;
}

.nav-links a:hover {
    color: #ffd700;
    border-left-color: #ffd700;
    padding-left: 16px;
    transform: translateX(4px);
}

/* 联系信息区域 */
.contact-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.qq-group {
    margin-bottom: 15px;
}

.qq-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #ffffff;
    text-decoration: none;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: 25px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.qq-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.qq-icon {
    font-size: 18px;
}

/* 统计信息 */
.stats-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 15px;
    backdrop-filter: blur(5px);
}

.stat-label {
    color: rgba(255, 255, 255, 0.8);
}

.stat-value {
    color: #ffd700;
    font-weight: 600;
    min-width: 60px;
    text-align: center;
}

.stat-unit {
    color: rgba(255, 255, 255, 0.8);
}

/* 网站描述区域 */
.footer-description {
    background: rgba(255, 255, 255, 0.1);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.site-intro h3 {
    color: #ffd700;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 12px;
    text-align: center;
}

.site-intro p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    font-size: 16px;
    text-align: center;
    margin-bottom: 15px;
}

.keywords {
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.keywords-label {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
}

.keywords-list {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
}

/* 版权信息区域 */
.footer-bottom {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px 0;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.copyright-info {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.copyright-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-bottom: 8px;
}

.copyright-text .icp-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: color 0.3s ease;
}

.copyright-text .icp-link:hover {
    color: #ffd700;
}

.runtime-info {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .footer-nav {
        grid-template-columns: 1fr;
        gap: 25px;
    }
    
    .nav-section {
        text-align: center;
    }
    
    .nav-links {
        align-items: center;
    }
    
    .nav-links a {
        border-left: none;
        border-bottom: 2px solid transparent;
        padding-left: 0;
        padding-bottom: 8px;
    }
    
    .nav-links a:hover {
        border-left: none;
        border-bottom-color: #ffd700;
        padding-left: 0;
        transform: translateY(-2px);
    }
    
    .contact-info {
        align-items: center;
    }
    
    .stats-info {
        align-items: center;
    }
    
    .footer-content {
        padding: 30px 15px;
    }
    
    .footer-description {
        padding: 20px 15px;
    }
    
    .site-intro h3 {
        font-size: 20px;
    }
    
    .site-intro p {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .footer-badge {
        padding: 15px 0;
    }
    
    .rating-badge {
        width: 200px;
        height: auto;
    }
    
    .qq-link {
        padding: 8px 12px;
        font-size: 14px;
    }
    
    .stat-item {
        padding: 6px 10px;
        font-size: 13px;
    }
    
    .keywords {
        font-size: 12px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modern-footer {
    animation: fadeInUp 0.6s ease-out;
}

/* 滚动时的视差效果 */
.modern-footer {
    position: relative;
    overflow: hidden;
}

.modern-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}
