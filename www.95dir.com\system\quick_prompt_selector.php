<?php
// fixed_prompt_selector.php - 修复版本
header('Content-Type: application/json; charset=utf-8');

// 开启错误报告但不显示
error_reporting(E_ALL);
ini_set('display_errors', 0);

// 开启输出缓冲
ob_start();

try {
    // 引入系统配置
    define('IN_ADMIN', TRUE);
    define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
    define('APP_PATH', ROOT_PATH.'source/');
    require(APP_PATH.'init.php');
    require(APP_PATH.'module/static.php');
    
    // 清除任何意外输出
    ob_clean();
    
} catch (Exception $e) {
    ob_clean();
    echo json_encode(array('status' => 'error', 'message' => '系统配置加载失败: ' . $e->getMessage()));
    exit;
} catch (Error $e) {
    ob_clean();
    echo json_encode(array('status' => 'error', 'message' => 'PHP错误: ' . $e->getMessage()));
    exit;
}

// 提示词模板数据
function getPromptTemplates() {
    return array(
        'general' => array(
            'name' => '通用网站模板',
            'description' => '适用于各类网站的通用模板',
            'content' => "你是一个专业的网站内容撰写助手，请根据以下信息为网站生成一段简洁、有吸引力的AI简介（输出1000字左右的HTML内容）：\n" .
                        "使用<p><ul><li><strong>等基础标签\n" .
                        "包含SEO关键词但保持自然\n" .
                        "分3-5个段落，每个段落有明确主题\n" .
                        "符合中文阅读习惯\n" .
                        "突出网站特色，语言流畅自然\n" .
                        "只要一个文本网址不要链接，不要有欢迎词语"
        ),
        
        'ecommerce' => array(
            'name' => '电商购物网站',
            'description' => '专为电商、购物、零售类网站设计',
            'content' => "你是一个专业的电商网站内容撰写助手，请根据以下信息为电商网站生成一段专业的AI简介（输出1000字左右的HTML内容）：\n" .
                        "突出商品品质、购物体验、服务保障\n" .
                        "使用<p><strong><ul><li>等HTML标签\n" .
                        "分3-5个段落：平台概述、商品优势、购物体验、服务保障\n" .
                        "强调正品保证、快速配送、优质服务\n" .
                        "符合中文阅读习惯，语言流畅自然\n" .
                        "只要一个文本网址不要链接，不要有欢迎词语"
        ),
        
        'tech' => array(
            'name' => '科技技术网站',
            'description' => '适用于科技公司、技术服务、软件开发类网站',
            'content' => "你是一个专业的科技网站内容撰写助手，请根据以下信息为科技网站生成一段技术导向的AI简介（输出1000字左右的HTML内容）：\n" .
                        "突出技术实力和创新能力\n" .
                        "使用<p><strong><code><ul><li>等HTML标签\n" .
                        "分3-5个段落：公司概述、核心技术、产品服务、技术优势\n" .
                        "强调技术创新、解决方案、行业经验\n" .
                        "符合中文阅读习惯，语言流畅自然\n" .
                        "只要一个文本网址不要链接，不要有欢迎词语"
        ),
        
        'education' => array(
            'name' => '教育培训网站',
            'description' => '专为教育机构、在线学习、培训平台设计',
            'content' => "你是一个专业的教育网站内容撰写助手，请根据以下信息为教育网站生成一段教育导向的AI简介（输出1000字左右的HTML内容）：\n" .
                        "突出教学质量、师资力量、学习效果\n" .
                        "使用<p><strong><ul><li>等HTML标签\n" .
                        "分3-5个段落：机构概述、课程体系、师资力量、教学特色\n" .
                        "强调专业教学和学员成果\n" .
                        "符合中文阅读习惯，语言流畅自然\n" .
                        "只要一个文本网址不要链接，不要有欢迎词语"
        ),
        
        'medical' => array(
            'name' => '医疗健康网站',
            'description' => '适用于医疗机构、健康服务、医药类网站',
            'content' => "你是一个专业的医疗网站内容撰写助手，请根据以下信息为医疗网站生成一段专业权威的AI简介（输出1000字左右的HTML内容）：\n" .
                        "突出医疗技术、专家团队、服务质量\n" .
                        "使用<p><strong><ul><li>等HTML标签\n" .
                        "分3-5个段落：机构概述、专科特色、专家团队、服务保障\n" .
                        "强调专业技术和患者关怀\n" .
                        "内容需符合医疗广告法规，避免夸大宣传\n" .
                        "符合中文阅读习惯，语言流畅自然\n" .
                        "只要一个文本网址不要链接，不要有欢迎词语"
        ),

        'simple' => array(
            'name' => '简洁模板',
            'description' => '最简洁的模板，生成速度最快',
            'content' => "你是一个网站内容撰写助手，请根据以下信息为网站生成一段简洁的AI简介（输出400字左右的HTML内容）：\n" .
                        "使用<p><strong>等基础标签\n" .
                        "分3个段落：网站概述、主要功能、联系方式\n" .
                        "语言简洁明了\n" .
                        "只要一个文本网址不要链接，不要有欢迎词语"
        )
    );
}

// 调试信息
$debug_info = array(
    'get_params' => $_GET,
    'post_params' => $_POST,
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'query_string' => isset($_SERVER['QUERY_STRING']) ? $_SERVER['QUERY_STRING'] : ''
);

// 处理获取提示词列表的请求
if (isset($_GET['action']) && $_GET['action'] == 'get_prompts') {
    try {
        $prompts = getPromptTemplates();
        $result = array();
        
        foreach ($prompts as $key => $prompt) {
            $result[] = array(
                'key' => $key,
                'name' => $prompt['name'],
                'description' => $prompt['description'],
                'preview' => substr($prompt['content'], 0, 200) . '...'
            );
        }
        
        echo json_encode(array(
            'status' => 'success', 
            'prompts' => $result,
            'debug' => $debug_info
        ), JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        echo json_encode(array(
            'status' => 'error', 
            'message' => '获取模板失败: ' . $e->getMessage(),
            'debug' => $debug_info
        ), JSON_UNESCAPED_UNICODE);
    }
    
    ob_end_flush();
    exit;
}

// 处理应用提示词的请求
if (isset($_POST['action']) && $_POST['action'] == 'apply_prompt' && isset($_POST['prompt_key'])) {
    try {
        $prompt_key = $_POST['prompt_key'];
        $prompts = getPromptTemplates();
        
        if (isset($prompts[$prompt_key])) {
            $selected_prompt = $prompts[$prompt_key]['content'];
            
            // 更新数据库中的提示词配置
            $table = $DB->table('options');
            $existing = $DB->fetch_one("SELECT option_name FROM $table WHERE option_name = 'ai_prompt_template'");
            
            if ($existing) {
                // 更新现有配置
                $udata = array('option_value' => $selected_prompt);
                $uwhere = array('option_name' => 'ai_prompt_template');
                
                if ($DB->update($table, $udata, $uwhere)) {
                    // 更新配置缓存
                    update_cache('options');
                    echo json_encode(array(
                        'status' => 'success', 
                        'message' => '成功应用提示词模板：' . $prompts[$prompt_key]['name'],
                        'content' => $selected_prompt
                    ), JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode(array('status' => 'error', 'message' => '应用提示词失败，请重试'), JSON_UNESCAPED_UNICODE);
                }
            } else {
                // 插入新配置
                $idata = array(
                    'option_name' => 'ai_prompt_template',
                    'option_value' => $selected_prompt
                );
                
                if ($DB->insert($table, $idata)) {
                    update_cache('options');
                    echo json_encode(array(
                        'status' => 'success', 
                        'message' => '成功应用提示词模板：' . $prompts[$prompt_key]['name'],
                        'content' => $selected_prompt
                    ), JSON_UNESCAPED_UNICODE);
                } else {
                    echo json_encode(array('status' => 'error', 'message' => '应用提示词失败，请重试'), JSON_UNESCAPED_UNICODE);
                }
            }
        } else {
            echo json_encode(array('status' => 'error', 'message' => '无效的提示词模板'), JSON_UNESCAPED_UNICODE);
        }
    } catch (Exception $e) {
        echo json_encode(array(
            'status' => 'error', 
            'message' => '应用失败: ' . $e->getMessage()
        ), JSON_UNESCAPED_UNICODE);
    }
    
    ob_end_flush();
    exit;
}

// 如果没有匹配的条件，返回调试信息
echo json_encode(array(
    'status' => 'error', 
    'message' => '无效的action参数',
    'debug' => $debug_info
), JSON_UNESCAPED_UNICODE);

ob_end_flush();
?>
