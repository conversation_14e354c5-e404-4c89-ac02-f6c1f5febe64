<!DOCTYPE HTML>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<link href="{#$site_root#}themes/default/skin/svg-fix.css" rel="stylesheet" type="text/css" />
<link href="{#$site_root#}themes/default/skin/vip-style.css" rel="stylesheet" type="text/css" />
<link href="{#$site_root#}public/css/logo-preview.css" rel="stylesheet" type="text/css" />
{#include file="script.html"#}
<script type="text/javascript" src="{#$site_root#}themes/default/skin/svg-fix.js"></script>

<style>
.vip-list-header {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%);
    color: #333;
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 25px;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.4);
    border: 3px solid #ffed4e;
}

.vip-list-item {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 3px solid #ffd700;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    position: relative;
    transition: all 0.3s ease;
}

.vip-list-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0,0,0,0.15);
}

.vip-list-item::before {
    content: '👑';
    position: absolute;
    top: -15px;
    right: 20px;
    font-size: 30px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    padding: 5px 10px;
    border-radius: 50%;
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
}

.vip-category-filter {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
}

.vip-category-filter h3 {
    color: #ffd700;
    margin-bottom: 15px;
    text-align: center;
}

.category-links {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.category-links a {
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 13px;
    transition: all 0.3s ease;
}

.category-links a:hover,
.category-links a.active {
    background: #ffd700;
    color: #333;
    transform: scale(1.05);
}

.vip-stats-bar {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: center;
    font-weight: bold;
}
</style>
</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
	{#include file="header.html"#}
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
    		<!-- VIP列表页头部 -->
    		<div class="vip-list-header vip-floating-card vip-stars">
    			<div class="vip-crown vip-sparkle">👑</div>
    			<h2 class="vip-title vip-gradient-text">VIP尊享网站列表</h2>
    			<p class="vip-subtitle">精选优质网站，享受专属特权服务</p>
    		</div>

    		<!-- 统计信息 -->
    		<div class="vip-stats-bar">
    			🌟 共有 {#$total#} 个VIP认证网站为您提供优质服务
    		</div>

    		<!-- 分类筛选 -->
    		<div class="vip-category-filter">
    			<h3>🔍 按分类筛选</h3>
    			<div class="category-links">
    				<a href="?mod=vip_list" {#if $current_category == 0#}class="active"{#/if#}>全部VIP</a>
    				{#foreach from=$categories item=cat#}
    				<a href="?mod=vip_list&cid={#$cat.cate_id#}" {#if $current_category == $cat.cate_id#}class="active"{#/if#}>
    					{#$cat.cate_name#}
    				</a>
    				{#/foreach#}
    			</div>
    		</div>

    		<!-- VIP网站列表 -->
    		{#foreach from=$websites item=web#}
    		<div class="vip-list-item vip-floating-card">
    			<div style="display: flex; align-items: flex-start; gap: 20px;">
    				<div style="flex-shrink: 0;">
    					<a href="{#$web.web_link#}">
    						<img src="{#$web.web_pic#}" width="120" height="90" alt="{#$web.web_name#}"
    						     style="border: 3px solid #ffd700; border-radius: 10px; transition: all 0.3s ease;"
    						     onmouseover="this.style.transform='scale(1.05)'"
    						     onmouseout="this.style.transform='scale(1)'" />
    					</a>
    				</div>

    				<div style="flex: 1;">
    					<h3 style="margin: 0 0 10px 0;">
    						<a href="{#$web.web_link#}" class="vip-gradient-text" style="text-decoration: none; font-size: 18px;">
    							👑 {#$web.web_name#}
    						</a>
    						<span class="vip-badge" style="background: {#$web.vip_badge_color#}; color: white; padding: 4px 10px; border-radius: 15px; font-size: 11px; margin-left: 10px; font-weight: 700;">
    							{#$web.vip_level#}
    						</span>
    					</h3>
    					
    					<p style="color: #666; line-height: 1.6; margin: 10px 0;">
    						{#$web.web_intro|truncate:150#}
    					</p>
    					
    					<div style="display: flex; align-items: center; gap: 20px; margin: 15px 0; font-size: 13px; color: #888;">
    						<span>📂 <a href="{#$web.cate_link#}" style="color: #007bff;">{#$web.cate_name#}</a></span>
    						<span>👁️ {#$web.web_views#} 浏览</span>
    						<span>📅 {#$web.web_ctime#}</span>
    						<span>🔄 {#$web.web_utime#}</span>
    					</div>
    					
    					<div style="display: flex; align-items: center; gap: 15px;">
    						<a href="{#$web.web_link#}" 
    						   style="background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 13px; font-weight: bold;">
    							🚀 查看详情
    						</a>
    						<a href="/go.php?url=http://{#$web.web_url#}" target="_blank" onClick="clickout({#$web.web_id#})"
    						   style="background: linear-gradient(45deg, #ffd700, #ffed4e); color: #333; padding: 8px 16px; border-radius: 20px; text-decoration: none; font-size: 13px; font-weight: bold;">
    							⚡ 直接访问
    						</a>
    						<span style="color: #28a745; font-size: 12px; font-weight: bold;">
    							🌐 {#$web.web_url#}
    						</span>
    					</div>
    				</div>
    			</div>
    		</div>
    		{#foreachelse#}
    		<div style="text-align: center; padding: 50px; background: #f8f9fa; border-radius: 10px; color: #666;">
    			<div style="font-size: 48px; margin-bottom: 20px;">👑</div>
    			<h3>暂无VIP网站</h3>
    			<p>该分类下暂时没有VIP认证网站</p>
    			<a href="?mod=vip_list" style="color: #007bff;">查看所有VIP网站</a>
    		</div>
    		{#/foreach#}

    		<!-- 分页 -->
    		{#if $showpage#}
    		<div style="text-align: center; margin: 30px 0;">
    			{#$showpage#}
    		</div>
    		{#/if#}

    		<!-- VIP特权说明 -->
    		<div class="vip-features" style="margin-top: 30px;">
    			<h3>🌟 VIP网站特权</h3>
    			<ul class="vip-feature-list">
    				<li>优先展示位置，获得更多曝光机会</li>
    				<li>专属VIP标识，彰显网站品质</li>
    				<li>快速审核通道，极速上线</li>
    				<li>专业客服支持，贴心服务保障</li>
    				<li>数据统计优先，实时监控网站表现</li>
    				<li>搜索引擎优化，提升网站排名</li>
    			</ul>
    		</div>
        </div>

        <div id="mainbox-right">
            <!-- VIP升级提示 -->
            <div style="background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%); color: #333; padding: 20px; border-radius: 12px; margin-bottom: 20px; text-align: center; border: 3px solid #ffd700; box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);">
                <div style="font-size: 32px; margin-bottom: 10px;">👑</div>
                <h4 style="margin: 0 0 10px 0; color: #333;">升级VIP享特权</h4>
                <p style="margin: 0 0 15px 0; font-size: 13px; color: #666;">优先展示 • 快速审核 • 专属客服</p>
                <button onclick="showDonatePopup()" style="background: linear-gradient(45deg, #ff6b35, #ff8c42); color: white; padding: 10px 20px; border: none; border-radius: 25px; font-size: 14px; cursor: pointer; font-weight: bold; box-shadow: 0 4px 10px rgba(255, 107, 53, 0.4);">
                    💎 立即升级VIP
                </button>
            </div>

            <!-- 最新VIP网站 -->
            <div id="bestweb" class="mag" style="border: 3px solid #ffd700; border-radius: 12px; background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);">
            	<h3 style="background: linear-gradient(45deg, #ffd700, #ffed4e); color: #333; margin: -1px -1px 15px -1px; padding: 12px; border-radius: 8px 8px 0 0; text-align: center;">👑 最新VIP站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 8, false, true) item=vip#}
                   	<li style="border-bottom: 1px solid #ffd700; padding-bottom: 10px; margin-bottom: 10px;">
                   	    <a href="?mod=vip_detail&id={#$vip.web_id#}">
                   	        <img src="{#$vip.web_pic#}" width="100" height="80" alt="{#$vip.web_name#}" style="border: 2px solid #ffd700; border-radius: 5px;" />
                   	    </a>
                   	    <strong style="display: flex; align-items: center; justify-content: space-between; margin: 5px 0;">
                   	        <a href="?mod=vip_detail&id={#$vip.web_id#}" title="{#$vip.web_name#}" style="color: #ff6b35; flex: 1;">
                   	            👑 {#$vip.web_name#}
                   	        </a>
                   	        <a href="{#$vip.web_furl#}" target="_blank" class="visit" onClick="clickout({#$vip.web_id#})" style="background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 3px 8px; border-radius: 12px; font-size: 10px; text-decoration: none; margin-left: 8px; white-space: nowrap;">
                   	            ⚡ VIP直达
                   	        </a>
                   	    </strong>
                   	    <p style="color: #666; margin: 5px 0 0 0;">{#$vip.web_intro|truncate:50#}</p>
                   	</li>
                   	{#/foreach#}
               	</ul>
            </div>

            <div class="blank10"></div>

            <div id="bestart" style="border: 2px solid #667eea; border-radius: 10px; background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);">
            	<h3 style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; margin: -1px -1px 15px -1px; padding: 12px; border-radius: 8px 8px 0 0; text-align: center;">📰 推荐资讯</h3>
                <ul class="artlist_b">
                	{#foreach from=get_articles(0, 10) item=art#}
                	<li>[<em><a href="{#$art.cate_link#}" title="{#$art.cate_name#}">{#$art.cate_name#}</a></em>]<a href="{#$art.art_link#}">{#$art.art_title#}</a></li>
                    {#/foreach#}
                </ul>
            </div>
        </div>
    </div>
    {#include file="footer.html"#}
</div>

<!-- 统一弹窗 -->
<div id="donate-popup" style="display:none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div class="donate-popup-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 15px; max-width: 500px; width: 90%; border: 3px solid #ffd700;">
        <span class="close" onclick="closeDonatePopup()" style="position: absolute; top: 10px; right: 15px; font-size: 24px; cursor: pointer; color: #999;">&times;</span>
        <h3 style="color: #ffd700; font-family: 'Arial', sans-serif; font-size: 22px; text-align: center; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2); margin-top: 0;">
            👑 VIP服务价格表
        </h3>
        <div style="background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%); padding: 15px; border-radius: 10px; margin: 20px 0; color: #333;">
            <p style="margin: 0; line-height: 1.6; text-align: center; font-weight: bold;">
                <strong style="color: #ff6b35;">💎 VIP直链席位30元/每年</strong> - 顶部VIP位置<br>
                <strong style="color: #28a745;">⭐ 10元上推荐位</strong> - 首页推荐展示<br>
                <strong style="color: #007bff;">⚡ 5元快审服务</strong> - 1-3个工作日审核
            </p>
        </div>
        <p style="text-align: center; margin: 15px 0; color: #666;">
            备注格式：<strong style="color: #ffd700;">VIP/推荐/快审+网址</strong>
        </p>
        <div class="donate-qr-codes" style="display: flex; justify-content: space-around; margin: 20px 0;">
            <div style="text-align: center;">
                <h4 style="color: #28a745;">微信支付</h4>
                <img src="https://cdn.jsdelivr.net/gh/zhuxiaoming2001/tuchuang/wzlingdi/202412022206265.png" alt="WeChat QR Code" style="width: 150px; height: 150px; border: 2px solid #28a745; border-radius: 10px;">
            </div>
            <div style="text-align: center;">
                <h4 style="color: #007bff;">支付宝支付</h4>
                <img src="https://cdn.jsdelivr.net/gh/zhuxiaoming2001/tuchuang/wzlingdi/202412022206984.png" alt="Alipay QR Code" style="width: 150px; height: 150px; border: 2px solid #007bff; border-radius: 10px;">
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin-top: 20px; border-left: 4px solid #ffd700;">
            <h4 style="margin-top: 0; color: #333;">🌟 VIP服务说明：</h4>
            <ul style="margin: 0; padding-left: 20px; line-height: 1.6; color: #666;">
                <li>VIP位：展示在顶部VIP推广区，享受黄金位置</li>
                <li>推荐位：展示在首页推荐区域，获得更多曝光</li>
                <li>快审：1-3个工作日完成审核，快速上线</li>
                <li>专属客服：VIP专线支持，优先处理问题</li>
                <li>数据优先：实时统计，优先更新网站数据</li>
            </ul>
        </div>
    </div>
</div>

<script>
// 统一弹窗功能
function showDonatePopup() {
    document.getElementById('donate-popup').style.display = 'block';
}

function closeDonatePopup() {
    document.getElementById('donate-popup').style.display = 'none';
}

// 点击弹窗外部关闭
document.getElementById('donate-popup').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDonatePopup();
    }
});

// ESC键关闭弹窗
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeDonatePopup();
    }
});
</script>

</body>
</html>
