<?php
// 测试JSON标题提取修复
define('IN_IWEBDIR', true);
define('APP_PATH', './source/');

require('./source/init.php');
require('./source/module/article.php');

echo "<h2>测试JSON标题提取修复</h2>\n";

// 你提供的JSON数据
$json_content = '{
    "id": 36685,
    "desc": "20分钟试听经典国语dj串烧，劲爆dj合集，DJ打碟现场 20分钟试听经典国语dj串烧，劲爆dj合集，DJ打碟现场，高速必听越听越上头#经典老歌#劲爆dj#中文dj#越听越上头#dj",
    "music": "https://sf5-hl-cdn-tos.douyinstatic.com/obj/ies-music/7499955641297996554.mp3"
}';

echo "<h3>1. 测试JSON数据提取</h3>\n";
echo "<h4>原始JSON:</h4>\n";
echo "<pre>" . htmlspecialchars($json_content) . "</pre>\n";

// 测试提取函数
$music_data = extract_music_urls_with_titles($json_content);
echo "<h4>extract_music_urls_with_titles 结果:</h4>\n";
if (!empty($music_data)) {
    foreach ($music_data as $i => $data) {
        echo "<p><strong>提取结果 " . ($i + 1) . ":</strong><br>";
        echo "标题: '" . htmlspecialchars($data['title']) . "'<br>";
        echo "URL: " . htmlspecialchars($data['url']) . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>没有提取到数据</p>\n";
}

// 删除现有测试文章
$DB->query("DELETE FROM ".$DB->table('articles')." WHERE art_title='JSON标题测试'");

// 创建测试文章
$title = $DB->escape_string('JSON标题测试');
$content = $DB->escape_string($json_content);
$intro = $DB->escape_string('测试JSON标题提取');

$sql = "INSERT INTO ".$DB->table('articles')." 
        (user_id, cate_id, art_title, art_tags, art_intro, art_content, art_status, art_ctime, art_utime) 
        VALUES 
        (1, 319, '$title', 'JSON,测试,DJ', '$intro', '$content', 3, ".time().", ".time().")";

if ($DB->query($sql)) {
    $art_id = $DB->insert_id();
    echo "<h3>2. 测试文章创建成功 (ID: $art_id)</h3>\n";
    
    // 测试get_music_links函数
    echo "<h3>3. get_music_links 函数结果</h3>\n";
    $music_links = get_music_links(319, 20);
    
    $found = false;
    foreach ($music_links as $link) {
        if ($link['art_id'] == $art_id) {
            $found = true;
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>\n";
            echo "<p><strong>最终显示标题:</strong> " . htmlspecialchars($link['title']) . "</p>\n";
            echo "<p><strong>提取的标题:</strong> " . htmlspecialchars($link['extracted_title'] ?? '无') . "</p>\n";
            echo "<p><strong>文章标题:</strong> " . htmlspecialchars($link['original_title']) . "</p>\n";
            echo "<p><strong>URL:</strong> " . htmlspecialchars($link['url']) . "</p>\n";
            echo "</div>\n";
        }
    }
    
    if (!$found) {
        echo "<p style='color: red;'>没有找到对应的音乐链接</p>\n";
    }
    
} else {
    echo "<p style='color: red;'>创建测试文章失败: " . $DB->error() . "</p>\n";
}

echo "<h3>4. 预期结果</h3>\n";
echo "<p><strong>应该显示:</strong> 20分钟试听经典国语dj串烧，劲爆dj合集，DJ打碟现场 [DJ串烧]</p>\n";
echo "<p><strong>而不是:</strong> JSON标题测试 [DJ串烧]</p>\n";

echo "<h3>5. 测试多个JSON音乐</h3>\n";
$multi_json = '[
    {
        "desc": "第一首歌 - 经典老歌",
        "music": "https://example.com/song1.mp3"
    },
    {
        "desc": "第二首歌 - 流行音乐",
        "music": "https://example.com/song2.mp3"
    }
]';

// 删除并创建多音乐测试文章
$DB->query("DELETE FROM ".$DB->table('articles')." WHERE art_title='多JSON音乐测试'");

$multi_title = $DB->escape_string('多JSON音乐测试');
$multi_content = $DB->escape_string($multi_json);

$sql2 = "INSERT INTO ".$DB->table('articles')." 
         (user_id, cate_id, art_title, art_tags, art_intro, art_content, art_status, art_ctime, art_utime) 
         VALUES 
         (1, 319, '$multi_title', 'JSON,测试,多音乐', '测试多个JSON音乐', '$multi_content', 3, ".time().", ".time().")";

if ($DB->query($sql2)) {
    $art_id2 = $DB->insert_id();
    echo "<p style='color: green;'>多音乐测试文章创建成功 (ID: $art_id2)</p>\n";
    
    $music_links2 = get_music_links(319, 20);
    foreach ($music_links2 as $link) {
        if ($link['art_id'] == $art_id2) {
            echo "<p><strong>标题:</strong> " . htmlspecialchars($link['title']) . "</p>\n";
        }
    }
}

echo "<h3>6. 测试AJAX接口</h3>\n";
echo "<p><a href='?mod=ajaxget&type=music_list' target='_blank'>点击查看AJAX接口返回的数据</a></p>\n";
?>
