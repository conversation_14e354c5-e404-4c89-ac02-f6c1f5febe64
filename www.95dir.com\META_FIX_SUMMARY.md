# Meta信息获取功能修复总结

## 问题描述
原有的域名获取meta信息功能失效，无法获取到网站的标题、描述、关键词等元数据信息，影响前后端网站提交功能。

## 修复方案
根据您提供的可工作的原始代码，我已经将所有相关文件恢复为原始的简洁版本，确保功能正常工作。

## 修复的文件

### 1. `source/module/webdata.php`
- 恢复了原始的 `get_sitemeta()` 函数
- 使用简洁有效的正则表达式匹配
- 保持原有的逻辑结构

### 2. `module/ajaxget.php`
- 恢复了原始的crawl处理逻辑
- 使用 `attr("value")` 方法设置表单值
- 保持简洁的JavaScript输出

### 3. `system/website.php`
- 恢复了原始的metainfo处理逻辑
- 与ajaxget.php保持一致的实现方式

### 4. `public/scripts/common.js`
- 恢复了原始的 `getmeta()` 函数
- 保持简洁的AJAX调用方式

### 5. `public/scripts/admin.js`
- 恢复了原始的 `GetMeta()` 函数
- 保持与common.js一致的实现方式

## 核心代码

### PHP后端 (get_sitemeta函数)
```php
function get_sitemeta($url) {
    $url = format_url($url);
    $data = get_url_content($url);
    $meta = array();
    if (!empty($data)) {
        // Title
        preg_match('/<TITLE>([\w\W]*?)<\/TITLE>/si', $data, $matches);
        if (!empty($matches[1])) {
            $meta['title'] = $matches[1];
        }

        // Keywords
        preg_match('/<META\s+name="keywords"\s+content="([\w\W]*?)"/si', $data, $matches);
        // ... 多种格式匹配

        // Description
        preg_match('/<META\s+name="description"\s+content="([\w\W]*?)"/si', $data, $matches);
        // ... 多种格式匹配
    }
    return $meta;
}
```

### JavaScript前端
```javascript
function getmeta() {
    var url = $("#web_url").attr("value");
    if (url == '') {
        alert('请输入网站域名！');
        $("#web_url").focus();
        return false;
    }
    $(document).ready(function(){
        $("#meta_btn").val('正在获取，请稍候...');
        $.ajax({
            type: "GET",
            url: sitepath + '?mod=ajaxget&type=crawl',
            data: 'url=' + url,
            datatype: "script",
            cache: false,
            success: function(data){
                $("body").append(data);
                $("#meta_btn").val('重新获取');
            }
        });
    });
}
```

### AJAX响应
```javascript
$("#web_name").attr("value", "网站标题");
$("#web_tags").attr("value", "关键词");
$("#web_intro").attr("value", "网站描述");
```

## 测试方法

### 1. 通过管理后台测试
1. 访问 `/system/` 管理后台
2. 进入"网站管理" -> "添加网站"
3. 输入域名如 `baidu.com`
4. 点击"抓取Meta"按钮

### 2. 通过会员中心测试
1. 访问 `/member/` 会员中心
2. 进入"网站管理" -> "提交网站"
3. 输入域名如 `baidu.com`
4. 点击"抓取Meta"按钮

### 3. 直接测试AJAX接口
在浏览器访问：
```
http://您的域名/?mod=ajaxget&type=crawl&url=baidu.com
```

## 预期结果
- 网站名称字段自动填充网站标题
- TAG标签字段自动填充关键词
- 网站简介字段自动填充描述

## 兼容性
- 完全兼容原有系统
- 不影响数据库结构
- 保持原有API接口

## 注意事项
1. 确保服务器有CURL扩展
2. 检查网络连接正常
3. 某些网站可能有反爬虫限制
4. 功能依赖于目标网站的meta标签完整性

现在您的meta信息获取功能应该能够正常工作了！
