<?php
/**
 * 链接检测修复脚本
 * 自动修复常见的链接检测问题
 */

echo "<h1>链接检测修复工具</h1>";

$fixes_applied = [];
$errors = [];

// 1. 确保缓存目录存在
$cache_dir = __DIR__ . '/module/cache';
if (!is_dir($cache_dir)) {
    if (mkdir($cache_dir, 0755, true)) {
        $fixes_applied[] = "创建缓存目录: $cache_dir";
    } else {
        $errors[] = "无法创建缓存目录: $cache_dir";
    }
} else {
    $fixes_applied[] = "缓存目录已存在: $cache_dir";
}

// 2. 设置缓存目录权限
if (is_dir($cache_dir)) {
    if (chmod($cache_dir, 0755)) {
        $fixes_applied[] = "设置缓存目录权限: 755";
    } else {
        $errors[] = "无法设置缓存目录权限";
    }
}

// 3. 清理过期的缓存文件
$cache_files = glob($cache_dir . '/chk_*.json');
$cleaned_count = 0;
$ttl = 86400; // 24小时

foreach ($cache_files as $file) {
    if (file_exists($file) && (time() - filemtime($file)) > $ttl) {
        if (unlink($file)) {
            $cleaned_count++;
        }
    }
}

if ($cleaned_count > 0) {
    $fixes_applied[] = "清理了 $cleaned_count 个过期缓存文件";
}

// 4. 测试 cURL 功能
if (extension_loaded('curl')) {
    $fixes_applied[] = "cURL 扩展已加载";
    
    // 测试基本的 cURL 功能
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => 'https://www.baidu.com',
        CURLOPT_NOBODY => true,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CONNECTTIMEOUT => 5,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSL_VERIFYPEER => false
    ]);
    
    $result = curl_exec($ch);
    if ($result !== false) {
        $fixes_applied[] = "cURL 网络连接测试成功";
    } else {
        $errors[] = "cURL 网络连接测试失败: " . curl_error($ch);
    }
    curl_close($ch);
} else {
    $errors[] = "cURL 扩展未加载，请联系管理员安装";
}

// 5. 检查并修复 status_check.php 文件
$status_check_file = __DIR__ . '/module/status_check.php';
if (file_exists($status_check_file)) {
    $fixes_applied[] = "status_check.php 文件存在";
    
    // 检查文件权限
    if (is_readable($status_check_file)) {
        $fixes_applied[] = "status_check.php 文件可读";
    } else {
        $errors[] = "status_check.php 文件不可读";
    }
} else {
    $errors[] = "status_check.php 文件不存在";
}

// 6. 创建测试缓存文件
$test_cache_file = $cache_dir . '/test_' . time() . '.json';
$test_data = json_encode(['test' => true, 'timestamp' => time()]);
if (file_put_contents($test_cache_file, $test_data)) {
    $fixes_applied[] = "缓存写入测试成功";
    unlink($test_cache_file); // 清理测试文件
} else {
    $errors[] = "缓存写入测试失败";
}

// 7. 检查 .htaccess 重写规则
$htaccess_file = __DIR__ . '/.htaccess';
if (file_exists($htaccess_file)) {
    $htaccess_content = file_get_contents($htaccess_file);
    if (strpos($htaccess_content, 'RewriteEngine On') !== false) {
        $fixes_applied[] = ".htaccess 重写规则已启用";
    } else {
        $errors[] = ".htaccess 重写规则未启用";
    }
} else {
    $errors[] = ".htaccess 文件不存在";
}

// 显示结果
echo "<h2>修复结果</h2>";

if (!empty($fixes_applied)) {
    echo "<h3 style='color: green;'>已应用的修复:</h3>";
    echo "<ul>";
    foreach ($fixes_applied as $fix) {
        echo "<li style='color: green;'>✓ $fix</li>";
    }
    echo "</ul>";
}

if (!empty($errors)) {
    echo "<h3 style='color: red;'>发现的问题:</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>✗ $error</li>";
    }
    echo "</ul>";
}

// 8. 提供手动修复建议
echo "<h2>手动修复建议</h2>";
echo "<ol>";
echo "<li><strong>如果 cURL 扩展未加载:</strong><br>";
echo "   - 联系服务器管理员安装 php-curl 扩展<br>";
echo "   - 或在 php.ini 中启用 extension=curl</li>";

echo "<li><strong>如果缓存目录权限问题:</strong><br>";
echo "   - 在服务器上运行: chmod 755 module/cache<br>";
echo "   - 确保 Web 服务器用户有写入权限</li>";

echo "<li><strong>如果网络连接问题:</strong><br>";
echo "   - 检查服务器防火墙设置<br>";
echo "   - 确保服务器可以访问外部网站<br>";
echo "   - 检查 DNS 解析是否正常</li>";

echo "<li><strong>如果 JavaScript 错误:</strong><br>";
echo "   - 打开浏览器开发者工具查看控制台错误<br>";
echo "   - 检查网络面板中的 AJAX 请求状态<br>";
echo "   - 确保 /module/status_check.php 可以正常访问</li>";

echo "<li><strong>测试步骤:</strong><br>";
echo "   - 访问 <a href='debug_link_check.php' target='_blank'>debug_link_check.php</a> 进行详细诊断<br>";
echo "   - 访问 <a href='test_link_check.html' target='_blank'>test_link_check.html</a> 测试前端功能<br>";
echo "   - 直接访问 <a href='module/status_check.php?url=https://www.baidu.com' target='_blank'>status_check.php</a> 测试接口</li>";
echo "</ol>";

echo "<h2>完成修复</h2>";
if (empty($errors)) {
    echo "<p style='color: green; font-weight: bold;'>✓ 所有检查都通过了！链接检测功能应该可以正常工作。</p>";
} else {
    echo "<p style='color: orange; font-weight: bold;'>⚠ 发现了一些问题，请根据上述建议进行手动修复。</p>";
}

echo "<p>修复完成时间: " . date('Y-m-d H:i:s') . "</p>";
?>
