<?php
// quick_prompt_setup.php - 快速提示词设置入口
header('Content-Type: text/html; charset=utf-8');

// 引入系统配置
define('IN_ADMIN', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');
require(APP_PATH.'init.php');
require(APP_PATH.'module/static.php');

// 检查当前AI配置状态
$ai_config_status = array(
    'api_key' => !empty($options['ai_api_key']),
    'api_url' => !empty($options['ai_api_url']),
    'model_name' => !empty($options['ai_model_name']),
    'temperature' => !empty($options['ai_temperature']),
    'prompt_template' => !empty($options['ai_prompt_template'])
);

$config_complete = array_sum($ai_config_status);
$config_total = count($ai_config_status);
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>AI功能快速设置</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #333; margin-bottom: 10px; }
        .status-card { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .status-item { display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #eee; }
        .status-item:last-child { border-bottom: none; }
        .status-badge { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; }
        .status-ok { background: #d4edda; color: #155724; }
        .status-missing { background: #f8d7da; color: #721c24; }
        .progress-bar { background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden; margin: 15px 0; }
        .progress-fill { background: linear-gradient(90deg, #28a745, #20c997); height: 100%; transition: width 0.3s ease; }
        .action-buttons { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 30px; }
        .btn { padding: 15px 20px; border: none; border-radius: 6px; font-size: 16px; font-weight: bold; text-decoration: none; text-align: center; cursor: pointer; transition: all 0.3s ease; }
        .btn-primary { background: #007cba; color: white; }
        .btn-primary:hover { background: #005a8b; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-secondary:hover { background: #545b62; }
        .tips { background: #e7f3ff; padding: 15px; border-radius: 6px; margin-top: 20px; }
        .tips h4 { margin-top: 0; color: #0066cc; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AI功能快速设置</h1>
            <p>一站式配置您的AI生成简介功能</p>
        </div>

        <div class="status-card">
            <h3>📊 当前配置状态</h3>
            <div class="progress-bar">
                <div class="progress-fill" style="width: <?php echo ($config_complete / $config_total) * 100; ?>%"></div>
            </div>
            <p style="text-align: center; margin: 10px 0;">
                配置完成度：<?php echo $config_complete; ?>/<?php echo $config_total; ?> 
                (<?php echo round(($config_complete / $config_total) * 100); ?>%)
            </p>

            <div class="status-item">
                <span>API密钥配置</span>
                <span class="status-badge <?php echo $ai_config_status['api_key'] ? 'status-ok' : 'status-missing'; ?>">
                    <?php echo $ai_config_status['api_key'] ? '✅ 已配置' : '❌ 未配置'; ?>
                </span>
            </div>
            <div class="status-item">
                <span>API地址配置</span>
                <span class="status-badge <?php echo $ai_config_status['api_url'] ? 'status-ok' : 'status-missing'; ?>">
                    <?php echo $ai_config_status['api_url'] ? '✅ 已配置' : '❌ 未配置'; ?>
                </span>
            </div>
            <div class="status-item">
                <span>模型名称配置</span>
                <span class="status-badge <?php echo $ai_config_status['model_name'] ? 'status-ok' : 'status-missing'; ?>">
                    <?php echo $ai_config_status['model_name'] ? '✅ 已配置' : '❌ 未配置'; ?>
                </span>
            </div>
            <div class="status-item">
                <span>温度参数配置</span>
                <span class="status-badge <?php echo $ai_config_status['temperature'] ? 'status-ok' : 'status-missing'; ?>">
                    <?php echo $ai_config_status['temperature'] ? '✅ 已配置' : '❌ 未配置'; ?>
                </span>
            </div>
            <div class="status-item">
                <span>提示词模板配置</span>
                <span class="status-badge <?php echo $ai_config_status['prompt_template'] ? 'status-ok' : 'status-missing'; ?>">
                    <?php echo $ai_config_status['prompt_template'] ? '✅ 已配置' : '❌ 未配置'; ?>
                </span>
            </div>
        </div>

        <div class="action-buttons">
            <a href="ai_prompt_manager.php" class="btn btn-primary">
                🎯 选择提示词模板
            </a>
            <a href="option.php?opt=misc" class="btn btn-secondary">
                ⚙️ 基础参数配置
            </a>
        </div>

        <div class="tips">
            <h4>💡 配置建议</h4>
            <ol>
                <li><strong>首次使用：</strong>建议先点击"基础参数配置"设置API密钥等基础信息</li>
                <li><strong>选择模板：</strong>然后使用"选择提示词模板"为您的网站类型选择合适的提示词</li>
                <li><strong>测试功能：</strong>配置完成后可在网站编辑页面测试AI生成功能</li>
                <li><strong>自定义调整：</strong>可随时返回调整配置或更换提示词模板</li>
            </ol>
        </div>

        <?php if ($config_complete == $config_total): ?>
            <div style="background: #d4edda; color: #155724; padding: 15px; border-radius: 6px; margin-top: 20px; text-align: center;">
                <h4 style="margin: 0;">🎉 配置完成！</h4>
                <p style="margin: 10px 0 0 0;">您的AI生成功能已完全配置，可以正常使用了！</p>
            </div>
        <?php elseif ($config_complete > 0): ?>
            <div style="background: #fff3cd; color: #856404; padding: 15px; border-radius: 6px; margin-top: 20px; text-align: center;">
                <h4 style="margin: 0;">⚠️ 配置未完成</h4>
                <p style="margin: 10px 0 0 0;">还有 <?php echo $config_total - $config_complete; ?> 项配置需要完善，请继续配置以正常使用AI功能。</p>
            </div>
        <?php else: ?>
            <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 6px; margin-top: 20px; text-align: center;">
                <h4 style="margin: 0;">🚨 尚未配置</h4>
                <p style="margin: 10px 0 0 0;">AI功能尚未配置，请先进行基础参数配置。</p>
            </div>
        <?php endif; ?>

        <div style="text-align: center; margin-top: 30px;">
            <a href="main.php" style="color: #007cba; text-decoration: none;">
                ← 返回管理首页
            </a>
        </div>
    </div>
</body>
</html>
