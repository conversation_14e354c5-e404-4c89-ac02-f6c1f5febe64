/* VIP专用样式文件 - 优化版本 */

/* 全局VIP样式变量 */
:root {
    --vip-gold: #ffd700;
    --vip-gold-light: #ffed4e;
    --vip-gold-dark: #e6c200;
    --vip-orange: #ff6b35;
    --vip-orange-light: #ff8c42;
    --vip-shadow-gold: rgba(255, 215, 0, 0.3);
    --vip-shadow-dark: rgba(0, 0, 0, 0.15);
    --vip-text-dark: #2c3e50;
    --vip-text-light: #ffffff;
}

/* VIP头部样式 - 增强版 */
.vip-header {
    background: linear-gradient(135deg, var(--vip-gold) 0%, var(--vip-gold-light) 30%, var(--vip-gold) 70%, var(--vip-gold-dark) 100%);
    color: var(--vip-text-dark);
    padding: 30px 25px;
    border-radius: 20px;
    margin-bottom: 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow:
        0 15px 35px var(--vip-shadow-gold),
        0 5px 15px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border: 2px solid var(--vip-gold-light);
}

.vip-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 20px,
        rgba(255, 255, 255, 0.08) 20px,
        rgba(255, 255, 255, 0.08) 40px
    );
    animation: shimmer 6s linear infinite;
    pointer-events: none;
}

.vip-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    pointer-events: none;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* VIP皇冠动画 - 增强版 */
.vip-crown {
    font-size: 56px;
    margin-bottom: 18px;
    text-shadow:
        3px 3px 6px rgba(0,0,0,0.3),
        0 0 20px rgba(255, 215, 0, 0.5);
    animation: bounce 3s ease-in-out infinite;
    display: inline-block;
    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
    z-index: 10;
    position: relative;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0) scale(1) rotate(0deg);
    }
    40% {
        transform: translateY(-15px) scale(1.15) rotate(-2deg);
    }
    60% {
        transform: translateY(-8px) scale(1.08) rotate(1deg);
    }
}

/* VIP标题样式 - 增强版 */
.vip-title {
    font-size: 28px;
    font-weight: 700;
    margin: 0;
    text-shadow:
        2px 2px 4px rgba(0,0,0,0.3),
        0 0 10px rgba(255, 215, 0, 0.3);
    z-index: 10;
    position: relative;
    color: var(--vip-text-dark);
    letter-spacing: 0.5px;
    line-height: 1.2;
}

.vip-subtitle {
    font-size: 16px;
    margin: 12px 0 0 0;
    opacity: 0.85;
    z-index: 10;
    position: relative;
    font-weight: 500;
    color: var(--vip-text-dark);
    letter-spacing: 0.3px;
}

/* VIP徽章样式 - 增强版 */
.vip-badge {
    display: inline-block;
    background: linear-gradient(45deg, var(--vip-orange) 0%, var(--vip-orange-light) 50%, var(--vip-orange) 100%);
    color: var(--vip-text-light);
    padding: 12px 24px;
    border-radius: 35px;
    font-size: 14px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    box-shadow:
        0 8px 25px rgba(255, 107, 53, 0.4),
        0 3px 10px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    animation: glow 3s ease-in-out infinite alternate;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.vip-badge:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow:
        0 12px 35px rgba(255, 107, 53, 0.6),
        0 5px 15px rgba(0, 0, 0, 0.3);
}

.vip-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: slide 4s infinite;
}

.vip-badge::after {
    content: '✨';
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 12px;
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes slide {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

@keyframes sparkle {
    0%, 100% { opacity: 0.5; transform: scale(0.8) rotate(0deg); }
    50% { opacity: 1; transform: scale(1.2) rotate(180deg); }
}

@keyframes glow {
    from {
        box-shadow:
            0 8px 25px rgba(255, 107, 53, 0.4),
            0 3px 10px rgba(0, 0, 0, 0.2);
        transform: scale(1);
    }
    to {
        box-shadow:
            0 12px 35px rgba(255, 107, 53, 0.6),
            0 5px 15px rgba(0, 0, 0, 0.3);
        transform: scale(1.02);
    }
}

/* VIP内容容器 - 增强版 */
.vip-content {
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 50%, #f8f9fa 100%);
    border: 3px solid var(--vip-gold);
    border-radius: 25px;
    padding: 35px;
    margin-bottom: 30px;
    box-shadow:
        0 20px 50px rgba(0,0,0,0.08),
        0 8px 25px var(--vip-shadow-gold),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    position: relative;
    transition: all 0.3s ease;
}

.vip-content:hover {
    transform: translateY(-3px);
    box-shadow:
        0 25px 60px rgba(0,0,0,0.12),
        0 12px 35px var(--vip-shadow-gold);
}

.vip-content::before {
    content: '👑';
    position: absolute;
    top: -18px;
    right: 30px;
    font-size: 32px;
    background: linear-gradient(45deg, var(--vip-gold) 0%, var(--vip-gold-light) 50%, var(--vip-gold) 100%);
    padding: 10px 14px;
    border-radius: 50%;
    box-shadow:
        0 8px 20px rgba(0,0,0,0.15),
        0 3px 10px var(--vip-shadow-gold);
    animation: rotate 6s linear infinite;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.vip-content::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--vip-gold) 0%, var(--vip-gold-light) 50%, var(--vip-gold) 100%);
    border-radius: 25px 25px 0 0;
}

@keyframes rotate {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
    100% { transform: rotate(360deg) scale(1); }
}

/* VIP统计数据网格 - 增强版 */
.vip-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 25px;
    margin: 30px 0;
}

.vip-stat-item {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
    color: var(--vip-text-light);
    padding: 25px 20px;
    border-radius: 20px;
    text-align: center;
    box-shadow:
        0 10px 30px rgba(102, 126, 234, 0.3),
        0 4px 15px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.vip-stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.15), transparent);
    transform: translateX(-100%);
    transition: transform 0.8s ease;
}

.vip-stat-item:hover::before {
    transform: translateX(100%);
}

.vip-stat-item:hover {
    transform: translateY(-10px) scale(1.08);
    box-shadow:
        0 20px 50px rgba(102, 126, 234, 0.5),
        0 8px 25px rgba(0, 0, 0, 0.2);
}

.vip-stat-item:nth-child(odd) {
    background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 50%, #ff6b35 100%);
}

.vip-stat-item:nth-child(odd):hover {
    box-shadow:
        0 20px 50px rgba(255, 107, 53, 0.5),
        0 8px 25px rgba(0, 0, 0, 0.2);
}

.vip-stat-number {
    font-size: 32px;
    font-weight: 700;
    display: block;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    line-height: 1;
}

.vip-stat-label {
    font-size: 12px;
    opacity: 0.95;
    margin-top: 5px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* VIP访问按钮 - 增强版 */
.vip-access-button {
    background: linear-gradient(45deg, #28a745 0%, #20c997 50%, #17a2b8 100%);
    color: var(--vip-text-light);
    padding: 20px 40px;
    border: none;
    border-radius: 50px;
    font-size: 16px;
    font-weight: 700;
    text-decoration: none;
    display: inline-block;
    box-shadow:
        0 10px 30px rgba(40, 167, 69, 0.4),
        0 4px 15px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    text-transform: uppercase;
    letter-spacing: 1.2px;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.vip-access-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.6s ease;
}

.vip-access-button::after {
    content: '🚀';
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    font-size: 14px;
    transition: all 0.3s ease;
}

.vip-access-button:hover::before {
    left: 100%;
}

.vip-access-button:hover::after {
    transform: translateY(-50%) translateX(5px);
}

.vip-access-button:hover {
    transform: translateY(-5px) scale(1.08);
    box-shadow:
        0 15px 45px rgba(40, 167, 69, 0.6),
        0 8px 25px rgba(0, 0, 0, 0.2);
    color: var(--vip-text-light);
    text-decoration: none;
}

.vip-access-button:active {
    transform: translateY(-2px) scale(1.05);
}

/* VIP特权功能区 */
.vip-features {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    margin: 25px 0;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.vip-features h3 {
    color: #ffd700;
    margin-bottom: 20px;
    text-align: center;
    font-size: 20px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.vip-feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.vip-feature-list li {
    padding: 12px 0;
    border-bottom: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
    font-size: 14px;
    line-height: 1.6;
}

.vip-feature-list li:last-child {
    border-bottom: none;
}

.vip-feature-list li:hover {
    padding-left: 10px;
    background: rgba(255,255,255,0.1);
    border-radius: 5px;
}

.vip-feature-list li:before {
    content: '✨';
    margin-right: 12px;
    font-size: 16px;
}

/* 响应式设计 - 增强版 */
@media (max-width: 1200px) {
    .vip-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }

    .vip-list-item {
        padding: 20px;
    }
}

@media (max-width: 992px) {
    .vip-header,
    .vip-list-header {
        padding: 25px 20px;
    }

    .vip-content {
        padding: 25px;
    }

    .vip-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 18px;
    }

    .category-links {
        gap: 8px;
    }

    .category-links a {
        padding: 8px 15px;
        font-size: 12px;
    }
}

@media (max-width: 768px) {
    .vip-header,
    .vip-list-header {
        padding: 20px 15px;
        margin-bottom: 20px;
    }

    .vip-crown {
        font-size: 42px;
        margin-bottom: 12px;
    }

    .vip-title {
        font-size: 22px;
        line-height: 1.3;
    }

    .vip-subtitle {
        font-size: 14px;
        margin-top: 8px;
    }

    .vip-content {
        padding: 20px;
        border-radius: 20px;
    }

    .vip-content::before {
        font-size: 28px;
        top: -15px;
        right: 20px;
    }

    .vip-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        margin: 20px 0;
    }

    .vip-stat-item {
        padding: 18px 15px;
        border-radius: 15px;
    }

    .vip-stat-number {
        font-size: 24px;
    }

    .vip-stat-label {
        font-size: 11px;
    }

    .vip-access-button {
        padding: 16px 28px;
        font-size: 14px;
        letter-spacing: 1px;
    }

    .vip-badge {
        padding: 10px 18px;
        font-size: 12px;
        letter-spacing: 1px;
    }

    .vip-list-item {
        padding: 18px;
        margin-bottom: 20px;
    }

    .vip-list-item::before {
        font-size: 24px;
        top: -10px;
        right: 20px;
    }

    .vip-category-filter {
        padding: 20px;
    }

    .category-links {
        gap: 6px;
    }

    .category-links a {
        padding: 6px 12px;
        font-size: 11px;
    }

    .vip-stats-bar {
        padding: 15px 20px;
        font-size: 14px;
    }

    .pagination a,
    .pagination .current {
        padding: 10px 14px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .vip-header,
    .vip-list-header {
        padding: 15px 12px;
    }

    .vip-crown {
        font-size: 36px;
    }

    .vip-title {
        font-size: 18px;
    }

    .vip-subtitle {
        font-size: 13px;
    }

    .vip-content {
        padding: 15px;
    }

    .vip-stats {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .vip-stat-item {
        padding: 15px;
    }

    .vip-stat-number {
        font-size: 20px;
    }

    .vip-access-button {
        padding: 14px 24px;
        font-size: 13px;
        width: 100%;
        max-width: 280px;
    }

    .vip-list-item {
        padding: 15px;
    }

    .category-links {
        justify-content: flex-start;
    }

    .category-links a {
        padding: 5px 10px;
        font-size: 10px;
    }

    .pagination {
        flex-wrap: wrap;
        gap: 5px;
    }

    .pagination a,
    .pagination .current {
        padding: 8px 12px;
        font-size: 12px;
    }
}

/* VIP列表页专用样式 */
.vip-list-header {
    background: linear-gradient(135deg, var(--vip-gold) 0%, var(--vip-gold-light) 30%, var(--vip-gold) 70%, var(--vip-gold-dark) 100%);
    color: var(--vip-text-dark);
    padding: 35px 30px;
    border-radius: 25px;
    margin-bottom: 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow:
        0 20px 50px var(--vip-shadow-gold),
        0 8px 25px rgba(0, 0, 0, 0.1),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
    border: 2px solid var(--vip-gold-light);
}

.vip-list-item {
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border: 2px solid var(--vip-gold);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow:
        0 12px 35px rgba(0,0,0,0.08),
        0 5px 20px var(--vip-shadow-gold);
    position: relative;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
}

.vip-list-item::before {
    content: '👑';
    position: absolute;
    top: -12px;
    right: 25px;
    font-size: 28px;
    background: linear-gradient(45deg, var(--vip-gold), var(--vip-gold-light));
    padding: 8px 12px;
    border-radius: 50%;
    box-shadow: 0 6px 15px rgba(0,0,0,0.15);
    z-index: 2;
}

.vip-list-item::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--vip-gold) 0%, var(--vip-gold-light) 50%, var(--vip-gold) 100%);
    border-radius: 20px 20px 0 0;
}

.vip-list-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 50px rgba(0,0,0,0.12),
        0 10px 30px var(--vip-shadow-gold);
    border-color: var(--vip-gold-dark);
}

.vip-category-filter {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--vip-text-light);
    padding: 25px;
    border-radius: 20px;
    margin-bottom: 25px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.vip-category-filter h3 {
    color: var(--vip-gold);
    margin-bottom: 20px;
    text-align: center;
    font-size: 18px;
    font-weight: 700;
}

.category-links {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: center;
}

.category-links a {
    background: rgba(255,255,255,0.15);
    color: var(--vip-text-light);
    padding: 10px 18px;
    border-radius: 25px;
    text-decoration: none;
    font-size: 13px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 1px solid rgba(255,255,255,0.2);
}

.category-links a:hover,
.category-links a.active {
    background: var(--vip-gold);
    color: var(--vip-text-dark);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

.vip-stats-bar {
    background: linear-gradient(45deg, #28a745 0%, #20c997 100%);
    color: var(--vip-text-light);
    padding: 18px 25px;
    border-radius: 15px;
    margin-bottom: 25px;
    text-align: center;
    font-weight: 700;
    font-size: 16px;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 分页样式优化 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 30px 0;
}

.pagination a,
.pagination .current {
    padding: 12px 18px;
    border-radius: 10px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.pagination a {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    border-color: #dee2e6;
}

.pagination a:hover {
    background: linear-gradient(135deg, var(--vip-gold-light) 0%, var(--vip-gold) 100%);
    color: var(--vip-text-dark);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px var(--vip-shadow-gold);
}

.pagination .current {
    background: linear-gradient(135deg, var(--vip-gold) 0%, var(--vip-gold-light) 100%);
    color: var(--vip-text-dark);
    border-color: var(--vip-gold-dark);
    box-shadow: 0 5px 15px var(--vip-shadow-gold);
}

/* VIP特殊效果和装饰 */
.vip-sparkle {
    position: relative;
    display: inline-block;
}

.vip-sparkle::before,
.vip-sparkle::after {
    content: '✨';
    position: absolute;
    font-size: 12px;
    animation: sparkle-float 3s ease-in-out infinite;
    pointer-events: none;
}

.vip-sparkle::before {
    top: -10px;
    left: -15px;
    animation-delay: 0s;
}

.vip-sparkle::after {
    bottom: -10px;
    right: -15px;
    animation-delay: 1.5s;
}

@keyframes sparkle-float {
    0%, 100% {
        opacity: 0.3;
        transform: translateY(0) scale(0.8);
    }
    50% {
        opacity: 1;
        transform: translateY(-5px) scale(1.2);
    }
}

/* VIP渐变文字效果 */
.vip-gradient-text {
    background: linear-gradient(45deg, var(--vip-gold), var(--vip-orange), var(--vip-gold));
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* VIP悬浮卡片效果 */
.vip-floating-card {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* VIP脉冲效果 */
.vip-pulse {
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7);
    }
    50% {
        box-shadow: 0 0 0 20px rgba(255, 215, 0, 0);
    }
}

/* VIP光晕效果 */
.vip-glow {
    position: relative;
}

.vip-glow::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--vip-gold), var(--vip-orange), var(--vip-gold));
    border-radius: inherit;
    z-index: -1;
    filter: blur(8px);
    opacity: 0.7;
    animation: glow-rotate 3s linear infinite;
}

@keyframes glow-rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* VIP星星背景 */
.vip-stars {
    position: relative;
    overflow: hidden;
}

.vip-stars::before {
    content: '⭐';
    position: absolute;
    top: 10%;
    left: 10%;
    font-size: 16px;
    opacity: 0.3;
    animation: twinkle 4s ease-in-out infinite;
}

.vip-stars::after {
    content: '✨';
    position: absolute;
    top: 70%;
    right: 15%;
    font-size: 14px;
    opacity: 0.4;
    animation: twinkle 3s ease-in-out infinite 1s;
}

@keyframes twinkle {
    0%, 100% { opacity: 0.2; transform: scale(0.8); }
    50% { opacity: 0.8; transform: scale(1.2); }
}

/* 侧边栏列表样式优化 */
.weblist_b {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.weblist_b li {
    position: relative;
    padding: 10px 0 !important;
    margin: 0 !important;
    border-bottom: 1px solid #e9ecef;
    line-height: 1.4;
}

.weblist_b li:last-child {
    border-bottom: none;
}

.weblist_b li strong {
    display: block;
    margin: 5px 0;
    font-weight: 600;
    width: auto;
}

.weblist_b li p {
    margin: 5px 0 !important;
    font-size: 12px;
    line-height: 1.3;
    color: #666;
}

.artlist_b {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.artlist_b li {
    position: relative;
    padding: 8px 0 !important;
    margin: 0 !important;
    border-bottom: 1px solid #e9ecef;
    line-height: 1.5;
    font-size: 13px;
}

.artlist_b li:last-child {
    border-bottom: none;
}

.artlist_b li:before {
    display: none !important;
}

/* VIP直达按钮优化 */
.vip-direct-btn {
    background: linear-gradient(45deg, #28a745, #20c997) !important;
    color: white !important;
    padding: 4px 10px !important;
    border-radius: 15px !important;
    font-size: 10px !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    transition: all 0.3s ease !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3) !important;
}

.vip-direct-btn:hover {
    transform: translateY(-1px) scale(1.05) !important;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.5) !important;
    color: white !important;
}

/* 右侧边栏标题优化 */
#mainbox-right h3 {
    font-size: 14px !important;
    font-weight: 700 !important;
    text-align: center !important;
    margin: -1px -1px 15px -1px !important;
    padding: 12px !important;
    border-radius: 8px 8px 0 0 !important;
}

/* 右侧边栏容器优化 */
#mainbox-right .mag,
#mainbox-right #bestart,
#mainbox-right #bestweb {
    margin-bottom: 20px !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

/* 动画性能优化 */
@media (prefers-reduced-motion: reduce) {
    .vip-crown,
    .vip-badge,
    .vip-content::before,
    .vip-list-item,
    .vip-access-button,
    .vip-sparkle::before,
    .vip-sparkle::after,
    .vip-gradient-text,
    .vip-floating-card,
    .vip-pulse,
    .vip-glow::before,
    .vip-stars::before,
    .vip-stars::after,
    .vip-direct-btn {
        animation: none;
        transition: none;
    }

    .vip-stat-item:hover,
    .vip-access-button:hover,
    .vip-list-item:hover,
    .vip-direct-btn:hover {
        transform: none;
    }
}
