<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>95目录网 - 美化页脚预览</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="themes/default/skin/footer-modern.css" rel="stylesheet" type="text/css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
        }
        .preview-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            min-height: 80vh;
        }
        .preview-header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-bottom: 40px;
        }
        .preview-content {
            padding: 40px 20px;
            text-align: center;
        }
        .preview-content h2 {
            color: #333;
            margin-bottom: 20px;
        }
        .preview-content p {
            color: #666;
            line-height: 1.6;
            max-width: 600px;
            margin: 0 auto 40px;
        }
    </style>
</head>
<body>
    <div class="preview-header">
        <h1>95目录网页脚美化预览</h1>
        <p>现代化、响应式的页脚设计</p>
    </div>

    <div class="preview-container">
        <div class="preview-content">
            <h2>页脚美化完成</h2>
            <p>我们为95目录网设计了一个全新的现代化页脚，包含以下特色：</p>
            <ul style="text-align: left; max-width: 500px; margin: 0 auto;">
                <li>渐变背景和现代化设计</li>
                <li>响应式布局，适配各种设备</li>
                <li>优化的导航链接分组</li>
                <li>美化的统计信息显示</li>
                <li>动画效果和交互体验</li>
                <li>清晰的版权信息区域</li>
            </ul>
            <p>请滚动到页面底部查看美化后的页脚效果。</p>
        </div>
    </div>

    <!-- 美化的页脚开始 -->
    <footer id="footer" class="modern-footer">
        <!-- 域名评级徽章 -->
        <div class="footer-badge">
            <a href="https://frogdr.com/95dir.com?utm_source=95dir.com" target="_blank" class="badge-link">
                <img src="https://frogdr.com/95dir.com/badge-white.svg" 
                     alt="Monitor your Domain Rating with FrogDR" 
                     width="250" height="54" 
                     class="rating-badge">
            </a>
        </div>

        <!-- 主要内容区域 -->
        <div class="footer-content">
            <!-- 导航链接区域 -->
            <div class="footer-nav">
                <div class="nav-section">
                    <h4>网站导航</h4>
                    <div class="nav-links">
                        <a href="#" title="网站首页">网站首页</a>
                        <a href="#" title="最新收录网站">最新收录</a>
                        <a href="#" title="网站数据归档">数据归档</a>
                        <a href="#" title="热门网站排行榜">TOP排行榜</a>
                    </div>
                </div>
                
                <div class="nav-section">
                    <h4>服务功能</h4>
                    <div class="nav-links">
                        <a href="#" title="违规网站黑名单">黑名单</a>
                        <a href="#" title="网站数据统计">数据公示</a>
                        <a href="#" title="网站地图">站点地图</a>
                        <a href="#" title="网站目录分类浏览">网站目录</a>
                        <a href="#" title="VIP优质网站推荐">VIP网站</a>
                        <a href="#" title="站长资讯文章">站长资讯</a>
                    </div>
                </div>

                <div class="nav-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <div class="qq-group">
                            <a href="#" target="_blank" class="qq-link">
                                <span class="qq-icon">🐧</span>
                                <span>QQ群：868850216</span>
                            </a>
                        </div>
                        <div class="stats-info">
                            <div class="stat-item">
                                <span class="stat-label">当前在线：</span>
                                <span id="onlineCount" class="stat-value">10</span>
                                <span class="stat-unit">人</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">总访客：</span>
                                <span id="totalVisitors" class="stat-value">137,102</span>
                                <span class="stat-unit">人</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 网站描述区域 -->
            <div class="footer-description">
                <div class="site-intro">
                    <h3>95目录网</h3>
                    <p>专业的网站分类目录平台，致力于为用户提供优质的网站收录服务。我们精心收录各行业优质网站，为用户提供便捷的网站发现和访问服务。</p>
                </div>
                <div class="keywords">
                    <span class="keywords-label">关键词：</span>
                    <span class="keywords-list">网站目录、网站收录、分类目录、网站推荐、优质网站、VIP网站、站长资讯、网站分类、网站大全、免费收录</span>
                </div>
            </div>
        </div>

        <!-- 版权信息区域 -->
        <div class="footer-bottom">
            <div class="copyright-info">
                <div class="copyright-text">
                    Copyright © 2025 95dir.com All Rights Reserved | 
                    <a href="https://beian.miit.gov.cn/#/Integrated/index" rel="nofollow" target="_blank" class="icp-link">鄂ICP备2024062716号-1</a>
                </div>
                <div class="runtime-info">
                    Processed in 0.424624 second(s), 738 Queries, Gzip Enabled
                </div>
            </div>
        </div>
    </footer>

    <script>
        // 模拟在线统计更新
        function updateStats() {
            const online = Math.floor(Math.random() * 20) + 5;
            const total = 137102 + Math.floor(Math.random() * 100);
            
            document.getElementById('onlineCount').textContent = online;
            document.getElementById('totalVisitors').textContent = total.toLocaleString();
        }
        
        // 每5秒更新一次统计数据（仅用于演示）
        setInterval(updateStats, 5000);
    </script>
</body>
</html>
