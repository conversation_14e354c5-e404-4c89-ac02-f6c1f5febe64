<?php
/*
 * 违规站点检查功能
 * 定期检查网站内容，发现违规站点自动隐藏
 */

if (!defined('IN_ADMIN') && !defined('IN_IWEBDIR')) {
    exit('Access Denied');
}

// 违规关键词配置
$violation_keywords = array(
    // 色情相关
    '色情', '成人', '裸体', '性爱', '黄色', '三级', 'porn', 'sex', 'adult', 'nude',
    // 赌博相关  
    '赌博', '博彩', '彩票', '老虎机', '百家乐', '德州扑克', 'casino', 'gambling', 'bet',
    // 违法相关
    '毒品', '枪支', '炸药', '假证', '代开发票', '洗钱', '诈骗',
    // 政治敏感
    '法轮功', '六四', '天安门', '反政府',
    // 其他违规
    '病毒', '木马', '钓鱼', '欺诈', '传销', '高利贷'
);

// 违规检查主函数
function check_website_violations() {
    global $DB, $violation_keywords;
    
    $results = array(
        'total_checked' => 0,
        'violations_found' => 0,
        'errors' => 0,
        'details' => array()
    );
    
    // 获取需要检查的网站（状态为3的正常网站）
    $sql = "SELECT web_id, web_name, web_url, web_intro FROM ".$DB->table('websites')." 
            WHERE web_status=3 AND web_violation_status!=1 
            ORDER BY web_utime ASC LIMIT 50"; // 每次检查50个网站
    
    $websites = $DB->fetch_all($sql);
    
    foreach ($websites as $website) {
        $results['total_checked']++;
        
        $violation_result = check_single_website($website, $violation_keywords);
        
        if ($violation_result['is_violation']) {
            $results['violations_found']++;
            
            // 标记为违规网站
            mark_website_violation($website['web_id'], $violation_result['reason']);
            
            $results['details'][] = array(
                'web_id' => $website['web_id'],
                'web_name' => $website['web_name'],
                'web_url' => $website['web_url'],
                'reason' => $violation_result['reason']
            );
        } else {
            // 更新检查时间
            update_website_check_time($website['web_id']);
        }
        
        // 避免请求过于频繁
        usleep(500000); // 0.5秒延迟
    }
    
    return $results;
}

// 检查单个网站
function check_single_website($website, $keywords) {
    $result = array(
        'is_violation' => false,
        'reason' => ''
    );
    
    // 1. 检查网站描述中的关键词
    $intro_check = check_content_keywords($website['web_intro'], $keywords);
    if ($intro_check['found']) {
        $result['is_violation'] = true;
        $result['reason'] = '网站描述包含违规关键词: ' . $intro_check['keyword'];
        return $result;
    }
    
    // 2. 检查网站标题和内容
    $content_check = fetch_and_check_website_content($website['web_url'], $keywords);
    if ($content_check['is_violation']) {
        $result['is_violation'] = true;
        $result['reason'] = $content_check['reason'];
        return $result;
    }
    
    // 3. 检查网站可访问性
    $access_check = check_website_accessibility($website['web_url']);
    if (!$access_check['accessible']) {
        $result['is_violation'] = true;
        $result['reason'] = '网站无法访问: ' . $access_check['reason'];
        return $result;
    }
    
    return $result;
}

// 检查内容中的关键词
function check_content_keywords($content, $keywords) {
    $result = array('found' => false, 'keyword' => '');
    
    if (empty($content)) {
        return $result;
    }
    
    $content = strtolower($content);
    
    foreach ($keywords as $keyword) {
        if (strpos($content, strtolower($keyword)) !== false) {
            $result['found'] = true;
            $result['keyword'] = $keyword;
            break;
        }
    }
    
    return $result;
}

// 获取并检查网站内容
function fetch_and_check_website_content($url, $keywords) {
    $result = array(
        'is_violation' => false,
        'reason' => ''
    );
    
    // 确保URL格式正确
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = 'http://' . $url;
    }
    
    // 设置User-Agent模拟正常浏览器
    $context = stream_context_create(array(
        'http' => array(
            'method' => 'GET',
            'header' => array(
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language: zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3'
            ),
            'timeout' => 10,
            'follow_location' => true,
            'max_redirects' => 3
        )
    ));
    
    try {
        $content = @file_get_contents($url, false, $context);
        
        if ($content === false) {
            return $result; // 无法获取内容，不算违规
        }
        
        // 提取title标签内容
        if (preg_match('/<title[^>]*>(.*?)<\/title>/is', $content, $matches)) {
            $title = strip_tags($matches[1]);
            $keyword_check = check_content_keywords($title, $keywords);
            if ($keyword_check['found']) {
                $result['is_violation'] = true;
                $result['reason'] = '网站标题包含违规关键词: ' . $keyword_check['keyword'];
                return $result;
            }
        }
        
        // 提取meta description
        if (preg_match('/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $content, $matches)) {
            $description = strip_tags($matches[1]);
            $keyword_check = check_content_keywords($description, $keywords);
            if ($keyword_check['found']) {
                $result['is_violation'] = true;
                $result['reason'] = '网站描述包含违规关键词: ' . $keyword_check['keyword'];
                return $result;
            }
        }
        
        // 检查页面主要文本内容（简单提取）
        $text_content = strip_tags($content);
        $text_content = preg_replace('/\s+/', ' ', $text_content);
        $text_sample = substr($text_content, 0, 1000); // 只检查前1000字符
        
        $keyword_check = check_content_keywords($text_sample, $keywords);
        if ($keyword_check['found']) {
            $result['is_violation'] = true;
            $result['reason'] = '网站内容包含违规关键词: ' . $keyword_check['keyword'];
            return $result;
        }
        
    } catch (Exception $e) {
        // 获取内容失败，不算违规
    }
    
    return $result;
}

// 检查网站可访问性
function check_website_accessibility($url) {
    $result = array(
        'accessible' => false,
        'reason' => ''
    );
    
    // 确保URL格式正确
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = 'http://' . $url;
    }
    
    // 使用curl检查
    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; SiteChecker/1.0)');
        curl_setopt($ch, CURLOPT_NOBODY, true); // 只获取头部
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            $result['reason'] = 'CURL错误: ' . $error;
        } elseif ($http_code >= 200 && $http_code < 400) {
            $result['accessible'] = true;
        } else {
            $result['reason'] = 'HTTP状态码: ' . $http_code;
        }
    } else {
        // 备用方案：使用get_headers
        $headers = @get_headers($url, 1);
        if ($headers && strpos($headers[0], '200') !== false) {
            $result['accessible'] = true;
        } else {
            $result['reason'] = '无法获取响应头';
        }
    }
    
    return $result;
}

// 标记网站为违规
function mark_website_violation($web_id, $reason) {
    global $DB;
    
    $data = array(
        'web_violation_status' => 1,
        'web_violation_reason' => $reason,
        'web_violation_time' => date('Y-m-d H:i:s')
    );
    
    $where = array('web_id' => $web_id);
    $DB->update($DB->table('websites'), $data, $where);
    
    // 记录违规日志
    log_violation($web_id, $reason);
}

// 更新网站检查时间
function update_website_check_time($web_id) {
    global $DB;
    
    $data = array('web_check_time' => date('Y-m-d H:i:s'));
    $where = array('web_id' => $web_id);
    $DB->update($DB->table('websites'), $data, $where);
}

// 记录违规日志
function log_violation($web_id, $reason) {
    global $DB;
    
    $log_data = array(
        'web_id' => $web_id,
        'violation_reason' => $reason,
        'check_time' => date('Y-m-d H:i:s'),
        'check_ip' => $_SERVER['SERVER_ADDR']
    );
    
    $DB->insert($DB->table('violation_logs'), $log_data);
}

// 获取违规统计
function get_violation_statistics() {
    global $DB;
    
    $stats = array();
    
    // 总违规网站数
    $stats['total_violations'] = $DB->get_count($DB->table('websites'), 'web_violation_status=1');
    
    // 今日新增违规
    $today = date('Y-m-d');
    $stats['today_violations'] = $DB->get_count($DB->table('websites'), 
        "web_violation_status=1 AND DATE(web_violation_time)='$today'");
    
    // 最近7天违规趋势
    $stats['week_trend'] = array();
    for ($i = 6; $i >= 0; $i--) {
        $date = date('Y-m-d', strtotime("-$i days"));
        $count = $DB->get_count($DB->table('websites'), 
            "web_violation_status=1 AND DATE(web_violation_time)='$date'");
        $stats['week_trend'][$date] = $count;
    }
    
    return $stats;
}

// 恢复违规网站（手动审核后）
function restore_website($web_id) {
    global $DB;
    
    $data = array(
        'web_violation_status' => 0,
        'web_violation_reason' => '',
        'web_violation_time' => null
    );
    
    $where = array('web_id' => $web_id);
    return $DB->update($DB->table('websites'), $data, $where);
}
?>
