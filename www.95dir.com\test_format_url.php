<?php
/**
 * 测试 format_url 函数修复效果
 */

// 引入必要的文件
define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

require(APP_PATH.'include/function.php');

echo "<h1>format_url() 函数测试</h1>";

// 测试用例
$test_urls = [
    'www.baidu.com',
    'baidu.com',
    'http://www.baidu.com',
    'https://www.baidu.com',
    'www.95dir.com/?mod=webdir',
    'www.95dir.com/?mod=siteinfo&wid=481',
    'https://www.95dir.com/?mod=webdir',
    'https://www.95dir.com/?mod=siteinfo&wid=481',
    'example.com/path/to/page',
    'subdomain.example.com',
    'localhost:8080',
    'https://api.example.com/v1/data?param=value',
    '',
    null
];

echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>原始URL</th><th>格式化后的URL</th><th>状态</th></tr>";

foreach ($test_urls as $url) {
    $formatted = format_url($url);
    $status = '';
    
    if (empty($url)) {
        $status = '空值处理';
    } elseif (filter_var($formatted, FILTER_VALIDATE_URL)) {
        $status = '<span style="color: green;">✓ 有效URL</span>';
    } else {
        $status = '<span style="color: red;">✗ 无效URL</span>';
    }
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($url ?? 'null') . "</td>";
    echo "<td>" . htmlspecialchars($formatted) . "</td>";
    echo "<td>$status</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>链接检测测试</h2>";

// 测试几个格式化后的URL是否可以正常检测
$test_formatted_urls = [
    format_url('www.baidu.com'),
    format_url('www.95dir.com/?mod=webdir'),
    format_url('www.95dir.com/?mod=siteinfo&wid=481')
];

echo "<h3>格式化URL的链接检测测试</h3>";
echo "<div id='link-test-results'></div>";

?>

<script>
// 前端测试链接检测
const CHECK_ENDPOINT = '/module/status_check.php?url=';
const testUrls = <?php echo json_encode($test_formatted_urls); ?>;

async function testLinkCheck() {
    const resultsDiv = document.getElementById('link-test-results');
    resultsDiv.innerHTML = '<p>正在测试链接检测...</p>';
    
    let html = '<table border="1" cellpadding="5" cellspacing="0">';
    html += '<tr><th>格式化URL</th><th>检测结果</th><th>状态码</th><th>响应时间</th></tr>';
    
    for (const url of testUrls) {
        try {
            const startTime = Date.now();
            const response = await fetch(CHECK_ENDPOINT + encodeURIComponent(url));
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            if (response.ok) {
                const data = await response.json();
                if (data.error) {
                    html += `<tr><td>${url}</td><td style="color: red;">错误: ${data.error}</td><td>-</td><td>${duration}ms</td></tr>`;
                } else {
                    html += `<tr><td>${url}</td><td style="color: green;">成功</td><td>${data.status}</td><td>${duration}ms</td></tr>`;
                }
            } else {
                html += `<tr><td>${url}</td><td style="color: red;">HTTP ${response.status}</td><td>-</td><td>${duration}ms</td></tr>`;
            }
        } catch (error) {
            html += `<tr><td>${url}</td><td style="color: red;">异常: ${error.message}</td><td>-</td><td>-</td></tr>`;
        }
    }
    
    html += '</table>';
    resultsDiv.innerHTML = html;
}

// 页面加载后自动测试
window.onload = testLinkCheck;
</script>

<h2>修复说明</h2>
<ul>
    <li><strong>问题原因：</strong>原始的 format_url() 函数会强制在URL末尾添加斜杠，导致某些URL格式不正确</li>
    <li><strong>修复方案：</strong>
        <ul>
            <li>如果URL已包含协议（http://或https://），直接返回原URL</li>
            <li>对于没有协议的URL，智能添加http://协议</li>
            <li>不强制添加尾部斜杠，保持URL的原始路径结构</li>
            <li>更好地处理域名、端口和路径的组合</li>
        </ul>
    </li>
    <li><strong>影响范围：</strong>修复了 source/include/function.php、source/module/webdata.php 等文件中的 format_url() 函数</li>
</ul>

<h2>下一步操作</h2>
<ol>
    <li>清除模板缓存：删除 data/compile/ 目录下的编译文件</li>
    <li>测试网站前端的链接检测功能</li>
    <li>如果问题仍然存在，检查数据库中的 web_url 字段数据格式</li>
</ol>

<?php
echo "<p><strong>测试完成时间：</strong>" . date('Y-m-d H:i:s') . "</p>";
?>
