<?php
/*
 * 隐身收录量检测模块
 * 使用最强的反反爬虫技术
 */

if (!defined('IN_IWEBDIR')) exit('Access Denied');

/**
 * 隐身收录量检测类
 */
class StealthIndexChecker {
    
    private $session_cookies = array();
    private $config = array();
    
    public function __construct() {
        $this->config = array(
            'timeout' => 20,
            'connect_timeout' => 8,
            'delay_min' => 1000000, // 1秒
            'delay_max' => 3000000, // 3秒
            'max_retries' => 2,
            'use_proxy' => false,
            'debug' => false
        );
        
        // 初始化会话Cookie存储
        $this->initCookieStorage();
    }
    
    /**
     * 获取单个搜索引擎收录量
     */
    public function getIndexCount($url, $engine = 'baidu') {
        switch ($engine) {
            case 'baidu':
                return $this->getBaiduIndexStealth($url);
            case 'bing':
                return $this->getBingIndexStealth($url);
            case '360':
                return $this->get360IndexStealth($url);
            case 'sogou':
                return $this->getSogouIndexStealth($url);
            default:
                return 0;
        }
    }
    
    /**
     * 百度收录量检测 - 基于真实浏览器参数
     */
    private function getBaiduIndexStealth($url) {
        $clean_url = $this->cleanUrl($url);

        // 第一步：访问百度首页，建立会话
        $this->visitHomepage('https://www.baidu.com/', 'baidu');

        // 第二步：使用真实浏览器参数模拟搜索
        $rsv_pq = $this->generateBaiduPQ();
        $rsv_iqid = $this->generateBaiduIQID();
        $rsv_t = $this->generateBaiduT();

        $search_urls = array(
            // 基于真实浏览器链接1 - 完整参数版
            "https://www.baidu.com/s?wd=site%3A" . urlencode($clean_url) .
            "&rsv_spt=1&rsv_iqid=" . $rsv_iqid . "&issp=1&f=8&rsv_bp=1&rsv_idx=2&ie=utf-8" .
            "&rqlang=cn&tn=baiduhome_pg&rsv_dl=tb&rsv_enter=0" .
            "&oq=site%253A" . urlencode($clean_url) . "&rsv_btype=t&rsv_t=" . $rsv_t .
            "&rsv_pq=" . $rsv_pq . "&prefixsug=site%253A" . urlencode($clean_url) . "&rsp=0",

            // 基于真实浏览器链接2 - 简化版
            "https://www.baidu.com/s?wd=site%3A" . urlencode($clean_url) .
            "&rsv_spt=1&f=8&rsv_bp=1&rsv_idx=2&ie=utf-8&rqlang=cn&tn=baiduhome_pg" .
            "&rsv_pq=" . $rsv_pq . "&rsv_t=" . $rsv_t,

            // 移动端真实参数
            "https://m.baidu.com/s?word=site%3A" . urlencode($clean_url) .
            "&sa=ib&ts=" . time() . "&rn=1&from=1000539d"
        );
        
        foreach ($search_urls as $search_url) {
            // 添加随机延迟
            usleep(rand($this->config['delay_min'], $this->config['delay_max']));
            
            $data = $this->sendStealthRequest($search_url, 'baidu', array(
                'referer' => 'https://www.baidu.com/',
                'upgrade_insecure_requests' => '1',
                'sec_fetch_dest' => 'document',
                'sec_fetch_mode' => 'navigate',
                'sec_fetch_site' => 'same-origin',
                'sec_fetch_user' => '?1'
            ));
            
            if ($this->config['debug']) {
                error_log("百度搜索URL: " . $search_url);
                error_log("返回数据长度: " . strlen($data));
                error_log("是否包含结果: " . (strpos($data, '找到') !== false ? 'Yes' : 'No'));
            }
            
            if (!$data || strlen($data) < 500) {
                continue;
            }
            
            // 检查是否被拦截
            if ($this->isBlockedBaidu($data)) {
                if ($this->config['debug']) {
                    error_log("百度检测到拦截");
                }
                continue;
            }
            
            // 解析结果
            $count = $this->parseBaiduResultAdvanced($data);
            if ($count > 0) {
                return $count;
            }
        }
        
        return 0;
    }
    
    /**
     * 必应收录量检测 - 基于真实浏览器参数
     */
    private function getBingIndexStealth($url) {
        $clean_url = $this->cleanUrl($url);

        // 访问必应首页
        $this->visitHomepage('https://www.bing.com/', 'bing');

        // 使用真实浏览器参数
        $refig = $this->generateBingRefig();
        $rdrig = $this->generateBingRdrig();

        $search_urls = array(
            // 基于真实浏览器链接 - 完整参数版
            "https://www.bing.com/search?q=site%3A" . urlencode($clean_url) .
            "&form=ANNTH1&refig=" . $refig . "&pc=CNNDDB&adppc=EDGEESS&rdr=1&rdrig=" . $rdrig,

            // 简化版真实参数
            "https://www.bing.com/search?q=site%3A" . urlencode($clean_url) .
            "&form=QBRE&pc=CNNDDB&refig=" . $refig,

            // 标准版
            "https://www.bing.com/search?q=site:" . urlencode($clean_url) . "&count=50&first=1&FORM=PERE"
        );
        
        foreach ($search_urls as $search_url) {
            usleep(rand($this->config['delay_min'], $this->config['delay_max']));
            
            $data = $this->sendStealthRequest($search_url, 'bing', array(
                'referer' => 'https://www.bing.com/',
                'upgrade_insecure_requests' => '1',
                'sec_fetch_dest' => 'document',
                'sec_fetch_mode' => 'navigate',
                'sec_fetch_site' => 'same-origin',
                'sec_fetch_user' => '?1'
            ));
            
            if (!$data || strlen($data) < 500) {
                continue;
            }
            
            if ($this->isBlockedBing($data)) {
                continue;
            }
            
            $count = $this->parseBingResultAdvanced($data);
            if ($count > 0) {
                return $count;
            }
        }
        
        return 0;
    }
    
    /**
     * 360收录量检测 - 基于真实浏览器参数
     */
    private function get360IndexStealth($url) {
        $clean_url = $this->cleanUrl($url);

        $this->visitHomepage('https://www.so.com/', '360');

        // 生成真实参数
        $ssid = $this->generate360SSID();
        $cp = $this->generate360CP();

        $search_urls = array(
            // 基于真实浏览器链接 - 完整参数版
            "https://www.so.com/s?ie=utf-8&fr=360sou_newhome&src=home_so.com&ssid=" . $ssid .
            "&sp=ad2&cp=" . $cp . "&nlpv=global_place_d_shyc&q=site%3A" . urlencode($clean_url),

            // 简化版真实参数
            "https://www.so.com/s?ie=utf-8&fr=360sou_newhome&src=home_so.com&q=site%3A" . urlencode($clean_url),

            // 标准版
            "https://www.so.com/s?ie=utf-8&shb=1&src=360sou_newhome&q=site%3A" . urlencode($clean_url)
        );

        foreach ($search_urls as $search_url) {
            usleep(rand($this->config['delay_min'], $this->config['delay_max']));

            $data = $this->sendStealthRequest($search_url, '360', array(
                'referer' => 'https://www.so.com/',
                'upgrade_insecure_requests' => '1'
            ));

            if (!$data || strlen($data) < 500) {
                continue;
            }

            if ($this->isBlocked360($data)) {
                continue;
            }

            $count = $this->parse360ResultAdvanced($data);
            if ($count > 0) {
                return $count;
            }
        }

        return 0;
    }
    
    /**
     * 搜狗收录量检测 - 基于真实浏览器参数
     */
    private function getSogouIndexStealth($url) {
        $clean_url = $this->cleanUrl($url);

        $this->visitHomepage('https://www.sogou.com/', 'sogou');

        // 生成真实参数
        $sessiontime = $this->generateSogouSessionTime();

        $search_urls = array(
            // 基于真实浏览器链接 - 完整参数版
            "https://www.sogou.com/web?query=site%3A" . urlencode($clean_url) .
            "&_asf=www.sogou.com&_ast=&w=01019900&p=40040100&ie=utf8" .
            "&from=index-nologin&s_from=index&sourceid=9_01_03&sessiontime=" . $sessiontime,

            // 简化版真实参数
            "https://www.sogou.com/web?query=site%3A" . urlencode($clean_url) .
            "&_asf=www.sogou.com&w=01019900&p=40040100&ie=utf8&from=index-nologin&s_from=index",

            // 标准版
            "https://www.sogou.com/web?query=site%3A" . urlencode($clean_url) .
            "&_asf=www.sogou.com&_ast=" . time() . "&w=01019900&p=40040100&ie=utf8&from=index&s_from=index"
        );

        foreach ($search_urls as $search_url) {
            usleep(rand($this->config['delay_min'], $this->config['delay_max']));

            $data = $this->sendStealthRequest($search_url, 'sogou', array(
                'referer' => 'https://www.sogou.com/',
                'upgrade_insecure_requests' => '1'
            ));

            if (!$data || strlen($data) < 500) {
                continue;
            }

            if ($this->isBlockedSogou($data)) {
                continue;
            }

            $count = $this->parseSogouResultAdvanced($data);
            if ($count > 0) {
                return $count;
            }
        }

        return 0;
    }
    
    /**
     * 访问首页建立会话
     */
    private function visitHomepage($url, $engine) {
        $data = $this->sendStealthRequest($url, $engine, array(
            'sec_fetch_dest' => 'document',
            'sec_fetch_mode' => 'navigate',
            'sec_fetch_site' => 'none',
            'sec_fetch_user' => '?1'
        ));
        
        // 短暂延迟，模拟用户阅读时间
        usleep(rand(500000, 1500000)); // 0.5-1.5秒
        
        return $data !== false;
    }
    
    /**
     * 发送隐身请求
     */
    private function sendStealthRequest($url, $engine, $extra_headers = array()) {
        $ch = curl_init();
        
        // 基础设置
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $this->config['timeout']);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $this->config['connect_timeout']);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate, br');
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_2_0);
        
        // Cookie管理
        $cookie_file = $this->getCookieFile($engine);
        curl_setopt($ch, CURLOPT_COOKIEJAR, $cookie_file);
        curl_setopt($ch, CURLOPT_COOKIEFILE, $cookie_file);
        
        // 构建高级请求头
        $headers = $this->buildStealthHeaders($url, $engine, $extra_headers);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        
        // 执行请求
        $data = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($this->config['debug']) {
            error_log("请求URL: " . $url);
            error_log("HTTP状态码: " . $http_code);
            error_log("cURL错误: " . $error);
        }
        
        if ($http_code == 200 && $data !== false) {
            return $data;
        }
        
        return false;
    }
    
    /**
     * 构建隐身请求头
     */
    private function buildStealthHeaders($url, $engine, $extra_headers = array()) {
        $parsed_url = parse_url($url);
        $host = $parsed_url['host'];
        
        // 最新的真实浏览器User-Agent
        $user_agents = array(
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        );
        
        $user_agent = $user_agents[array_rand($user_agents)];
        
        $headers = array(
            'Host: ' . $host,
            'User-Agent: ' . $user_agent,
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Accept-Encoding: gzip, deflate, br',
            'Connection: keep-alive',
            'Cache-Control: max-age=0',
            'sec-ch-ua: "Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile: ?0',
            'sec-ch-ua-platform: "Windows"'
        );
        
        // 添加额外头部
        foreach ($extra_headers as $key => $value) {
            if ($key == 'referer') {
                $headers[] = 'Referer: ' . $value;
            } elseif ($key == 'upgrade_insecure_requests') {
                $headers[] = 'Upgrade-Insecure-Requests: ' . $value;
            } elseif ($key == 'sec_fetch_dest') {
                $headers[] = 'Sec-Fetch-Dest: ' . $value;
            } elseif ($key == 'sec_fetch_mode') {
                $headers[] = 'Sec-Fetch-Mode: ' . $value;
            } elseif ($key == 'sec_fetch_site') {
                $headers[] = 'Sec-Fetch-Site: ' . $value;
            } elseif ($key == 'sec_fetch_user') {
                $headers[] = 'Sec-Fetch-User: ' . $value;
            }
        }
        
        return $headers;
    }
    
    /**
     * 高级百度结果解析
     */
    private function parseBaiduResultAdvanced($data) {
        // 多种解析方式
        $patterns = array(
            // 标准格式
            '/找到相关结果约\s*([0-9,，\s]+)\s*个/u',
            '/百度为您找到相关结果约\s*([0-9,，\s]+)\s*个/u',
            '/找到约\s*([0-9,，\s]+)\s*条结果/u',
            
            // JSON格式
            '/"results_nums"[^>]*>([0-9,，\s]+)</u',
            
            // 新版格式
            '/共找到\s*([0-9,，\s]+)\s*个结果/u',
            '/相关结果\s*([0-9,，\s]+)\s*个/u',
            
            // 移动端格式
            '/结果数约\s*([0-9,，\s]+)/u'
        );
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $data, $matches)) {
                $count = preg_replace('/[^0-9]/', '', $matches[1]);
                if ($count && intval($count) > 0) {
                    return intval($count);
                }
            }
        }
        
        return 0;
    }
    
    /**
     * 高级必应结果解析
     */
    private function parseBingResultAdvanced($data) {
        $patterns = array(
            '/([0-9,，\s]+)\s*results/i',
            '/约\s*([0-9,，\s]+)\s*个结果/u',
            '/of\s*([0-9,，\s]+)\s*results/i',
            '/"sb_count"[^>]*>([0-9,，\s]+)</u',
            '/共找到\s*([0-9,，\s]+)\s*个结果/u'
        );
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $data, $matches)) {
                $count = preg_replace('/[^0-9]/', '', $matches[1]);
                if ($count && intval($count) > 0) {
                    return intval($count);
                }
            }
        }
        
        return 0;
    }
    
    /**
     * 高级360结果解析
     */
    private function parse360ResultAdvanced($data) {
        $patterns = array(
            '/找到相关结果约\s*([0-9,，\s]+)\s*个/u',
            '/共找到\s*([0-9,，\s]+)\s*条结果/u',
            '/找到约\s*([0-9,，\s]+)\s*条结果/u',
            '/为您找到相关结果\s*([0-9,，\s]+)\s*个/u'
        );
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $data, $matches)) {
                $count = preg_replace('/[^0-9]/', '', $matches[1]);
                if ($count && intval($count) > 0) {
                    return intval($count);
                }
            }
        }
        
        return 0;
    }
    
    /**
     * 高级搜狗结果解析
     */
    private function parseSogouResultAdvanced($data) {
        $patterns = array(
            '/找到约\s*([0-9,，\s]+)\s*条结果/u',
            '/共\s*([0-9,，\s]+)\s*个结果/u',
            '/搜索结果约\s*([0-9,，\s]+)\s*条/u',
            '/为您找到约\s*([0-9,，\s]+)\s*条结果/u'
        );
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $data, $matches)) {
                $count = preg_replace('/[^0-9]/', '', $matches[1]);
                if ($count && intval($count) > 0) {
                    return intval($count);
                }
            }
        }
        
        return 0;
    }
    
    /**
     * 检查百度是否被拦截
     */
    private function isBlockedBaidu($data) {
        $blocked_keywords = array(
            '安全验证', '网络不给力', 'security', 'captcha', '百度安全验证', 
            '请输入验证码', '访问异常', '系统检测到异常流量', '请稍后再试'
        );
        
        foreach ($blocked_keywords as $keyword) {
            if (strpos($data, $keyword) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查必应是否被拦截
     */
    private function isBlockedBing($data) {
        $blocked_keywords = array(
            'blocked', 'captcha', 'unusual traffic', 'security check', 
            'verification', 'robot', 'automated'
        );
        
        foreach ($blocked_keywords as $keyword) {
            if (strpos($data, $keyword) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查360是否被拦截
     */
    private function isBlocked360($data) {
        $blocked_keywords = array(
            '访问异常', '安全验证', 'blocked', '验证码', 'captcha', '安全检查'
        );
        
        foreach ($blocked_keywords as $keyword) {
            if (strpos($data, $keyword) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查搜狗是否被拦截
     */
    private function isBlockedSogou($data) {
        $blocked_keywords = array(
            '验证码', '安全验证', 'captcha', '访问异常', 'blocked', '请输入验证码'
        );
        
        foreach ($blocked_keywords as $keyword) {
            if (strpos($data, $keyword) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 工具函数
     */
    private function cleanUrl($url) {
        $clean_url = preg_replace('/^https?:\/\//', '', $url);
        $clean_url = preg_replace('/^www\./', '', $clean_url);
        $clean_url = rtrim($clean_url, '/');
        return $clean_url;
    }
    
    private function generateBaiduPQ() {
        return substr(md5(uniqid(rand(), true)), 0, 16);
    }

    private function generateBaiduT() {
        return substr(md5(time() . rand()), 0, 4) . substr(md5(rand()), 0, 4);
    }

    private function generateBaiduIQID() {
        return '0x' . substr(md5(uniqid(rand(), true)), 0, 16);
    }

    private function generateBingRefig() {
        return substr(md5(uniqid(rand(), true)), 0, 32);
    }

    private function generateBingRdrig() {
        return strtoupper(substr(md5(uniqid(rand(), true)), 0, 32));
    }

    private function generateBingCVID() {
        return strtoupper(substr(md5(uniqid(rand(), true)), 0, 32));
    }

    private function generate360SSID() {
        return substr(md5(uniqid(rand(), true)), 0, 32);
    }

    private function generate360CP() {
        return substr(md5(time() . rand()), 0, 12);
    }

    private function generateSogouSessionTime() {
        return time() . '000'; // 毫秒时间戳
    }
    
    private function initCookieStorage() {
        $cookie_dir = '/tmp/search_cookies/';
        if (!is_dir($cookie_dir)) {
            @mkdir($cookie_dir, 0755, true);
        }
    }
    
    private function getCookieFile($engine) {
        return '/tmp/search_cookies/cookies_' . $engine . '_' . date('Ymd') . '.txt';
    }
    
    /**
     * 启用调试模式
     */
    public function enableDebug() {
        $this->config['debug'] = true;
    }
}

// 全局函数
function get_stealth_index_checker() {
    static $checker = null;
    if ($checker === null) {
        $checker = new StealthIndexChecker();
    }
    return $checker;
}

?>
