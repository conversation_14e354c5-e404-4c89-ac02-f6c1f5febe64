<?php
// 测试标题修复
define('IN_IWEBDIR', true);
define('APP_PATH', './source/');

require('./source/init.php');
require('./source/module/article.php');

echo "<h2>测试标题修复</h2>\n";

// 测试标题清理函数
$test_desc = "20分钟试听经典国语dj串烧，劲爆dj合集，DJ打碟现场 20分钟试听经典国语dj串烧，劲爆dj合集，DJ打碟现场，高速必听越听越上头#经典老歌#劲爆dj#中文dj#越听越上头#dj";

echo "<h3>1. 测试标题清理</h3>\n";
echo "<h4>原始标题:</h4>\n";
echo "<p>" . htmlspecialchars($test_desc) . "</p>\n";

$cleaned = clean_music_title($test_desc);
echo "<h4>清理后标题:</h4>\n";
echo "<p>'" . htmlspecialchars($cleaned) . "'</p>\n";
echo "<p>长度: " . mb_strlen($cleaned) . " 字符</p>\n";

// 测试JSON提取
$json_content = '{
    "id": 36685,
    "desc": "' . $test_desc . '",
    "music": "https://sf5-hl-cdn-tos.douyinstatic.com/obj/ies-music/7499955641297996554.mp3"
}';

echo "<h3>2. 测试JSON提取</h3>\n";
$music_data = extract_music_urls_with_titles($json_content);
echo "<h4>提取结果:</h4>\n";
if (!empty($music_data)) {
    foreach ($music_data as $i => $data) {
        echo "<p><strong>音乐 " . ($i + 1) . ":</strong><br>";
        echo "标题: '" . htmlspecialchars($data['title']) . "'<br>";
        echo "URL: " . htmlspecialchars($data['url']) . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>没有提取到数据</p>\n";
}

// 创建测试文章
echo "<h3>3. 创建测试文章</h3>\n";
$DB->query("DELETE FROM ".$DB->table('articles')." WHERE art_title='标题修复测试'");

$title = $DB->escape_string('标题修复测试');
$content = $DB->escape_string($json_content);

$sql = "INSERT INTO ".$DB->table('articles')." 
        (user_id, cate_id, art_title, art_tags, art_intro, art_content, art_status, art_ctime, art_utime) 
        VALUES 
        (1, 319, '$title', '测试', '测试', '$content', 3, ".time().", ".time().")";

if ($DB->query($sql)) {
    $art_id = $DB->insert_id();
    echo "<p style='color: green;'>测试文章创建成功 (ID: $art_id)</p>\n";
    
    // 测试get_music_links
    $music_links = get_music_links(319, 20);
    foreach ($music_links as $link) {
        if ($link['art_id'] == $art_id) {
            echo "<h4>get_music_links结果:</h4>\n";
            echo "<p><strong>显示标题:</strong> " . htmlspecialchars($link['title']) . "</p>\n";
            echo "<p><strong>提取标题:</strong> " . htmlspecialchars($link['extracted_title'] ?? '无') . "</p>\n";
            echo "<p><strong>文章标题:</strong> " . htmlspecialchars($link['original_title']) . "</p>\n";
            break;
        }
    }
} else {
    echo "<p style='color: red;'>创建文章失败</p>\n";
}

// 测试其他格式
echo "<h3>4. 测试其他格式</h3>\n";

$test_formats = array(
    '周杰伦 - 青花瓷：https://music.163.com/song/media/outer/url?id=185668',
    'https://example.com/music/unknown.mp3',
    '{"desc": "简单测试", "music": "https://example.com/test.mp3"}'
);

foreach ($test_formats as $i => $format) {
    echo "<h4>格式 " . ($i + 1) . ":</h4>\n";
    echo "<p>内容: " . htmlspecialchars($format) . "</p>\n";
    
    $result = extract_music_urls_with_titles($format);
    if (!empty($result)) {
        foreach ($result as $data) {
            echo "<p>提取标题: '" . htmlspecialchars($data['title']) . "'</p>\n";
        }
    } else {
        echo "<p>没有提取到数据</p>\n";
    }
    echo "<br>\n";
}

echo "<h3>5. 预期结果</h3>\n";
echo "<p>对于你的JSON数据，应该显示类似：</p>\n";
echo "<p><strong>20分钟试听经典国语dj串烧，劲爆dj合集，DJ打碟现场 [DJ串烧]</strong></p>\n";
echo "<p>而不是：标题修复测试 [DJ串烧]</p>\n";
?>
