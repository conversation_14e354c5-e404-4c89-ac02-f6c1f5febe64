<?php
// 调试标题提取问题
define('IN_IWEBDIR', true);
define('APP_PATH', './source/');

require('./source/init.php');
require('./source/module/article.php');

echo "<h2>调试标题提取问题</h2>\n";

// 测试JSON内容
$json_content = '{
    "id": 36685,
    "desc": "20分钟试听经典国语dj串烧，劲爆dj合集，DJ打碟现场",
    "music": "https://sf5-hl-cdn-tos.douyinstatic.com/obj/ies-music/7499955641297996554.mp3"
}';

echo "<h3>1. 测试extract_music_urls_with_titles函数</h3>\n";
echo "<h4>输入内容:</h4>\n";
echo "<pre>" . htmlspecialchars($json_content) . "</pre>\n";

$result = extract_music_urls_with_titles($json_content);
echo "<h4>函数返回结果:</h4>\n";
echo "<pre>" . print_r($result, true) . "</pre>\n";

// 测试JSON解析函数
echo "<h3>2. 测试extract_json_music_data函数</h3>\n";
$json_result = extract_json_music_data($json_content);
echo "<h4>JSON解析结果:</h4>\n";
echo "<pre>" . print_r($json_result, true) . "</pre>\n";

// 测试标题清理函数
echo "<h3>3. 测试clean_music_title函数</h3>\n";
$test_desc = "20分钟试听经典国语dj串烧，劲爆dj合集，DJ打碟现场 20分钟试听经典国语dj串烧，劲爆dj合集，DJ打碟现场，高速必听越听越上头#经典老歌#劲爆dj#中文dj#越听越上头#dj";
$cleaned_title = clean_music_title($test_desc);
echo "<h4>原始描述:</h4>\n";
echo "<p>" . htmlspecialchars($test_desc) . "</p>\n";
echo "<h4>清理后标题:</h4>\n";
echo "<p>" . htmlspecialchars($cleaned_title) . "</p>\n";

// 创建测试文章并查看实际数据库中的内容
echo "<h3>4. 创建测试文章并查看数据库内容</h3>\n";

$DB->query("DELETE FROM ".$DB->table('articles')." WHERE art_title='调试标题提取'");

$title = $DB->escape_string('调试标题提取');
$content = $DB->escape_string($json_content);

$sql = "INSERT INTO ".$DB->table('articles')." 
        (user_id, cate_id, art_title, art_tags, art_intro, art_content, art_status, art_ctime, art_utime) 
        VALUES 
        (1, 319, '$title', '调试,测试', '调试测试', '$content', 3, ".time().", ".time().")";

if ($DB->query($sql)) {
    $art_id = $DB->insert_id();
    echo "<p style='color: green;'>测试文章创建成功 (ID: $art_id)</p>\n";
    
    // 直接查询数据库看看文章内容
    $check_sql = "SELECT art_id, art_title, art_content FROM ".$DB->table('articles')." WHERE art_id=$art_id";
    $check_result = $DB->query($check_sql);
    $check_row = $DB->fetch_array($check_result);
    
    echo "<h4>数据库中的文章内容:</h4>\n";
    echo "<p><strong>文章ID:</strong> " . $check_row['art_id'] . "</p>\n";
    echo "<p><strong>文章标题:</strong> " . htmlspecialchars($check_row['art_title']) . "</p>\n";
    echo "<p><strong>文章内容:</strong></p>\n";
    echo "<pre>" . htmlspecialchars($check_row['art_content']) . "</pre>\n";
    
    // 测试对这个具体文章的处理
    echo "<h3>5. 测试对这个文章的音乐提取</h3>\n";
    $content_from_db = $check_row['art_content'];
    $music_data_from_db = extract_music_urls_with_titles($content_from_db);
    echo "<h4>从数据库内容提取的音乐数据:</h4>\n";
    echo "<pre>" . print_r($music_data_from_db, true) . "</pre>\n";
    
    // 模拟get_music_links函数的处理逻辑
    echo "<h3>6. 模拟get_music_links处理逻辑</h3>\n";
    if (!empty($music_data_from_db)) {
        foreach ($music_data_from_db as $i => $data) {
            $url = $data['url'];
            $extracted_title = $data['title'];
            
            echo "<p><strong>音乐 " . ($i + 1) . ":</strong></p>\n";
            echo "<p>URL: " . htmlspecialchars($url) . "</p>\n";
            echo "<p>提取的标题: '" . htmlspecialchars($extracted_title) . "'</p>\n";
            echo "<p>提取标题是否为空: " . (empty($extracted_title) ? '是' : '否') . "</p>\n";
            
            if (!empty($extracted_title)) {
                $final_title = $extracted_title;
                echo "<p style='color: green;'>使用提取的标题: " . htmlspecialchars($final_title) . "</p>\n";
            } else {
                $final_title = $check_row['art_title'];
                echo "<p style='color: red;'>使用文章标题: " . htmlspecialchars($final_title) . "</p>\n";
            }
            
            $final_title .= ' [DJ串烧]';
            echo "<p><strong>最终显示标题:</strong> " . htmlspecialchars($final_title) . "</p>\n";
            echo "<hr>\n";
        }
    }
    
    // 测试实际的get_music_links函数
    echo "<h3>7. 测试实际的get_music_links函数</h3>\n";
    $actual_links = get_music_links(319, 20);
    foreach ($actual_links as $link) {
        if ($link['art_id'] == $art_id) {
            echo "<p><strong>实际函数返回的标题:</strong> " . htmlspecialchars($link['title']) . "</p>\n";
            echo "<p><strong>提取的标题字段:</strong> " . htmlspecialchars($link['extracted_title'] ?? '无') . "</p>\n";
            echo "<p><strong>原始文章标题:</strong> " . htmlspecialchars($link['original_title']) . "</p>\n";
        }
    }
    
} else {
    echo "<p style='color: red;'>创建测试文章失败: " . $DB->error() . "</p>\n";
}
?>
