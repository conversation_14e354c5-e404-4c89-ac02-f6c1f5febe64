<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐播放器修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .loading-item { color: #666; font-style: italic; }
        .error-item { color: red; }
        .music-item { 
            padding: 10px; 
            border-bottom: 1px solid #eee; 
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .music-item:hover { background-color: #f5f5f5; }
        .music-item.active { background-color: #e3f2fd; }
        .track-title { font-weight: bold; }
        .track-url { font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <h1>音乐播放器修复测试</h1>
    
    <div class="test-section">
        <h2>1. 检查JavaScript函数</h2>
        <button onclick="checkFunctions()">检查函数是否存在</button>
        <div id="functionCheck"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 测试音乐列表API</h2>
        <button onclick="testMusicAPI()">测试音乐列表API</button>
        <div id="apiResult"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 模拟音乐播放器界面</h2>
        <div id="music-player-container">
            <div class="music-controls">
                <button id="prevBtn" title="上一首">⏮</button>
                <button id="playBtn" title="播放/暂停">▶</button>
                <button id="nextBtn" title="下一首">⏭</button>
                <button id="volumeBtn" title="静音/取消静音">🔊</button>
                <input type="range" id="volumeSlider" min="0" max="100" value="50">
                <button id="refreshList" title="刷新列表">🔄</button>
            </div>
            
            <div class="music-info">
                <div id="currentTitle">选择一首歌曲开始播放</div>
                <div class="music-progress">
                    <div class="progress-bar" style="width: 200px; height: 4px; background: #ddd; position: relative;">
                        <div id="progressFill" style="height: 100%; background: #007bff; width: 0%;"></div>
                    </div>
                    <div class="time-info">
                        <span id="currentTime">0:00</span> / <span id="totalTime">0:00</span>
                    </div>
                </div>
            </div>
            
            <div class="music-list">
                <div class="list-header">
                    <span>播放列表</span>
                </div>
                <ul id="musicList" style="list-style: none; padding: 0;">
                    <li class="loading-item">等待初始化...</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>4. 调试信息</h2>
        <div id="debugInfo"></div>
    </div>

    <!-- 引入jQuery和common.js -->
    <script src="public/scripts/jquery.min.js"></script>
    <script src="public/scripts/common.js"></script>
    
    <script>
        function checkFunctions() {
            const resultDiv = document.getElementById('functionCheck');
            const functions = [
                'initMusicPlayer',
                'loadMusicList', 
                'playTrack',
                'togglePlayPause',
                'playNext',
                'playPrevious'
            ];
            
            let html = '<h3>函数检查结果:</h3><ul>';
            functions.forEach(func => {
                const exists = typeof window[func] === 'function';
                html += `<li class="${exists ? 'success' : 'error'}">${func}: ${exists ? '✓ 存在' : '✗ 不存在'}</li>`;
            });
            html += '</ul>';
            
            resultDiv.innerHTML = html;
        }
        
        async function testMusicAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<div class="info">正在测试API...</div>';
            
            try {
                const response = await fetch('?mod=ajaxget&type=music_list');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">✓ API调用成功，获取到 ${data.count} 首音乐</div>`;
                    
                    if (data.music_list && data.music_list.length > 0) {
                        let html = '<h4>音乐列表预览:</h4><ul>';
                        data.music_list.slice(0, 3).forEach((track, index) => {
                            html += `<li>${index + 1}. ${track.title} - ${track.url}</li>`;
                        });
                        html += '</ul>';
                        resultDiv.innerHTML += html;
                    }
                } else {
                    resultDiv.innerHTML = `<div class="error">✗ API调用失败: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ 请求失败: ${error.message}</div>`;
            }
        }
        
        // 页面加载完成后自动检查
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试页面加载完成');
            
            // 延迟一点时间让common.js完全加载
            setTimeout(() => {
                checkFunctions();
                
                // 如果音乐播放器初始化函数存在，尝试初始化
                if (typeof initMusicPlayer === 'function') {
                    console.log('尝试初始化音乐播放器...');
                    initMusicPlayer();
                } else {
                    console.error('initMusicPlayer函数不存在');
                }
            }, 500);
        });
    </script>
</body>
</html>
