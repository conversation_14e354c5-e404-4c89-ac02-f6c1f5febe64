<?php
// 快速提交修复验证工具
define('IN_IWEBDIR', true);
require_once('config.php');
require_once('source/init.php');

echo "<h1>🎉 快速提交修复验证</h1>";

echo "<h2>🔧 修复内容</h2>";
echo "<ul>";
echo "<li>✅ <strong>邮件问题修复</strong>：简化邮件内容，避免复杂HTML导致的问题</li>";
echo "<li>✅ <strong>白屏问题修复</strong>：优化JavaScript输出，确保成功提示正常显示</li>";
echo "<li>✅ <strong>字符安全处理</strong>：对用户输入进行安全转义，避免JavaScript语法错误</li>";
echo "<li>✅ <strong>错误处理优化</strong>：邮件发送失败不影响主流程</li>";
echo "</ul>";

echo "<h2>📊 系统状态检查</h2>";

// 基础检查
$checks = array(
    '数据库连接' => (isset($DB) && is_object($DB)) ? '✅ 正常' : '❌ 异常',
    '快速提交模块' => file_exists('module/quicksubmit.php') ? '✅ 存在' : '❌ 不存在',
    '邮件发送函数' => file_exists('source/include/sendmail.php') ? '✅ 存在' : '❌ 不存在'
);

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'><th>检查项</th><th>状态</th></tr>";
foreach ($checks as $item => $status) {
    $color = (strpos($status, '✅') !== false) ? 'color: green;' : 'color: red;';
    echo "<tr><td><strong>{$item}</strong></td><td style='{$color}'>{$status}</td></tr>";
}
echo "</table>";

// 邮件配置检查
echo "<h2>📧 邮件配置状态</h2>";
$email_configs = array(
    'smtp_host' => isset($options['smtp_host']) ? $options['smtp_host'] : '',
    'smtp_port' => isset($options['smtp_port']) ? $options['smtp_port'] : '',
    'smtp_user' => isset($options['smtp_user']) ? $options['smtp_user'] : '',
    'smtp_pass' => isset($options['smtp_pass']) ? ($options['smtp_pass'] ? '已设置' : '未设置') : '未设置',
    'admin_email' => isset($options['admin_email']) ? $options['admin_email'] : ''
);

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'><th>配置项</th><th>值</th><th>状态</th></tr>";

$email_ready = true;
foreach ($email_configs as $key => $value) {
    $status = !empty($value) ? '✅ 已配置' : '❌ 未配置';
    $color = !empty($value) ? 'color: green;' : 'color: red;';
    if (empty($value)) $email_ready = false;
    
    echo "<tr>";
    echo "<td>{$key}</td>";
    echo "<td>" . ($key == 'smtp_pass' ? $value : htmlspecialchars($value)) . "</td>";
    echo "<td style='{$color}'>{$status}</td>";
    echo "</tr>";
}
echo "</table>";

if ($email_ready) {
    echo "<p style='color: green; font-weight: bold;'>✅ 邮件配置完整，应该能正常发送邮件通知</p>";
} else {
    echo "<p style='color: orange; font-weight: bold;'>⚠️ 邮件配置不完整，邮件通知可能失败（但不影响数据提交）</p>";
}

// 测试表单
echo "<h2>🧪 快速提交测试表单</h2>";

if (isset($DB) && is_object($DB)) {
    // 获取分类选项
    $category_option = '';
    try {
        $category_option = get_category_option('webdir', 0);
    } catch (Exception $e) {
        echo "<p style='color: red;'>获取分类失败: " . $e->getMessage() . "</p>";
    }
    
    // 生成验证码
    session_start();
    $_SESSION['code'] = 'test123';
    
    echo "<div style='border: 2px solid #28a745; padding: 20px; background: #f8f9fa; border-radius: 8px;'>";
    echo "<h3>🚀 修复后测试表单</h3>";
    echo "<p style='color: #666; margin-bottom: 15px;'><strong>预期结果：</strong>提交成功后显示成功提示弹窗，然后自动跳转到首页</p>";
    
    echo "<form method='post' action='?mod=quicksubmit' onsubmit='return confirm(\"确认提交测试数据？\");'>";
    echo "<table style='width: 100%;'>";
    echo "<tr><td style='padding: 8px; width: 120px;'><strong>选择分类：</strong></td><td style='padding: 8px;'><select name='cate_id' required style='padding: 5px; width: 200px;'><option value=''>请选择分类</option>{$category_option}</select> <span style='color: #f00;'>*</span></td></tr>";
    echo "<tr><td style='padding: 8px;'><strong>网站名称：</strong></td><td style='padding: 8px;'><input type='text' name='web_name' value='修复测试" . date('His') . "' required style='padding: 5px; width: 300px;'> <span style='color: #f00;'>*</span></td></tr>";
    echo "<tr><td style='padding: 8px;'><strong>网站域名：</strong></td><td style='padding: 8px;'><input type='text' name='web_url' value='fixed" . time() . ".com' required style='padding: 5px; width: 300px;'> <span style='color: #f00;'>*</span></td></tr>";
    echo "<tr><td style='padding: 8px;'><strong>网站标签：</strong></td><td style='padding: 8px;'><input type='text' name='web_tags' value='修复,测试,网站' style='padding: 5px; width: 300px;'></td></tr>";
    echo "<tr><td style='padding: 8px;'><strong>网站简介：</strong></td><td style='padding: 8px;'><textarea name='web_intro' rows='3' required style='padding: 5px; width: 300px;'>这是修复后的快速提交功能测试。修复了邮件发送问题和白屏问题，现在应该能正常显示成功提示并跳转。</textarea> <span style='color: #f00;'>*</span></td></tr>";
    echo "<tr><td style='padding: 8px;'><strong>网站所有者：</strong></td><td style='padding: 8px;'><input type='text' name='web_owner' value='修复测试用户' style='padding: 5px; width: 300px;'></td></tr>";
    echo "<tr><td style='padding: 8px;'><strong>电子邮箱：</strong></td><td style='padding: 8px;'><input type='email' name='web_email' value='<EMAIL>' required style='padding: 5px; width: 250px;'> <span style='color: #f00;'>*</span></td></tr>";
    echo "<tr><td style='padding: 8px;'><strong>验证码：</strong></td><td style='padding: 8px;'><input type='text' name='check_code' value='test123' required style='padding: 5px; width: 150px;'> <span style='color: #666;'>(测试验证码: test123)</span> <span style='color: #f00;'>*</span></td></tr>";
    echo "</table>";
    echo "<input type='hidden' name='act' value='submit'>";
    echo "<br><input type='submit' value='🚀 测试修复效果' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; font-weight: bold;'>";
    echo "</form>";
    echo "</div>";
} else {
    echo "<p style='color: red;'>数据库连接失败，无法显示测试表单</p>";
}

echo "<h2>🔗 测试链接</h2>";
echo "<p>";
echo "<a href='?mod=quicksubmit' target='_blank' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📝 访问快速提交页面</a>";
echo "<a href='system/' target='_blank' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔧 查看后台管理</a>";
echo "<a href='database_debug.php' target='_blank' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔍 数据库调试</a>";
echo "</p>";

echo "<h2>📋 测试步骤</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #007cba;'>";
echo "<ol>";
echo "<li><strong>确认系统状态</strong> - 上面的检查项都应该正常</li>";
echo "<li><strong>选择分类</strong> - 从下拉菜单选择一个网站分类</li>";
echo "<li><strong>检查表单数据</strong> - 确认所有必填项都已填写</li>";
echo "<li><strong>确认验证码</strong> - 验证码为 test123</li>";
echo "<li><strong>提交测试</strong> - 点击'🚀 测试修复效果'按钮</li>";
echo "<li><strong>观察结果</strong> - 应该显示成功提示弹窗，然后自动跳转到首页</li>";
echo "<li><strong>检查后台</strong> - 访问后台查看是否有新的待审核记录</li>";
echo "<li><strong>检查邮件</strong> - 查看管理员邮箱是否收到通知邮件</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🎯 修复后的预期结果</h2>";
echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745;'>";
echo "<ul>";
echo "<li>✅ <strong>不再白屏</strong> - 提交后正常显示成功提示</li>";
echo "<li>✅ <strong>成功提示</strong> - 显示包含网站信息的成功提示弹窗</li>";
echo "<li>✅ <strong>自动跳转</strong> - 提示后自动跳转到首页</li>";
echo "<li>✅ <strong>数据入库</strong> - 数据成功插入数据库</li>";
echo "<li>✅ <strong>后台记录</strong> - 后台出现新的待审核记录</li>";
echo "<li>✅ <strong>邮件通知</strong> - 管理员收到简化的邮件通知（如果邮件配置正确）</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🔍 如果仍有问题</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107;'>";
echo "<ul>";
echo "<li><strong>白屏问题</strong> - 检查浏览器开发者工具的控制台错误</li>";
echo "<li><strong>邮件问题</strong> - 查看 data/email_debug.log 和 data/email_error.log</li>";
echo "<li><strong>数据库问题</strong> - 使用数据库调试工具检查连接</li>";
echo "<li><strong>验证码问题</strong> - 确认输入 test123</li>";
echo "<li><strong>服务器错误</strong> - 查看服务器PHP错误日志</li>";
echo "</ul>";
echo "</div>";

// 显示最近提交记录
if (isset($DB) && is_object($DB)) {
    echo "<h2>📊 最近提交记录</h2>";
    try {
        $recent = $DB->query("SELECT web_id, web_name, web_url, web_status, web_ctime, user_id FROM " . $DB->table('websites') . " ORDER BY web_ctime DESC LIMIT 5");
        if ($DB->num_rows($recent)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f8f9fa;'><th>ID</th><th>网站名称</th><th>域名</th><th>状态</th><th>类型</th><th>提交时间</th></tr>";
            while ($row = $DB->fetch_array($recent)) {
                $status_names = array(1 => '拉黑', 2 => '待审核', 3 => '已收录', 4 => '不通过');
                $status = isset($status_names[$row['web_status']]) ? $status_names[$row['web_status']] : '未知';
                $user_type = ($row['user_id'] == 0) ? '<span style="color: #28a745;">快速提交</span>' : '<span style="color: #007cba;">会员提交</span>';
                
                echo "<tr>";
                echo "<td>{$row['web_id']}</td>";
                echo "<td>" . htmlspecialchars($row['web_name']) . "</td>";
                echo "<td>" . htmlspecialchars($row['web_url']) . "</td>";
                echo "<td>{$status}</td>";
                echo "<td>{$user_type}</td>";
                echo "<td>" . date('m-d H:i', $row['web_ctime']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>暂无提交记录</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>获取记录失败: " . $e->getMessage() . "</p>";
    }
}

// 显示邮件日志
echo "<h2>📄 邮件调试日志（最近5条）</h2>";
$log_file = "data/email_debug.log";
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    $recent_lines = array_slice(array_filter($lines), -5);
    
    if (!empty($recent_lines)) {
        echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #ddd; max-height: 200px; overflow-y: auto;'>";
        echo htmlspecialchars(implode("\n", $recent_lines));
        echo "</pre>";
    } else {
        echo "<p>邮件调试日志为空</p>";
    }
} else {
    echo "<p>邮件调试日志文件不存在</p>";
}
?>
